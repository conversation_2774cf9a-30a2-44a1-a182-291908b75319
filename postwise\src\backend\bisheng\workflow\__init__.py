# 主服务 bisheng-main 一直无法连接到 celery 问题的根源，在于程序有两个不同的 Celery 应用实例:
# bisheng.worker.main.py 中定义的 bisheng_celery
# bisheng.core.celery_app.py 中定义的 celery_app
# 主服务试图从 bisheng.worker 导入 celery_app，但是 worker/__init__.py 中没有导出这个变量。
####################### 不要推到线上 ################################
def __getattr__(name):
    if name == 'celery_app':
        from bisheng.worker.main import bisheng_celery
        return bisheng_celery
    raise AttributeError(f"module {__name__} has no attribute {name}")
