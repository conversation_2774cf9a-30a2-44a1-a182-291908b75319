[2025-08-22 09:42:11.156132] [INFO process-33448-31480 datasets.config:58] - trace=1 PyTorch version 2.8.0 available.
[2025-08-22 09:42:14.511144] [INFO process-33448-31480 bisheng.interface.utils:92] - trace=1 LLM caching setup with InMemoryCache
[2025-08-22 15:00:54.664379] [INFO process-12860-26940 datasets.config:58] - trace=1 PyTorch version 2.8.0 available.
[2025-08-22 15:00:58.645999] [INFO process-12860-26940 bisheng.interface.utils:92] - trace=1 LLM caching setup with InMemoryCache
[2025-08-22 15:01:00.150915] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=dbfd7ff281314b89a93517e8749d7303 GET /api/v1/all
[2025-08-22 15:01:00.157343] [INFO process-12860-27644 bisheng.interface.custom.utils:343] - trace=dbfd7ff281314b89a93517e8749d7303 Building custom components from ['D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\components']
[2025-08-22 15:01:00.224447] [INFO process-12860-27644 bisheng.interface.custom.utils:354] - trace=dbfd7ff281314b89a93517e8749d7303 Loading 1 component(s) from category custom_components
[2025-08-22 15:01:00.709886] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=dbfd7ff281314b89a93517e8749d7303 GET /api/v1/all 200 timecost=558.971
[2025-08-22 15:01:03.785928] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=af46f2db64a64d519c06a8ecd267f0e2 GET /api/v1/user/public_key
[2025-08-22 15:01:03.949855] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=af46f2db64a64d519c06a8ecd267f0e2 GET /api/v1/user/public_key 200 timecost=163.927
[2025-08-22 15:01:04.051972] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=81c035d259374a2c86ae5fa2556f837e POST /api/v1/user/login
[2025-08-22 15:01:04.130845] [INFO process-12860-26940 bisheng.api.services.audit_log:399] - trace=81c035d259374a2c86ae5fa2556f837e act=user_login user=<EMAIL> ip=127.0.0.1 user_id=1
[2025-08-22 15:01:04.158958] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=81c035d259374a2c86ae5fa2556f837e POST /api/v1/user/login 200 timecost=106.986
[2025-08-22 15:01:04.271861] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=cf6f12d7baf24008a4277165c78c9cdb GET /api/v1/user/get_captcha
[2025-08-22 15:01:04.307438] [INFO process-12860-26940 bisheng.api.v1.user:673] - trace=cf6f12d7baf24008a4277165c78c9cdb get_captcha captcha_char=lKAQ
[2025-08-22 15:01:04.335002] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=cf6f12d7baf24008a4277165c78c9cdb GET /api/v1/user/get_captcha 200 timecost=63.141
[2025-08-22 15:01:04.347843] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=5376c152df7845a2bd89bb7a540289a5 GET /api/v1/web/config
[2025-08-22 15:01:04.362359] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=5376c152df7845a2bd89bb7a540289a5 GET /api/v1/web/config 200 timecost=14.477
[2025-08-22 15:01:05.944684] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=7ecd62771c7544fb82a07326b9ec3b41 GET /api/v1/user/info
[2025-08-22 15:01:05.954109] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=fea16f0660964aefa226a6951414bec7 GET /api/v1/env
[2025-08-22 15:01:05.962152] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=78346aac2acb4075afb67e9efca525ae GET /api/v1/all
[2025-08-22 15:01:05.998408] [INFO process-12860-23260 bisheng.interface.custom.utils:343] - trace=78346aac2acb4075afb67e9efca525ae Building custom components from ['D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\components']
[2025-08-22 15:01:06.242639] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=7ecd62771c7544fb82a07326b9ec3b41 GET /api/v1/user/info 200 timecost=297.955
[2025-08-22 15:01:06.256796] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=fea16f0660964aefa226a6951414bec7 GET /api/v1/env 200 timecost=302.687
[2025-08-22 15:01:06.276801] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=08d06136b402420494a6adde75d6226f GET /api/v1/component
[2025-08-22 15:01:06.310681] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=7de3c7c6f5ed4a81a22f2f460ff195ee GET /api/v1/tag
[2025-08-22 15:01:06.359960] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=e5e4a96a183945bc8f2226a58bb0fffe GET /api/v1/tag/home
[2025-08-22 15:01:06.802825] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=08d06136b402420494a6adde75d6226f GET /api/v1/component 200 timecost=526.024
[2025-08-22 15:01:06.802825] [INFO process-12860-23260 bisheng.interface.custom.utils:354] - trace=78346aac2acb4075afb67e9efca525ae Loading 1 component(s) from category custom_components
[2025-08-22 15:01:06.819387] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=e5e4a96a183945bc8f2226a58bb0fffe GET /api/v1/tag/home 200 timecost=459.427
[2025-08-22 15:01:06.827909] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=7de3c7c6f5ed4a81a22f2f460ff195ee GET /api/v1/tag 200 timecost=517.228
[2025-08-22 15:01:06.836147] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=d2d5f3e4c8954a7c9458a27959d3e5b6 GET /api/v1/workstation/config
[2025-08-22 15:01:07.292102] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=c8402e1dc08b4e06bfeee1bb7411d3c5 GET /api/v1/chat/list
[2025-08-22 15:01:07.298273] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=78346aac2acb4075afb67e9efca525ae GET /api/v1/all 200 timecost=1336.121
[2025-08-22 15:01:07.321568] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=162b0ae8f7b04171b728ef470192f6f3 GET /api/v1/tag
[2025-08-22 15:01:07.328204] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=de60a8078f4a4fb8bfaa4bc95c67675e GET /api/v1/chat/online
[2025-08-22 15:01:07.438248] [ERROR process-12860-13604 __main__:33] - trace=1 GET http://127.0.0.1:7860/api/v1/workstation/config 'NoneType' object has no attribute 'model_dump'
[2025-08-22 15:01:07.686289] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=162b0ae8f7b04171b728ef470192f6f3 GET /api/v1/tag 200 timecost=364.72
[2025-08-22 15:01:07.695839] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=c8402e1dc08b4e06bfeee1bb7411d3c5 GET /api/v1/chat/list 200 timecost=404.753
[2025-08-22 15:01:07.703214] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=de60a8078f4a4fb8bfaa4bc95c67675e GET /api/v1/chat/online 200 timecost=375.01
[2025-08-22 15:01:08.347259] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=8bdce3175a724735908d1341ca3c3a6c GET /api/v1/chat/online
[2025-08-22 15:01:08.397393] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=8bdce3175a724735908d1341ca3c3a6c GET /api/v1/chat/online 200 timecost=50.134
[2025-08-22 15:02:12.714857] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=d4aa65e465864060984f7e0d36f817b3 GET /api/v1/skill/template
[2025-08-22 15:02:12.758668] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=d4aa65e465864060984f7e0d36f817b3 GET /api/v1/skill/template 200 timecost=43.811
[2025-08-22 15:02:12.839408] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=65ff620714e4424ca6284612b1ac1fca GET /api/v1/tag
[2025-08-22 15:02:12.887819] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=65ff620714e4424ca6284612b1ac1fca GET /api/v1/tag 200 timecost=48.411
[2025-08-22 15:02:13.126872] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=8e42c341516e4e0385b65eee01018895 GET /api/v1/workflow/list
[2025-08-22 15:02:13.191848] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=8e42c341516e4e0385b65eee01018895 GET /api/v1/workflow/list 200 timecost=64.976
[2025-08-22 15:02:13.525719] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=2c78c0773aac4ec7b2a7da27f00501b1 GET /api/v1/skill/template
[2025-08-22 15:02:13.565451] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=2c78c0773aac4ec7b2a7da27f00501b1 GET /api/v1/skill/template 200 timecost=39.732
[2025-08-22 15:02:14.928128] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=5490e2ad3f094d5d975588ab90b7946c GET /api/v1/flows/ac8b23aed25947908ac042dc5b5f1594
[2025-08-22 15:02:14.971685] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=5490e2ad3f094d5d975588ab90b7946c GET /api/v1/flows/ac8b23aed25947908ac042dc5b5f1594 200 timecost=43.557
[2025-08-22 15:02:15.234773] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=38b32b76338c45ebb33ac60b89302c56 GET /api/v1/workflow/versions
[2025-08-22 15:02:15.263031] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=38b32b76338c45ebb33ac60b89302c56 GET /api/v1/workflow/versions 200 timecost=28.259
[2025-08-22 15:02:15.538400] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=53393c7607ae4e8ab708236b89d871ff GET /api/v1/assistant/tool_list
[2025-08-22 15:02:15.549104] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=9f2f1eedd39c47bd9e38577220aae9e0 GET /api/v1/llm/assistant/llm_list
[2025-08-22 15:02:15.556108] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=5f730c81978841cf85cdadaa3ad8fb34 GET /api/v1/knowledge
[2025-08-22 15:02:15.562108] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=8dd365866b5a4bca83f6ff17a6d905c9 GET /api/v1/assistant/tool_list
[2025-08-22 15:02:15.686811] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=9f2f1eedd39c47bd9e38577220aae9e0 GET /api/v1/llm/assistant/llm_list 200 timecost=137.707
[2025-08-22 15:02:15.755538] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=5f730c81978841cf85cdadaa3ad8fb34 GET /api/v1/knowledge 200 timecost=199.431
[2025-08-22 15:02:15.820107] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=8dd365866b5a4bca83f6ff17a6d905c9 GET /api/v1/assistant/tool_list 200 timecost=257.999
[2025-08-22 15:02:15.826106] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=53393c7607ae4e8ab708236b89d871ff GET /api/v1/assistant/tool_list 200 timecost=287.705
[2025-08-22 15:03:16.308282] [INFO process-12860-26392 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:03:16.319786] [INFO process-12860-26940 bisheng.chat.clients.base:68] - trace=1 client_id=415d4554a76c4c9bbcd20721e6891442 trace_id=e9e5c2ed9c1f472195040306bc8c3698 message={'action': 'init_data', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'data': {'description': '支持添加知识库和工具', 'status': 2, 'user_id': 1, 'guide_word': None, 'create_time': '2025-08-21T10:54:24', 'name': '多轮对话-479e3', 'logo': '', 'flow_type': 10, 'update_time': '2025-08-21T10:58:11', 'id': 'ac8b23aed25947908ac042dc5b5f1594', 'nodes': [{'id': 'start_553b9', 'data': {'v': 1, 'id': 'start_553b9', 'name': '开始', 'type': 'start', 'description': '工作流运行的起始节点。', 'group_params': [{'name': '开场引导', 'params': [{'key': 'guide_word', 'type': 'textarea', 'label': '开场白', 'value': '您好，请问想聊些什么呢？', 'placeholder': '每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。'}, {'key': 'guide_question', 'help': '为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', 'type': 'input_list', 'label': '引导问题', 'value': ['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], 'placeholder': '请输入引导问题'}]}, {'name': '全局变量', 'params': [{'key': 'current_time', 'type': 'var', 'label': '当前时间', 'value': '', 'global': 'key'}, {'key': 'chat_history', 'type': 'chat_history_num', 'value': 10, 'global': 'key'}, {'key': 'preset_question', 'help': '适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', 'type': 'input_list', 'label': '预置问题列表', 'value': [{'key': 'f4cbae', 'value': ''}], 'global': 'item:input_list', 'placeholder': '输入批量预置问题'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 702}, 'position': {'x': 491, 'y': 63.125}, 'selected': False}, {'id': 'input_2775b', 'data': {'v': 2, 'id': 'input_2775b', 'tab': {'value': 'dialog_input', 'options': [{'key': 'dialog_input', 'help': '接收用户从对话框输入的内容。!(input)', 'label': '对话框输入'}, {'key': 'form_input', 'help': '将会在用户会话界面弹出一个表单，接收用户从表单提交的内容。!(form)', 'label': '表单输入'}]}, 'name': '输入', 'type': 'input', 'expand': False, 'description': '接收用户在会话页面的输入，支持 2 种形式：对话框输入，表单输入。', 'group_params': [{'name': '', 'params': [{'key': 'user_input', 'tab': 'dialog_input', 'type': 'var', 'label': '用户输入内容', 'global': 'key'}, {'key': 'form_input', 'tab': 'form_input', 'type': 'form', 'label': '+ 添加表单项', 'value': [], 'global': 'item:form_input'}, {'key': 'dialog_files_content', 'tab': 'dialog_input', 'type': 'var', 'label': '上传文件内容', 'global': 'key'}, {'key': 'dialog_files_content_size', 'min': 0, 'tab': 'dialog_input', 'type': 'char_number', 'label': '文件内容长度上限', 'value': 15000}, {'key': 'dialog_file_accept', 'tab': 'dialog_input', 'type': 'select_fileaccept', 'label': '上传文件类型', 'value': 'all'}, {'key': 'dialog_image_files', 'tab': 'dialog_input', 'help': '提取上传文件中的图片文件，当助手或大模型节点使用多模态大模型时，可传入此图片。', 'type': 'var', 'label': '上传图片文件', 'global': 'key'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 149}, 'position': {'x': 947, 'y': 85}, 'selected': False}, {'id': 'agent_1d0c8', 'data': {'v': 2, 'id': 'agent_1d0c8', 'tab': {'value': 'single', 'options': [{'key': 'single', 'label': '单次运行'}, {'key': 'batch', 'label': '批量运行'}]}, 'name': '多轮对话助手', 'type': 'agent', 'expand': True, 'description': 'AI 自主进行任务规划，选择合适的知识库或工具进行调用。', 'group_params': [{'params': [{'key': 'batch_variable', 'tab': 'batch', 'help': '选择需要批处理的变量，将会多次运行本节点，每次运行时从选择的变量中取一项赋值给batch_variable进行处理。', 'type': 'user_question', 'label': '批处理变量', 'value': [], 'global': 'self', 'linkage': 'output', 'required': True, 'placeholder': '请选择批处理变量'}]}, {'name': '模型设置', 'params': [{'key': 'model_id', 'type': 'agent_model', 'label': '模型', 'value': 2, 'required': True, 'placeholder': '请选择模型'}, {'key': 'temperature', 'step': 0.1, 'type': 'slide', 'label': '温度', 'scope': [0, 2], 'value': 0.7}]}, {'name': '提示词', 'params': [{'key': 'system_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '系统提示词', 'value': '你是一个熟悉各行各业的 AI 助理，善于根据用户需求，适时调用合适的工具来回答用户问题', 'required': True, 'placeholder': '助手画像'}, {'key': 'user_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '用户提示词', 'value': '{{#input_2775b.user_input#}}', 'varZh': {'input_2775b.user_input': '输入/user_input'}, 'required': True, 'placeholder': '用户消息内容'}, {'key': 'chat_history_flag', 'help': '带入模型上下文的历史消息条数，为 0 时代表不包含上下文信息。', 'step': 1, 'type': 'slide_switch', 'label': '历史聊天记录', 'scope': [0, 100], 'value': {'flag': True, 'value': 50}}, {'key': 'image_prompt', 'help': '当使用多模态大模型时，可通过此功能传入图片，结合图像内容进行问答', 'type': 'image_prompt', 'label': '视觉', 'value': ''}]}, {'name': '知识库', 'params': [{'key': 'knowledge_id', 'type': 'knowledge_select_multi', 'label': '检索知识库范围', 'value': {'type': 'knowledge', 'value': []}, 'placeholder': '请选择知识库'}]}, {'name': '数据库', 'params': [{'key': 'sql_agent', 'type': 'sql_config', 'value': {'open': False, 'db_name': '', 'db_address': '', 'db_password': '', 'db_username': ''}}]}, {'name': '工具', 'params': [{'key': 'tool_list', 'type': 'add_tool', 'label': '+ 添加工具', 'value': []}]}, {'name': '输出', 'params': [{'key': 'output_user', 'help': '一般在问答等场景开启，文档审核、报告生成等场景可关闭。', 'type': 'switch', 'label': '将输出结果展示在会话中', 'value': True}, {'key': 'output', 'type': 'var', 'label': '输出变量', 'value': [], 'global': 'code:value.map(el => ({ label: el.label, value: el.key }))'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 1202}, 'position': {'x': 1416.1256404922672, 'y': 89.69476666183581}, 'selected': True}], 'edges': [{'id': 'xy-edge__start_553b9right_handle-input_2775bleft_handle', 'type': 'customEdge', 'source': 'start_553b9', 'target': 'input_2775b', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__input_2775bright_handle-agent_1d0c8left_handle', 'type': 'customEdge', 'source': 'input_2775b', 'target': 'agent_1d0c8', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__agent_1d0c8right_handle-input_2775bleft_handle', 'type': 'customEdge', 'source': 'agent_1d0c8', 'target': 'input_2775b', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}], 'viewport': {'x': 202.67642774879823, 'y': -1.8593657130471684, 'zoom': 0.7423265855532226}}}
[2025-08-22 15:03:16.477587] [INFO process-12860-9920 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:03:17.363595] [INFO process-12860-26392 bisheng.utils.threadpool:67] - trace=2 async_task_added fun=consume_message args=
[2025-08-22 15:03:17.488569] [INFO process-12860-9920 bisheng.utils.threadpool:67] - trace=e9e5c2ed9c1f472195040306bc8c3698 async_task_added fun=wrapper_task args=e9e5c2ed9c1f472195040306bc8c3698
[2025-08-22 15:03:34.908629] [INFO process-12860-26940 bisheng.chat.clients.base:68] - trace=1 client_id=415d4554a76c4c9bbcd20721e6891442 trace_id=1e1591be54df40dbafc885c39b0cfca8 message={'action': 'input', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'data': {'input_2775b': {'data': {'user_input': '你好，请问你知道什么？', 'dialog_files_content': []}, 'message': '你好，请问你知道什么？', 'message_id': '769ae4bb30284029a7165fb617e020bb', 'category': 'question', 'extra': '', 'source': 0}}}
[2025-08-22 15:03:34.927976] [INFO process-12860-26392 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:03:34.943012] [INFO process-12860-28992 bisheng.chat.clients.workflow_client:182] - trace=1e1591be54df40dbafc885c39b0cfca8 get user input: {'input_2775b': {'data': {'user_input': '你好，请问你知道什么？', 'dialog_files_content': []}, 'message': '你好，请问你知道什么？', 'message_id': '769ae4bb30284029a7165fb617e020bb', 'category': 'question', 'extra': '', 'source': 0}}
[2025-08-22 15:03:35.943161] [INFO process-12860-26392 bisheng.utils.threadpool:67] - trace=1e1591be54df40dbafc885c39b0cfca8 async_task_added fun=wrapper_task args=1e1591be54df40dbafc885c39b0cfca8
[2025-08-22 15:03:37.428741] [ERROR process-12860-26940 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=13>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=13>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000002719487F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 65132, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000002719487E050>
              └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000002719487E0E0>
           └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000002719425EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x00000271D46CC310>
    │       └ <function run at 0x00000271D393D090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000271D393CAF0>
    └ <uvicorn.server.Server object at 0x00000271DB765720>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000271D393CB80>
           │       │   └ <uvicorn.server.Server object at 0x00000271DB765720>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000271948C16C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000271948C2170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000271948C2200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002719477FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=13>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=13>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=13>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=13>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=79>
           │    └ <_ProactorSocketTransport fd=8124 read=<_OverlappedFuture pending overlapped=<pending, 0x271dcbfcd30> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=13>

AssertionError: assert f is self._write_fut
[2025-08-22 15:03:38.349482] [ERROR process-12860-26940 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000002719487F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 65132, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000002719487E050>
              └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000002719487E0E0>
           └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000002719425EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x00000271D46CC310>
    │       └ <function run at 0x00000271D393D090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000271D393CAF0>
    └ <uvicorn.server.Server object at 0x00000271DB765720>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000271D393CB80>
           │       │   └ <uvicorn.server.Server object at 0x00000271DB765720>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000271948C16C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000271948C2170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000271948C2200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002719477FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=10>
           │    └ <_ProactorSocketTransport fd=8124 read=<_OverlappedFuture pending overlapped=<pending, 0x271dcbfcd30> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=10>

AssertionError: assert f is self._write_fut
[2025-08-22 15:03:38.927548] [ERROR process-12860-26940 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=55>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=55>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000002719487F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 65132, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000002719487E050>
              └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000002719487E0E0>
           └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000002719425EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x00000271D46CC310>
    │       └ <function run at 0x00000271D393D090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000271D393CAF0>
    └ <uvicorn.server.Server object at 0x00000271DB765720>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000271D393CB80>
           │       │   └ <uvicorn.server.Server object at 0x00000271DB765720>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000271948C16C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000271948C2170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000271948C2200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002719477FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=55>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=55>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=55>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=55>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=10>
           │    └ <_ProactorSocketTransport fd=8124 read=<_OverlappedFuture pending overlapped=<pending, 0x271dcbfcd30> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=55>

AssertionError: assert f is self._write_fut
[2025-08-22 15:03:40.751464] [ERROR process-12860-26940 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000002719487F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 65132, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000002719487E050>
              └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000002719487E0E0>
           └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000002719425EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x00000271D46CC310>
    │       └ <function run at 0x00000271D393D090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000271D393CAF0>
    └ <uvicorn.server.Server object at 0x00000271DB765720>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000271D393CB80>
           │       │   └ <uvicorn.server.Server object at 0x00000271DB765720>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000271948C16C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000271948C2170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000271948C2200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002719477FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=40>
           │    └ <_ProactorSocketTransport fd=8124 read=<_OverlappedFuture pending overlapped=<pending, 0x271dcbfcd30> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=10>

AssertionError: assert f is self._write_fut
[2025-08-22 15:03:45.196393] [INFO process-12860-26940 bisheng.chat.clients.base:68] - trace=1 client_id=415d4554a76c4c9bbcd20721e6891442 trace_id=425484a71d614dc39b03c4199a26abc1 message={'action': 'input', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'data': {'input_2775b': {'data': {'user_input': '最近关于低空经济的政策', 'dialog_files_content': []}, 'message': '最近关于低空经济的政策', 'message_id': '5f79cf4f09eb4a13a3b35ce9413db882', 'category': 'question', 'extra': '', 'source': 0}}}
[2025-08-22 15:03:45.210639] [INFO process-12860-27576 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:03:45.222723] [INFO process-12860-27256 bisheng.chat.clients.workflow_client:182] - trace=425484a71d614dc39b03c4199a26abc1 get user input: {'input_2775b': {'data': {'user_input': '最近关于低空经济的政策', 'dialog_files_content': []}, 'message': '最近关于低空经济的政策', 'message_id': '5f79cf4f09eb4a13a3b35ce9413db882', 'category': 'question', 'extra': '', 'source': 0}}
[2025-08-22 15:03:46.225970] [INFO process-12860-27576 bisheng.utils.threadpool:67] - trace=425484a71d614dc39b03c4199a26abc1 async_task_added fun=wrapper_task args=425484a71d614dc39b03c4199a26abc1
[2025-08-22 15:03:46.540277] [ERROR process-12860-26940 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=12>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=12>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000002719487F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 65132, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000002719487E050>
              └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000002719487E0E0>
           └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000002719425EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x00000271D46CC310>
    │       └ <function run at 0x00000271D393D090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000271D393CAF0>
    └ <uvicorn.server.Server object at 0x00000271DB765720>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000271D393CB80>
           │       │   └ <uvicorn.server.Server object at 0x00000271DB765720>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000271948C16C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000271948C2170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000271948C2200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002719477FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=12>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=12>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=12>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=12>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=59>
           │    └ <_ProactorSocketTransport fd=8124 read=<_OverlappedFuture pending overlapped=<pending, 0x271dcc465f0> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=12>

AssertionError: assert f is self._write_fut
[2025-08-22 15:03:50.930193] [ERROR process-12860-26940 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=36>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=36>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000002719487F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 65132, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000002719487E050>
              └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000002719487E0E0>
           └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000002719425EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x00000271D46CC310>
    │       └ <function run at 0x00000271D393D090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000271D393CAF0>
    └ <uvicorn.server.Server object at 0x00000271DB765720>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000271D393CB80>
           │       │   └ <uvicorn.server.Server object at 0x00000271DB765720>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000271948C16C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000271948C2170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000271948C2200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002719477FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=36>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=36>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=36>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=36>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=10>
           │    └ <_ProactorSocketTransport fd=8124 read=<_OverlappedFuture pending overlapped=<pending, 0x271dcc465f0> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=36>

AssertionError: assert f is self._write_fut
[2025-08-22 15:03:51.288270] [ERROR process-12860-26940 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=13>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=13>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000002719487F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 65132, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000002719487E050>
              └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000002719487E0E0>
           └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000002719425EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x00000271D46CC310>
    │       └ <function run at 0x00000271D393D090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000271D393CAF0>
    └ <uvicorn.server.Server object at 0x00000271DB765720>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000271D393CB80>
           │       │   └ <uvicorn.server.Server object at 0x00000271DB765720>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000271948C16C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000271948C2170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000271948C2200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002719477FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=13>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=13>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=13>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=13>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=14>
           │    └ <_ProactorSocketTransport fd=8124 read=<_OverlappedFuture pending overlapped=<pending, 0x271dcc465f0> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=13>

AssertionError: assert f is self._write_fut
[2025-08-22 15:03:51.803204] [ERROR process-12860-26940 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=71>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=71>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000002719487F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 65132, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000002719487E050>
              └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000002719487E0E0>
           └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000002719425EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x00000271D46CC310>
    │       └ <function run at 0x00000271D393D090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000271D393CAF0>
    └ <uvicorn.server.Server object at 0x00000271DB765720>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000271D393CB80>
           │       │   └ <uvicorn.server.Server object at 0x00000271DB765720>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000271948C16C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000271948C2170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000271948C2200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002719477FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=71>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=71>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=71>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=71>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=10>
           │    └ <_ProactorSocketTransport fd=8124 read=<_OverlappedFuture pending overlapped=<pending, 0x271dcc465f0> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=71>

AssertionError: assert f is self._write_fut
[2025-08-22 15:03:52.551451] [ERROR process-12860-26940 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=20>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=20>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000002719487F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 65132, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000002719487E050>
              └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000002719487E0E0>
           └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000002719425EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x00000271D46CC310>
    │       └ <function run at 0x00000271D393D090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000271D393CAF0>
    └ <uvicorn.server.Server object at 0x00000271DB765720>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000271D393CB80>
           │       │   └ <uvicorn.server.Server object at 0x00000271DB765720>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000271948C16C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000271948C2170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000271948C2200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002719477FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=20>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=20>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=20>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=20>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=10>
           │    └ <_ProactorSocketTransport fd=8124 read=<_OverlappedFuture pending overlapped=<pending, 0x271dcc465f0> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=20>

AssertionError: assert f is self._write_fut
[2025-08-22 15:03:56.309316] [ERROR process-12860-26940 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=16>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=16>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000002719487F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 65132, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000002719487E050>
              └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000002719487E0E0>
           └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000002719425EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x00000271D46CC310>
    │       └ <function run at 0x00000271D393D090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000271D393CAF0>
    └ <uvicorn.server.Server object at 0x00000271DB765720>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000271D393CB80>
           │       │   └ <uvicorn.server.Server object at 0x00000271DB765720>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000271948C16C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000271948C2170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000271948C2200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002719477FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=16>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=16>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=16>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=16>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=30>
           │    └ <_ProactorSocketTransport fd=8124 read=<_OverlappedFuture pending overlapped=<pending, 0x271dcc465f0> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=16>

AssertionError: assert f is self._write_fut
[2025-08-22 15:03:56.946072] [ERROR process-12860-26940 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=13>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=13>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000002719487F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 65132, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000002719487E050>
              └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000002719487E0E0>
           └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000002719425EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x00000271D46CC310>
    │       └ <function run at 0x00000271D393D090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000271D393CAF0>
    └ <uvicorn.server.Server object at 0x00000271DB765720>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000271D393CB80>
           │       │   └ <uvicorn.server.Server object at 0x00000271DB765720>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000271948C16C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000271948C2170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000271948C2200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002719477FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=13>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=13>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=13>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=13>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=33>
           │    └ <_ProactorSocketTransport fd=8124 read=<_OverlappedFuture pending overlapped=<pending, 0x271dcbfcd30> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=13>

AssertionError: assert f is self._write_fut
[2025-08-22 15:03:57.320028] [ERROR process-12860-26940 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000002719487F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 65132, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000002719487E050>
              └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000002719487E0E0>
           └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000002719425EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x00000271D46CC310>
    │       └ <function run at 0x00000271D393D090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000271D393CAF0>
    └ <uvicorn.server.Server object at 0x00000271DB765720>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000271D393CB80>
           │       │   └ <uvicorn.server.Server object at 0x00000271DB765720>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000271948C16C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000271948C2170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000271948C2200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002719477FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=38>
           │    └ <_ProactorSocketTransport fd=8124 read=<_OverlappedFuture pending overlapped=<pending, 0x271dcbfcd30> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=10>

AssertionError: assert f is self._write_fut
[2025-08-22 15:04:07.205953] [INFO process-12860-26940 bisheng.chat.manager:243] - trace=1 act=rcv_client_disconnect (1005, None)
[2025-08-22 15:04:07.210968] [INFO process-12860-26940 bisheng.chat.manager:181] - trace=1 close_client client_key=415d4554a76c4c9bbcd20721e6891442
[2025-08-22 15:04:07.309987] [ERROR process-12860-26940 bisheng.chat.manager:194] - trace=1 Unexpected ASGI message 'websocket.close', after sending 'websocket.close'.
[2025-08-22 15:04:07.318329] [WARNING process-12860-26940 bisheng.chat.manager:179] - trace=1 close_client client_key=415d4554a76c4c9bbcd20721e6891442 not in active_clients
[2025-08-22 15:04:07.536980] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=f3bea38388b14537a990cc2bbe0cc472 GET /api/v1/skill/template
[2025-08-22 15:04:07.618403] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=f3bea38388b14537a990cc2bbe0cc472 GET /api/v1/skill/template 200 timecost=81.423
[2025-08-22 15:04:07.816595] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=c0e6a233a4044b99bd32fb8d1b710802 GET /api/v1/workflow/list
[2025-08-22 15:04:07.919894] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=c0e6a233a4044b99bd32fb8d1b710802 GET /api/v1/workflow/list 200 timecost=103.299
[2025-08-22 15:04:07.949001] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=ae74125a1e5a404293f10712029fbc69 GET /api/v1/skill/template
[2025-08-22 15:04:08.017142] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=ae74125a1e5a404293f10712029fbc69 GET /api/v1/skill/template 200 timecost=69.139
[2025-08-22 15:04:09.517453] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=e30471ff0cf34f1e8de6d1b11fca6feb GET /api/v1/tag
[2025-08-22 15:04:09.558131] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=e30471ff0cf34f1e8de6d1b11fca6feb GET /api/v1/tag 200 timecost=40.678
[2025-08-22 15:04:09.824566] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=f4a3e18d75aa48e3b2eb47f70b394e36 GET /api/v1/tag/home
[2025-08-22 15:04:09.828569] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=fc156d7ac3994658bbe47b8a5d0d6c3f GET /api/v1/chat/list
[2025-08-22 15:04:09.882009] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=f4a3e18d75aa48e3b2eb47f70b394e36 GET /api/v1/tag/home 200 timecost=57.443
[2025-08-22 15:04:09.897207] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=0a9c3483ee8e4d809497cffaaa8003a0 GET /api/v1/tag
[2025-08-22 15:04:09.938641] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=fc156d7ac3994658bbe47b8a5d0d6c3f GET /api/v1/chat/list 200 timecost=110.072
[2025-08-22 15:04:09.951453] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=0a9c3483ee8e4d809497cffaaa8003a0 GET /api/v1/tag 200 timecost=53.018
[2025-08-22 15:04:10.436531] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=567992ca23444ff1aa151265937d3c06 GET /api/v1/chat/online
[2025-08-22 15:04:10.517856] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=567992ca23444ff1aa151265937d3c06 GET /api/v1/chat/online 200 timecost=82.35
[2025-08-22 15:04:11.137174] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=8c64dd510d484234bdf384b23346e302 GET /api/v1/chat/online
[2025-08-22 15:04:11.193899] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=8c64dd510d484234bdf384b23346e302 GET /api/v1/chat/online 200 timecost=56.724
[2025-08-22 15:04:16.159005] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=caa334d150484b00bb1cc6bfc08f7bc1 GET /api/v1/flows/ac8b23aed25947908ac042dc5b5f1594
[2025-08-22 15:04:16.203068] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=caa334d150484b00bb1cc6bfc08f7bc1 GET /api/v1/flows/ac8b23aed25947908ac042dc5b5f1594 200 timecost=44.063
[2025-08-22 15:04:16.521547] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=537b3ad21e694f66a3b3d05513856ed7 GET /api/v1/chat/history
[2025-08-22 15:04:16.553097] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=537b3ad21e694f66a3b3d05513856ed7 GET /api/v1/chat/history 200 timecost=31.55
[2025-08-22 15:04:16.987321] [INFO process-12860-26940 bisheng.chat.clients.base:68] - trace=1 client_id=4774d1a912af4c4db68acce7904d403d trace_id=109dd73b188341d0889480b1c86189b4 message={'action': 'init_data', 'chat_id': '3643d6089784d4254c2bc4c312643031', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'data': {'description': '支持添加知识库和工具', 'status': 2, 'user_id': 1, 'guide_word': None, 'create_time': '2025-08-21T10:54:24', 'name': '多轮对话-479e3', 'logo': '', 'flow_type': 10, 'update_time': '2025-08-21T10:58:11', 'id': 'ac8b23aed25947908ac042dc5b5f1594', 'nodes': [{'id': 'start_553b9', 'data': {'v': 1, 'id': 'start_553b9', 'name': '开始', 'type': 'start', 'description': '工作流运行的起始节点。', 'group_params': [{'name': '开场引导', 'params': [{'key': 'guide_word', 'type': 'textarea', 'label': '开场白', 'value': '您好，请问想聊些什么呢？', 'placeholder': '每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。'}, {'key': 'guide_question', 'help': '为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', 'type': 'input_list', 'label': '引导问题', 'value': ['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], 'placeholder': '请输入引导问题'}]}, {'name': '全局变量', 'params': [{'key': 'current_time', 'type': 'var', 'label': '当前时间', 'value': '', 'global': 'key'}, {'key': 'chat_history', 'type': 'chat_history_num', 'value': 10, 'global': 'key'}, {'key': 'preset_question', 'help': '适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', 'type': 'input_list', 'label': '预置问题列表', 'value': [{'key': 'f4cbae', 'value': ''}], 'global': 'item:input_list', 'placeholder': '输入批量预置问题'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 705}, 'position': {'x': 491, 'y': 63.125}, 'selected': False}, {'id': 'input_2775b', 'data': {'v': 2, 'id': 'input_2775b', 'tab': {'value': 'dialog_input', 'options': [{'key': 'dialog_input', 'help': '接收用户从对话框输入的内容。!(input)', 'label': '对话框输入'}, {'key': 'form_input', 'help': '将会在用户会话界面弹出一个表单，接收用户从表单提交的内容。!(form)', 'label': '表单输入'}]}, 'name': '输入', 'type': 'input', 'expand': False, 'description': '接收用户在会话页面的输入，支持 2 种形式：对话框输入，表单输入。', 'group_params': [{'name': '', 'params': [{'key': 'user_input', 'tab': 'dialog_input', 'type': 'var', 'label': '用户输入内容', 'global': 'key'}, {'key': 'form_input', 'tab': 'form_input', 'type': 'form', 'label': '+ 添加表单项', 'value': [], 'global': 'item:form_input'}, {'key': 'dialog_files_content', 'tab': 'dialog_input', 'type': 'var', 'label': '上传文件内容', 'global': 'key'}, {'key': 'dialog_files_content_size', 'min': 0, 'tab': 'dialog_input', 'type': 'char_number', 'label': '文件内容长度上限', 'value': 15000}, {'key': 'dialog_file_accept', 'tab': 'dialog_input', 'type': 'select_fileaccept', 'label': '上传文件类型', 'value': 'all'}, {'key': 'dialog_image_files', 'tab': 'dialog_input', 'help': '提取上传文件中的图片文件，当助手或大模型节点使用多模态大模型时，可传入此图片。', 'type': 'var', 'label': '上传图片文件', 'global': 'key'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 151}, 'position': {'x': 947, 'y': 85}, 'selected': False}, {'id': 'agent_1d0c8', 'data': {'v': 2, 'id': 'agent_1d0c8', 'tab': {'value': 'single', 'options': [{'key': 'single', 'label': '单次运行'}, {'key': 'batch', 'label': '批量运行'}]}, 'name': '多轮对话助手', 'type': 'agent', 'expand': True, 'description': 'AI 自主进行任务规划，选择合适的知识库或工具进行调用。', 'group_params': [{'params': [{'key': 'batch_variable', 'tab': 'batch', 'help': '选择需要批处理的变量，将会多次运行本节点，每次运行时从选择的变量中取一项赋值给batch_variable进行处理。', 'type': 'user_question', 'label': '批处理变量', 'value': [], 'global': 'self', 'linkage': 'output', 'required': True, 'placeholder': '请选择批处理变量'}]}, {'name': '模型设置', 'params': [{'key': 'model_id', 'type': 'agent_model', 'label': '模型', 'value': 2, 'required': True, 'placeholder': '请选择模型'}, {'key': 'temperature', 'step': 0.1, 'type': 'slide', 'label': '温度', 'scope': [0, 2], 'value': 0.7}]}, {'name': '提示词', 'params': [{'key': 'system_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '系统提示词', 'value': '你是一个熟悉各行各业的 AI 助理，善于根据用户需求，适时调用合适的工具来回答用户问题', 'required': True, 'placeholder': '助手画像'}, {'key': 'user_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '用户提示词', 'value': '{{#input_2775b.user_input#}}', 'varZh': {'input_2775b.user_input': '输入/user_input'}, 'required': True, 'placeholder': '用户消息内容'}, {'key': 'chat_history_flag', 'help': '带入模型上下文的历史消息条数，为 0 时代表不包含上下文信息。', 'step': 1, 'type': 'slide_switch', 'label': '历史聊天记录', 'scope': [0, 100], 'value': {'flag': True, 'value': 50}}, {'key': 'image_prompt', 'help': '当使用多模态大模型时，可通过此功能传入图片，结合图像内容进行问答', 'type': 'image_prompt', 'label': '视觉', 'value': ''}]}, {'name': '知识库', 'params': [{'key': 'knowledge_id', 'type': 'knowledge_select_multi', 'label': '检索知识库范围', 'value': {'type': 'knowledge', 'value': []}, 'placeholder': '请选择知识库'}]}, {'name': '数据库', 'params': [{'key': 'sql_agent', 'type': 'sql_config', 'value': {'open': False, 'db_name': '', 'db_address': '', 'db_password': '', 'db_username': ''}}]}, {'name': '工具', 'params': [{'key': 'tool_list', 'type': 'add_tool', 'label': '+ 添加工具', 'value': []}]}, {'name': '输出', 'params': [{'key': 'output_user', 'help': '一般在问答等场景开启，文档审核、报告生成等场景可关闭。', 'type': 'switch', 'label': '将输出结果展示在会话中', 'value': True}, {'key': 'output', 'type': 'var', 'label': '输出变量', 'value': [], 'global': 'code:value.map(el => ({ label: el.label, value: el.key }))'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 1207}, 'position': {'x': 1416.1256404922672, 'y': 89.69476666183581}, 'selected': True}], 'edges': [{'id': 'xy-edge__start_553b9right_handle-input_2775bleft_handle', 'type': 'customEdge', 'source': 'start_553b9', 'target': 'input_2775b', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__input_2775bright_handle-agent_1d0c8left_handle', 'type': 'customEdge', 'source': 'input_2775b', 'target': 'agent_1d0c8', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__agent_1d0c8right_handle-input_2775bleft_handle', 'type': 'customEdge', 'source': 'agent_1d0c8', 'target': 'input_2775b', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}], 'viewport': {'x': 186.00521793194332, 'y': -0.22168128703947332, 'zoom': 0.6371751491016153}}}
[2025-08-22 15:04:17.044416] [INFO process-12860-23748 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:04:17.089113] [INFO process-12860-9920 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:04:17.123119] [INFO process-12860-27688 bisheng.api.services.audit_log:109] - trace=109dd73b188341d0889480b1c86189b4 act=create_chat_workflow user=<EMAIL> ip=127.0.0.1 flow=ac8b23aed25947908ac042dc5b5f1594
[2025-08-22 15:04:18.099301] [INFO process-12860-23748 bisheng.utils.threadpool:67] - trace=2 async_task_added fun=consume_message args=
[2025-08-22 15:04:18.114464] [INFO process-12860-9920 bisheng.utils.threadpool:67] - trace=109dd73b188341d0889480b1c86189b4 async_task_added fun=wrapper_task args=109dd73b188341d0889480b1c86189b4
[2025-08-22 15:04:24.461890] [INFO process-12860-26940 bisheng.chat.clients.base:68] - trace=1 client_id=4774d1a912af4c4db68acce7904d403d trace_id=991360c033c84449b35b04a6f6104e97 message={'action': 'input', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'chat_id': '3643d6089784d4254c2bc4c312643031', 'data': {'input_2775b': {'data': {'user_input': '你知道小米手机吗？', 'dialog_files_content': []}, 'message': '你知道小米手机吗？', 'message_id': 30, 'category': 'question', 'extra': '', 'source': 0}}}
[2025-08-22 15:04:24.480454] [INFO process-12860-27576 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:04:24.493051] [INFO process-12860-27548 bisheng.chat.clients.workflow_client:182] - trace=991360c033c84449b35b04a6f6104e97 get user input: {'input_2775b': {'data': {'user_input': '你知道小米手机吗？', 'dialog_files_content': []}, 'message': '你知道小米手机吗？', 'message_id': 30, 'category': 'question', 'extra': '', 'source': 0}}
[2025-08-22 15:04:25.497151] [INFO process-12860-27576 bisheng.utils.threadpool:67] - trace=991360c033c84449b35b04a6f6104e97 async_task_added fun=wrapper_task args=991360c033c84449b35b04a6f6104e97
[2025-08-22 15:04:26.324998] [ERROR process-12860-26940 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=14>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=14>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000002719487F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 65132, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000002719487E050>
              └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000002719487E0E0>
           └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000002719425EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x00000271D46CC310>
    │       └ <function run at 0x00000271D393D090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000271D393CAF0>
    └ <uvicorn.server.Server object at 0x00000271DB765720>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000271D393CB80>
           │       │   └ <uvicorn.server.Server object at 0x00000271DB765720>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000271948C16C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000271948C2170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000271948C2200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002719477FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=14>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=14>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=14>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=14>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=64>
           │    └ <_ProactorSocketTransport fd=7940 read=<_OverlappedFuture pending overlapped=<pending, 0x271dd19ec20> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=14>

AssertionError: assert f is self._write_fut
[2025-08-22 15:04:27.619771] [ERROR process-12860-26940 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=17>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=17>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000002719487F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 65132, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000002719487E050>
              └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000002719487E0E0>
           └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000002719425EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x00000271D46CC310>
    │       └ <function run at 0x00000271D393D090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000271D393CAF0>
    └ <uvicorn.server.Server object at 0x00000271DB765720>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000271D393CB80>
           │       │   └ <uvicorn.server.Server object at 0x00000271DB765720>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000271948C16C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000271948C2170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000271948C2200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002719477FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=17>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=17>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=17>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=17>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=79>
           │    └ <_ProactorSocketTransport fd=7940 read=<_OverlappedFuture pending overlapped=<pending, 0x271dd19ec20> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=17>

AssertionError: assert f is self._write_fut
[2025-08-22 15:04:28.301186] [ERROR process-12860-26940 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=58>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=58>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000002719487F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 65132, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000002719487E050>
              └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000002719487E0E0>
           └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000002719425EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x00000271D46CC310>
    │       └ <function run at 0x00000271D393D090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000271D393CAF0>
    └ <uvicorn.server.Server object at 0x00000271DB765720>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000271D393CB80>
           │       │   └ <uvicorn.server.Server object at 0x00000271DB765720>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000271948C16C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000271948C2170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000271948C2200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002719477FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=58>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=58>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=58>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=58>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=10>
           │    └ <_ProactorSocketTransport fd=7940 read=<_OverlappedFuture pending overlapped=<pending, 0x271dd19ec20> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=58>

AssertionError: assert f is self._write_fut
[2025-08-22 15:04:28.661151] [ERROR process-12860-26940 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000002719487F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 65132, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000002719487E050>
              └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000002719487E0E0>
           └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000002719425EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x00000271D46CC310>
    │       └ <function run at 0x00000271D393D090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000271D393CAF0>
    └ <uvicorn.server.Server object at 0x00000271DB765720>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000271D393CB80>
           │       │   └ <uvicorn.server.Server object at 0x00000271DB765720>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000271948C16C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000271948C2170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000271948C2200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002719477FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=47>
           │    └ <_ProactorSocketTransport fd=7940 read=<_OverlappedFuture pending overlapped=<pending, 0x271dd19ec20> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=10>

AssertionError: assert f is self._write_fut
[2025-08-22 15:04:29.126307] [ERROR process-12860-26940 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=61>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=61>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000002719487F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 65132, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000002719487E050>
              └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000002719487E0E0>
           └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000002719425EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x00000271D46CC310>
    │       └ <function run at 0x00000271D393D090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000271D393CAF0>
    └ <uvicorn.server.Server object at 0x00000271DB765720>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000271D393CB80>
           │       │   └ <uvicorn.server.Server object at 0x00000271DB765720>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000271948C16C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000271948C2170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000271948C2200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002719477FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=61>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=61>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=61>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=61>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=10>
           │    └ <_ProactorSocketTransport fd=7940 read=<_OverlappedFuture pending overlapped=<pending, 0x271dd19ec20> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=61>

AssertionError: assert f is self._write_fut
[2025-08-22 15:04:29.907626] [ERROR process-12860-26940 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000002719487F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 65132, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000002719487E050>
              └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000002719487E0E0>
           └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000002719425EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x00000271D46CC310>
    │       └ <function run at 0x00000271D393D090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000271D393CAF0>
    └ <uvicorn.server.Server object at 0x00000271DB765720>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000271D393CB80>
           │       │   └ <uvicorn.server.Server object at 0x00000271DB765720>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000271948C16C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000271948C2170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000271948C2200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002719477FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=75>
           │    └ <_ProactorSocketTransport fd=7940 read=<_OverlappedFuture pending overlapped=<pending, 0x271dd19ec20> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=10>

AssertionError: assert f is self._write_fut
[2025-08-22 15:04:30.453197] [ERROR process-12860-26940 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000002719487F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 65132, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000002719487E050>
              └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000002719487E0E0>
           └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000002719425EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x00000271D46CC310>
    │       └ <function run at 0x00000271D393D090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000271D393CAF0>
    └ <uvicorn.server.Server object at 0x00000271DB765720>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000271D393CB80>
           │       │   └ <uvicorn.server.Server object at 0x00000271DB765720>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000271948C16C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000271948C2170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000271948C2200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002719477FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=71>
           │    └ <_ProactorSocketTransport fd=7940 read=<_OverlappedFuture pending overlapped=<pending, 0x271dd19ec20> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=10>

AssertionError: assert f is self._write_fut
[2025-08-22 15:04:30.902784] [ERROR process-12860-26940 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000002719487F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 65132, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000002719487E050>
              └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000002719487E0E0>
           └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000002719425EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x00000271D46CC310>
    │       └ <function run at 0x00000271D393D090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000271D393CAF0>
    └ <uvicorn.server.Server object at 0x00000271DB765720>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000271D393CB80>
           │       │   └ <uvicorn.server.Server object at 0x00000271DB765720>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000271948C16C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000271948C2170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000271948C2200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002719477FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=43>
           │    └ <_ProactorSocketTransport fd=7940 read=<_OverlappedFuture pending overlapped=<pending, 0x271dd19ec20> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=10>

AssertionError: assert f is self._write_fut
[2025-08-22 15:04:31.310064] [ERROR process-12860-26940 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=76>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=76>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000002719487F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 65132, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000002719487E050>
              └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000002719487E0E0>
           └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000002719425EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x00000271D46CC310>
    │       └ <function run at 0x00000271D393D090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000271D393CAF0>
    └ <uvicorn.server.Server object at 0x00000271DB765720>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000271D393CB80>
           │       │   └ <uvicorn.server.Server object at 0x00000271DB765720>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000271948C16C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000271948C2170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000271948C2200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002719477FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=76>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=76>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=76>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=76>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=15>
           │    └ <_ProactorSocketTransport fd=7940 read=<_OverlappedFuture pending overlapped=<pending, 0x271dd19ec20> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=76>

AssertionError: assert f is self._write_fut
[2025-08-22 15:04:31.574651] [ERROR process-12860-26940 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=73>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=73>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000002719487F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 65132, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000002719487E050>
              └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000002719487E0E0>
           └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000002719425EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x00000271D46CC310>
    │       └ <function run at 0x00000271D393D090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000271D393CAF0>
    └ <uvicorn.server.Server object at 0x00000271DB765720>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000271D393CB80>
           │       │   └ <uvicorn.server.Server object at 0x00000271DB765720>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000271948C16C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000271948C2170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000271948C2200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002719477FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=73>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=73>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=73>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=73>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=15>
           │    └ <_ProactorSocketTransport fd=7940 read=<_OverlappedFuture pending overlapped=<pending, 0x271dd19ec20> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=73>

AssertionError: assert f is self._write_fut
[2025-08-22 15:05:27.938312] [INFO process-12860-26940 bisheng.chat.clients.base:68] - trace=1 client_id=4774d1a912af4c4db68acce7904d403d trace_id=5fc9a00c1e454e0fb851534db646c3f5 message={'action': 'input', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'chat_id': '3643d6089784d4254c2bc4c312643031', 'data': {'input_2775b': {'data': {'user_input': '请问你知道华为手机吗？', 'dialog_files_content': []}, 'message': '请问你知道华为手机吗？', 'message_id': 33, 'category': 'question', 'extra': '', 'source': 0}}}
[2025-08-22 15:05:27.957820] [INFO process-12860-23748 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:05:27.974223] [INFO process-12860-30480 bisheng.chat.clients.workflow_client:182] - trace=5fc9a00c1e454e0fb851534db646c3f5 get user input: {'input_2775b': {'data': {'user_input': '请问你知道华为手机吗？', 'dialog_files_content': []}, 'message': '请问你知道华为手机吗？', 'message_id': 33, 'category': 'question', 'extra': '', 'source': 0}}
[2025-08-22 15:05:28.971705] [INFO process-12860-23748 bisheng.utils.threadpool:67] - trace=5fc9a00c1e454e0fb851534db646c3f5 async_task_added fun=wrapper_task args=5fc9a00c1e454e0fb851534db646c3f5
[2025-08-22 15:05:29.316542] [ERROR process-12860-26940 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=90>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=90>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000002719487F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 65132, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000002719487E050>
              └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000002719487E0E0>
           └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000002719425EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x00000271D46CC310>
    │       └ <function run at 0x00000271D393D090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000271D393CAF0>
    └ <uvicorn.server.Server object at 0x00000271DB765720>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000271D393CB80>
           │       │   └ <uvicorn.server.Server object at 0x00000271DB765720>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000271948C16C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000271948C2170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000271948C2200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002719477FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=90>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=90>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=90>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=90>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=14>
           │    └ <_ProactorSocketTransport fd=7940 read=<_OverlappedFuture pending overlapped=<pending, 0x271dd19ec20> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=90>

AssertionError: assert f is self._write_fut
[2025-08-22 15:05:29.884762] [ERROR process-12860-26940 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=56>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=56>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000002719487F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 65132, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000002719487E050>
              └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000002719487E0E0>
           └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000002719425EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x00000271D46CC310>
    │       └ <function run at 0x00000271D393D090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000271D393CAF0>
    └ <uvicorn.server.Server object at 0x00000271DB765720>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000271D393CB80>
           │       │   └ <uvicorn.server.Server object at 0x00000271DB765720>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000271948C16C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000271948C2170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000271948C2200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002719477FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=56>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=56>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=56>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=56>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=13>
           │    └ <_ProactorSocketTransport fd=7940 read=<_OverlappedFuture pending overlapped=<pending, 0x271dd19ec20> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=56>

AssertionError: assert f is self._write_fut
[2025-08-22 15:05:30.526037] [ERROR process-12860-26940 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=67>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=67>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000002719487F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 65132, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000002719487E050>
              └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000002719487E0E0>
           └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000002719425EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x00000271D46CC310>
    │       └ <function run at 0x00000271D393D090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000271D393CAF0>
    └ <uvicorn.server.Server object at 0x00000271DB765720>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000271D393CB80>
           │       │   └ <uvicorn.server.Server object at 0x00000271DB765720>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000271948C16C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000271948C2170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000271948C2200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002719477FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=67>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=67>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=67>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=67>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=10>
           │    └ <_ProactorSocketTransport fd=7940 read=<_OverlappedFuture pending overlapped=<pending, 0x271dd19ec20> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=67>

AssertionError: assert f is self._write_fut
[2025-08-22 15:05:30.958580] [ERROR process-12860-26940 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=80>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=80>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000002719487F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 65132, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000002719487E050>
              └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000002719487E0E0>
           └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000002719425EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x00000271D46CC310>
    │       └ <function run at 0x00000271D393D090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000271D393CAF0>
    └ <uvicorn.server.Server object at 0x00000271DB765720>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000271D393CB80>
           │       │   └ <uvicorn.server.Server object at 0x00000271DB765720>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000271948C16C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000271948C2170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000271948C2200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002719477FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=80>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=80>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=80>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=80>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=17>
           │    └ <_ProactorSocketTransport fd=7940 read=<_OverlappedFuture pending overlapped=<pending, 0x271dd19ec20> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=80>

AssertionError: assert f is self._write_fut
[2025-08-22 15:05:31.694049] [ERROR process-12860-26940 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=83>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=83>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000002719487F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 65132, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000002719487E050>
              └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000002719487E0E0>
           └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000002719425EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x00000271D46CC310>
    │       └ <function run at 0x00000271D393D090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000271D393CAF0>
    └ <uvicorn.server.Server object at 0x00000271DB765720>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000271D393CB80>
           │       │   └ <uvicorn.server.Server object at 0x00000271DB765720>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000271948C16C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000271948C2170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000271948C2200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002719477FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=83>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=83>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=83>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=83>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=10>
           │    └ <_ProactorSocketTransport fd=7940 read=<_OverlappedFuture pending overlapped=<pending, 0x271dd19ec20> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=83>

AssertionError: assert f is self._write_fut
[2025-08-22 15:05:31.906148] [ERROR process-12860-26940 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=88>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=88>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000002719487F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 65132, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000002719487E050>
              └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000002719487E0E0>
           └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000002719425EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x00000271D46CC310>
    │       └ <function run at 0x00000271D393D090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000271D393CAF0>
    └ <uvicorn.server.Server object at 0x00000271DB765720>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000271D393CB80>
           │       │   └ <uvicorn.server.Server object at 0x00000271DB765720>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000271948C16C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000271948C2170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000271948C2200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002719477FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=88>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=88>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=88>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=88>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=10>
           │    └ <_ProactorSocketTransport fd=7940 read=<_OverlappedFuture pending overlapped=<pending, 0x271dd19ec20> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=88>

AssertionError: assert f is self._write_fut
[2025-08-22 15:05:32.201843] [ERROR process-12860-26940 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=15>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=15>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000002719487F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 65132, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000002719487E050>
              └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000002719487E0E0>
           └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000002719425EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x00000271D46CC310>
    │       └ <function run at 0x00000271D393D090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000271D393CAF0>
    └ <uvicorn.server.Server object at 0x00000271DB765720>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000271D393CB80>
           │       │   └ <uvicorn.server.Server object at 0x00000271DB765720>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000271948C16C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000271948C2170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000271948C2200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002719477FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=15>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=15>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=15>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=15>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=82>
           │    └ <_ProactorSocketTransport fd=7940 read=<_OverlappedFuture pending overlapped=<pending, 0x271dd19ec20> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=15>

AssertionError: assert f is self._write_fut
[2025-08-22 15:05:34.060052] [ERROR process-12860-26940 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000002719487F010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 65132, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000002719487E050>
              └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000002719487E0E0>
           └ <__main__.PyDB object at 0x0000027194855540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000002719425EF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x00000271D46CC310>
    │       └ <function run at 0x00000271D393D090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000271D393CAF0>
    └ <uvicorn.server.Server object at 0x00000271DB765720>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000271D393CB80>
           │       │   └ <uvicorn.server.Server object at 0x00000271DB765720>
           │       └ <function _patch_asyncio.<locals>.run at 0x00000271948C16C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x00000271948C2170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x00000271948C2200>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002719477FA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=64>
           │    └ <_ProactorSocketTransport fd=7940 read=<_OverlappedFuture pending overlapped=<pending, 0x271dd19ec20> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=10>

AssertionError: assert f is self._write_fut
[2025-08-22 15:06:33.692381] [INFO process-12860-26940 bisheng.chat.clients.base:68] - trace=1 client_id=4774d1a912af4c4db68acce7904d403d trace_id=920f2eee38134951a1bbd434b6593ee1 message={'action': 'input', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'chat_id': '3643d6089784d4254c2bc4c312643031', 'data': {'input_2775b': {'data': {'user_input': '那你知道苹果手机吗？介绍一下，尽量详细的说。', 'dialog_files_content': []}, 'message': '那你知道苹果手机吗？介绍一下，尽量详细的说。', 'message_id': 36, 'category': 'question', 'extra': '', 'source': 0}}}
[2025-08-22 15:06:33.713300] [INFO process-12860-26392 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:06:33.732695] [INFO process-12860-32548 bisheng.chat.clients.workflow_client:182] - trace=920f2eee38134951a1bbd434b6593ee1 get user input: {'input_2775b': {'data': {'user_input': '那你知道苹果手机吗？介绍一下，尽量详细的说。', 'dialog_files_content': []}, 'message': '那你知道苹果手机吗？介绍一下，尽量详细的说。', 'message_id': 36, 'category': 'question', 'extra': '', 'source': 0}}
[2025-08-22 15:06:34.731639] [INFO process-12860-26392 bisheng.utils.threadpool:67] - trace=920f2eee38134951a1bbd434b6593ee1 async_task_added fun=wrapper_task args=920f2eee38134951a1bbd434b6593ee1
[2025-08-22 15:06:49.363012] [INFO process-12860-26940 bisheng.chat.manager:243] - trace=1 act=rcv_client_disconnect (1005, None)
[2025-08-22 15:06:49.372072] [INFO process-12860-26940 bisheng.chat.manager:181] - trace=1 close_client client_key=4774d1a912af4c4db68acce7904d403d
[2025-08-22 15:06:49.380733] [ERROR process-12860-26940 bisheng.chat.manager:194] - trace=1 Unexpected ASGI message 'websocket.close', after sending 'websocket.close'.
[2025-08-22 15:06:49.388818] [WARNING process-12860-26940 bisheng.chat.manager:179] - trace=1 close_client client_key=4774d1a912af4c4db68acce7904d403d not in active_clients
[2025-08-22 15:06:49.392810] [ERROR process-12860-29640 bisheng.chat.clients.base:61] - trace=2 consume_message error {'is_bot': 1, 'message': '', 'type': 'close', 'category': 'processing', 'intermediate_steps': '', 'files': [], 'user_id': 1, 'message_id': None, 'source': 0, 'sender': None, 'receiver': None, 'liked': 0, 'extra': '{"client_key": "4774d1a912af4c4db68acce7904d403d"}', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'chat_id': '3643d6089784d4254c2bc4c312643031'} error: Cannot call "send" once a close message has been sent.
[2025-08-22 15:06:49.679303] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=75012c38c6b544c08c1f7de47106a5e7 GET /api/v1/tag
[2025-08-22 15:06:49.687815] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=eadb65cfad594454af4d0344f28d0ba5 GET /api/v1/tag/home
[2025-08-22 15:06:49.769242] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=eadb65cfad594454af4d0344f28d0ba5 GET /api/v1/tag/home 200 timecost=81.427
[2025-08-22 15:06:49.780282] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=75012c38c6b544c08c1f7de47106a5e7 GET /api/v1/tag 200 timecost=100.978
[2025-08-22 15:06:49.792555] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=cceab5b14de441c4b964082030c74e52 GET /api/v1/tag
[2025-08-22 15:06:49.837066] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=cceab5b14de441c4b964082030c74e52 GET /api/v1/tag 200 timecost=44.511
[2025-08-22 15:06:49.968659] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=531f713b8ea341bab3ac55a8fc45b62f GET /api/v1/chat/online
[2025-08-22 15:06:50.046463] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=531f713b8ea341bab3ac55a8fc45b62f GET /api/v1/chat/online 200 timecost=77.804
[2025-08-22 15:06:50.677336] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=a5da959b39ba492f9ed25aca7488ff82 GET /api/v1/chat/online
[2025-08-22 15:06:50.735577] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=a5da959b39ba492f9ed25aca7488ff82 GET /api/v1/chat/online 200 timecost=58.242
[2025-08-22 15:06:51.007278] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=a42cf845274443fe82965f3c92278513 GET /api/v1/flows/ac8b23aed25947908ac042dc5b5f1594
[2025-08-22 15:06:51.049251] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=a42cf845274443fe82965f3c92278513 GET /api/v1/flows/ac8b23aed25947908ac042dc5b5f1594 200 timecost=40.953
[2025-08-22 15:06:51.374184] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=c83439af87904774afc7740c8a83c6c5 GET /api/v1/chat/history
[2025-08-22 15:06:51.412477] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=c83439af87904774afc7740c8a83c6c5 GET /api/v1/chat/history 200 timecost=38.294
[2025-08-22 15:06:51.873016] [INFO process-12860-9920 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:06:51.884339] [INFO process-12860-26940 bisheng.chat.clients.base:68] - trace=1 client_id=8e3e10d9c8d24d3dbde59e2370b0ee9a trace_id=aa9f294a397b44daa84a652b7198ac3e message={'action': 'init_data', 'chat_id': '48ee4b24542124511ef3bcdbed62e380', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'data': {'description': '支持添加知识库和工具', 'status': 2, 'user_id': 1, 'guide_word': None, 'create_time': '2025-08-21T10:54:24', 'name': '多轮对话-479e3', 'logo': '', 'flow_type': 10, 'update_time': '2025-08-21T10:58:11', 'id': 'ac8b23aed25947908ac042dc5b5f1594', 'nodes': [{'id': 'start_553b9', 'data': {'v': 1, 'id': 'start_553b9', 'name': '开始', 'type': 'start', 'description': '工作流运行的起始节点。', 'group_params': [{'name': '开场引导', 'params': [{'key': 'guide_word', 'type': 'textarea', 'label': '开场白', 'value': '您好，请问想聊些什么呢？', 'placeholder': '每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。'}, {'key': 'guide_question', 'help': '为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', 'type': 'input_list', 'label': '引导问题', 'value': ['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], 'placeholder': '请输入引导问题'}]}, {'name': '全局变量', 'params': [{'key': 'current_time', 'type': 'var', 'label': '当前时间', 'value': '', 'global': 'key'}, {'key': 'chat_history', 'type': 'chat_history_num', 'value': 10, 'global': 'key'}, {'key': 'preset_question', 'help': '适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', 'type': 'input_list', 'label': '预置问题列表', 'value': [{'key': 'f4cbae', 'value': ''}], 'global': 'item:input_list', 'placeholder': '输入批量预置问题'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 705}, 'position': {'x': 491, 'y': 63.125}, 'selected': False}, {'id': 'input_2775b', 'data': {'v': 2, 'id': 'input_2775b', 'tab': {'value': 'dialog_input', 'options': [{'key': 'dialog_input', 'help': '接收用户从对话框输入的内容。!(input)', 'label': '对话框输入'}, {'key': 'form_input', 'help': '将会在用户会话界面弹出一个表单，接收用户从表单提交的内容。!(form)', 'label': '表单输入'}]}, 'name': '输入', 'type': 'input', 'expand': False, 'description': '接收用户在会话页面的输入，支持 2 种形式：对话框输入，表单输入。', 'group_params': [{'name': '', 'params': [{'key': 'user_input', 'tab': 'dialog_input', 'type': 'var', 'label': '用户输入内容', 'global': 'key'}, {'key': 'form_input', 'tab': 'form_input', 'type': 'form', 'label': '+ 添加表单项', 'value': [], 'global': 'item:form_input'}, {'key': 'dialog_files_content', 'tab': 'dialog_input', 'type': 'var', 'label': '上传文件内容', 'global': 'key'}, {'key': 'dialog_files_content_size', 'min': 0, 'tab': 'dialog_input', 'type': 'char_number', 'label': '文件内容长度上限', 'value': 15000}, {'key': 'dialog_file_accept', 'tab': 'dialog_input', 'type': 'select_fileaccept', 'label': '上传文件类型', 'value': 'all'}, {'key': 'dialog_image_files', 'tab': 'dialog_input', 'help': '提取上传文件中的图片文件，当助手或大模型节点使用多模态大模型时，可传入此图片。', 'type': 'var', 'label': '上传图片文件', 'global': 'key'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 151}, 'position': {'x': 947, 'y': 85}, 'selected': False}, {'id': 'agent_1d0c8', 'data': {'v': 2, 'id': 'agent_1d0c8', 'tab': {'value': 'single', 'options': [{'key': 'single', 'label': '单次运行'}, {'key': 'batch', 'label': '批量运行'}]}, 'name': '多轮对话助手', 'type': 'agent', 'expand': True, 'description': 'AI 自主进行任务规划，选择合适的知识库或工具进行调用。', 'group_params': [{'params': [{'key': 'batch_variable', 'tab': 'batch', 'help': '选择需要批处理的变量，将会多次运行本节点，每次运行时从选择的变量中取一项赋值给batch_variable进行处理。', 'type': 'user_question', 'label': '批处理变量', 'value': [], 'global': 'self', 'linkage': 'output', 'required': True, 'placeholder': '请选择批处理变量'}]}, {'name': '模型设置', 'params': [{'key': 'model_id', 'type': 'agent_model', 'label': '模型', 'value': 2, 'required': True, 'placeholder': '请选择模型'}, {'key': 'temperature', 'step': 0.1, 'type': 'slide', 'label': '温度', 'scope': [0, 2], 'value': 0.7}]}, {'name': '提示词', 'params': [{'key': 'system_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '系统提示词', 'value': '你是一个熟悉各行各业的 AI 助理，善于根据用户需求，适时调用合适的工具来回答用户问题', 'required': True, 'placeholder': '助手画像'}, {'key': 'user_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '用户提示词', 'value': '{{#input_2775b.user_input#}}', 'varZh': {'input_2775b.user_input': '输入/user_input'}, 'required': True, 'placeholder': '用户消息内容'}, {'key': 'chat_history_flag', 'help': '带入模型上下文的历史消息条数，为 0 时代表不包含上下文信息。', 'step': 1, 'type': 'slide_switch', 'label': '历史聊天记录', 'scope': [0, 100], 'value': {'flag': True, 'value': 50}}, {'key': 'image_prompt', 'help': '当使用多模态大模型时，可通过此功能传入图片，结合图像内容进行问答', 'type': 'image_prompt', 'label': '视觉', 'value': ''}]}, {'name': '知识库', 'params': [{'key': 'knowledge_id', 'type': 'knowledge_select_multi', 'label': '检索知识库范围', 'value': {'type': 'knowledge', 'value': []}, 'placeholder': '请选择知识库'}]}, {'name': '数据库', 'params': [{'key': 'sql_agent', 'type': 'sql_config', 'value': {'open': False, 'db_name': '', 'db_address': '', 'db_password': '', 'db_username': ''}}]}, {'name': '工具', 'params': [{'key': 'tool_list', 'type': 'add_tool', 'label': '+ 添加工具', 'value': []}]}, {'name': '输出', 'params': [{'key': 'output_user', 'help': '一般在问答等场景开启，文档审核、报告生成等场景可关闭。', 'type': 'switch', 'label': '将输出结果展示在会话中', 'value': True}, {'key': 'output', 'type': 'var', 'label': '输出变量', 'value': [], 'global': 'code:value.map(el => ({ label: el.label, value: el.key }))'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 1207}, 'position': {'x': 1416.1256404922672, 'y': 89.69476666183581}, 'selected': True}], 'edges': [{'id': 'xy-edge__start_553b9right_handle-input_2775bleft_handle', 'type': 'customEdge', 'source': 'start_553b9', 'target': 'input_2775b', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__input_2775bright_handle-agent_1d0c8left_handle', 'type': 'customEdge', 'source': 'input_2775b', 'target': 'agent_1d0c8', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__agent_1d0c8right_handle-input_2775bleft_handle', 'type': 'customEdge', 'source': 'agent_1d0c8', 'target': 'input_2775b', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}], 'viewport': {'x': 186.00521793194332, 'y': -0.22168128703947332, 'zoom': 0.6371751491016153}}}
[2025-08-22 15:06:51.986247] [INFO process-12860-27576 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:06:52.007264] [INFO process-12860-30492 bisheng.api.services.audit_log:109] - trace=aa9f294a397b44daa84a652b7198ac3e act=create_chat_workflow user=<EMAIL> ip=127.0.0.1 flow=ac8b23aed25947908ac042dc5b5f1594
[2025-08-22 15:06:52.939013] [INFO process-12860-9920 bisheng.utils.threadpool:67] - trace=2 async_task_added fun=consume_message args=
[2025-08-22 15:06:53.000483] [INFO process-12860-27576 bisheng.utils.threadpool:67] - trace=aa9f294a397b44daa84a652b7198ac3e async_task_added fun=wrapper_task args=aa9f294a397b44daa84a652b7198ac3e
[2025-08-22 15:06:55.509161] [INFO process-12860-26940 bisheng.chat.clients.base:68] - trace=1 client_id=8e3e10d9c8d24d3dbde59e2370b0ee9a trace_id=b03d15b59ee84dd8967fdef846206186 message={'action': 'input', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'chat_id': '48ee4b24542124511ef3bcdbed62e380', 'data': {'input_2775b': {'data': {'user_input': '那你知道苹果手机吗？介绍一下，尽量详细的说。', 'dialog_files_content': []}, 'message': '那你知道苹果手机吗？介绍一下，尽量详细的说。', 'message_id': 38, 'category': 'question', 'extra': '', 'source': 0}}}
[2025-08-22 15:06:55.527545] [INFO process-12860-9920 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:06:55.544910] [INFO process-12860-20100 bisheng.chat.clients.workflow_client:182] - trace=b03d15b59ee84dd8967fdef846206186 get user input: {'input_2775b': {'data': {'user_input': '那你知道苹果手机吗？介绍一下，尽量详细的说。', 'dialog_files_content': []}, 'message': '那你知道苹果手机吗？介绍一下，尽量详细的说。', 'message_id': 38, 'category': 'question', 'extra': '', 'source': 0}}
[2025-08-22 15:06:56.544087] [INFO process-12860-9920 bisheng.utils.threadpool:67] - trace=b03d15b59ee84dd8967fdef846206186 async_task_added fun=wrapper_task args=b03d15b59ee84dd8967fdef846206186
[2025-08-22 15:07:16.035996] [INFO process-12860-26940 bisheng.chat.manager:243] - trace=1 act=rcv_client_disconnect (1005, None)
[2025-08-22 15:07:16.046061] [INFO process-12860-26940 bisheng.chat.manager:181] - trace=1 close_client client_key=8e3e10d9c8d24d3dbde59e2370b0ee9a
[2025-08-22 15:07:16.055929] [ERROR process-12860-26940 bisheng.chat.manager:194] - trace=1 Unexpected ASGI message 'websocket.close', after sending 'websocket.close'.
[2025-08-22 15:07:16.067722] [WARNING process-12860-26940 bisheng.chat.manager:179] - trace=1 close_client client_key=8e3e10d9c8d24d3dbde59e2370b0ee9a not in active_clients
[2025-08-22 15:07:16.074263] [ERROR process-12860-32564 bisheng.chat.clients.base:61] - trace=2 consume_message error {'is_bot': 1, 'message': '', 'type': 'close', 'category': 'processing', 'intermediate_steps': '', 'files': [], 'user_id': 1, 'message_id': None, 'source': 0, 'sender': None, 'receiver': None, 'liked': 0, 'extra': '{"client_key": "8e3e10d9c8d24d3dbde59e2370b0ee9a"}', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'chat_id': '48ee4b24542124511ef3bcdbed62e380'} error: Cannot call "send" once a close message has been sent.
[2025-08-22 15:07:16.093915] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=54916e707aea461d85f98751be186a14 GET /api/v1/llm/knowledge
[2025-08-22 15:07:16.171501] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=54916e707aea461d85f98751be186a14 GET /api/v1/llm/knowledge 200 timecost=77.586
[2025-08-22 15:07:16.357659] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=d548d53918714c1a893862d5598af0e3 GET /api/v1/llm
[2025-08-22 15:07:16.440642] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=d548d53918714c1a893862d5598af0e3 GET /api/v1/llm 200 timecost=82.983
[2025-08-22 15:07:16.493365] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=cc8cdb4bd657457e9db96b003efcff75 GET /api/v1/skill/template
[2025-08-22 15:07:16.567653] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=cc8cdb4bd657457e9db96b003efcff75 GET /api/v1/skill/template 200 timecost=74.287
[2025-08-22 15:07:16.658510] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=058209364dd549b19d4b2cd84ca8beda GET /api/v1/knowledge
[2025-08-22 15:07:16.752800] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=058209364dd549b19d4b2cd84ca8beda GET /api/v1/knowledge 200 timecost=93.285
[2025-08-22 15:07:17.383370] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=9679f5a61d7f450d9e3765f0fafd084a GET /api/v1/workflow/list
[2025-08-22 15:07:17.507396] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=9679f5a61d7f450d9e3765f0fafd084a GET /api/v1/workflow/list 200 timecost=124.027
[2025-08-22 15:07:17.647791] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=74d28a3c88d242c081b2ea66dd0cc304 GET /api/v1/skill/template
[2025-08-22 15:07:17.734743] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=74d28a3c88d242c081b2ea66dd0cc304 GET /api/v1/skill/template 200 timecost=86.953
[2025-08-22 15:07:42.034356] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=6e82a5d7a04f4e21a0526c6866d8f873 GET /api/v1/skill/template
[2025-08-22 15:07:42.100381] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=6e82a5d7a04f4e21a0526c6866d8f873 GET /api/v1/skill/template 200 timecost=66.025
[2025-08-22 15:07:42.934715] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=aed65bcd3ccd4a4e83118cc14cf9693e GET /api/v1/workflow/list
[2025-08-22 15:07:43.061863] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=aed65bcd3ccd4a4e83118cc14cf9693e GET /api/v1/workflow/list 200 timecost=127.148
[2025-08-22 15:07:43.191853] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=bde714c759704920a706893dd098df1e GET /api/v1/skill/template
[2025-08-22 15:07:43.274117] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=bde714c759704920a706893dd098df1e GET /api/v1/skill/template 200 timecost=82.264
[2025-08-22 15:07:44.729383] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=9e05c9ee61fc40b49a3c9d6b348f44f4 GET /api/v1/tag
[2025-08-22 15:07:44.792212] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=9e05c9ee61fc40b49a3c9d6b348f44f4 GET /api/v1/tag 200 timecost=62.829
[2025-08-22 15:07:45.033170] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=51e64befbc024cf48feab7c57c73c692 GET /api/v1/tag/home
[2025-08-22 15:07:45.050849] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=dce09fca6d5f46f78f838619468d17e1 GET /api/v1/chat/list
[2025-08-22 15:07:45.166488] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=51e64befbc024cf48feab7c57c73c692 GET /api/v1/tag/home 200 timecost=133.318
[2025-08-22 15:07:45.234227] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=dce09fca6d5f46f78f838619468d17e1 GET /api/v1/chat/list 200 timecost=183.039
[2025-08-22 15:07:45.501894] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=408d40de15c348a68eb4ca19e177c0c4 GET /api/v1/tag
[2025-08-22 15:07:45.572686] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=408d40de15c348a68eb4ca19e177c0c4 GET /api/v1/tag 200 timecost=70.792
[2025-08-22 15:07:45.641859] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=9249d63453a74432a3b8c74d0ad91fa3 GET /api/v1/chat/online
[2025-08-22 15:07:45.749862] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=9249d63453a74432a3b8c74d0ad91fa3 GET /api/v1/chat/online 200 timecost=108.002
[2025-08-22 15:07:46.390053] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=f0126a5fd8d6450dadb215fa9af915ef GET /api/v1/chat/online
[2025-08-22 15:07:46.437161] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=3ca8b4c47da4414b8cfb99a5ffbd9439 GET /api/v1/flows/ac8b23aed25947908ac042dc5b5f1594
[2025-08-22 15:07:46.516776] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=3ca8b4c47da4414b8cfb99a5ffbd9439 GET /api/v1/flows/ac8b23aed25947908ac042dc5b5f1594 200 timecost=79.614
[2025-08-22 15:07:46.545301] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=f0126a5fd8d6450dadb215fa9af915ef GET /api/v1/chat/online 200 timecost=155.248
[2025-08-22 15:07:46.846123] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=a7423a59705a4920918abdd87d0157bc GET /api/v1/chat/history
[2025-08-22 15:07:46.890928] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=a7423a59705a4920918abdd87d0157bc GET /api/v1/chat/history 200 timecost=46.315
[2025-08-22 15:07:47.349083] [INFO process-12860-26940 bisheng.chat.clients.base:68] - trace=1 client_id=b27d7a70849649c5a402da24171ff1f1 trace_id=9d48c9568126477d8d8560c8a2df6be1 message={'action': 'init_data', 'chat_id': 'bfd40c7fc538b3d86925d91c60c18fb4', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'data': {'description': '支持添加知识库和工具', 'status': 2, 'user_id': 1, 'guide_word': None, 'create_time': '2025-08-21T10:54:24', 'name': '多轮对话-479e3', 'logo': '', 'flow_type': 10, 'update_time': '2025-08-21T10:58:11', 'id': 'ac8b23aed25947908ac042dc5b5f1594', 'nodes': [{'id': 'start_553b9', 'data': {'v': 1, 'id': 'start_553b9', 'name': '开始', 'type': 'start', 'description': '工作流运行的起始节点。', 'group_params': [{'name': '开场引导', 'params': [{'key': 'guide_word', 'type': 'textarea', 'label': '开场白', 'value': '您好，请问想聊些什么呢？', 'placeholder': '每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。'}, {'key': 'guide_question', 'help': '为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', 'type': 'input_list', 'label': '引导问题', 'value': ['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], 'placeholder': '请输入引导问题'}]}, {'name': '全局变量', 'params': [{'key': 'current_time', 'type': 'var', 'label': '当前时间', 'value': '', 'global': 'key'}, {'key': 'chat_history', 'type': 'chat_history_num', 'value': 10, 'global': 'key'}, {'key': 'preset_question', 'help': '适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', 'type': 'input_list', 'label': '预置问题列表', 'value': [{'key': 'f4cbae', 'value': ''}], 'global': 'item:input_list', 'placeholder': '输入批量预置问题'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 705}, 'position': {'x': 491, 'y': 63.125}, 'selected': False}, {'id': 'input_2775b', 'data': {'v': 2, 'id': 'input_2775b', 'tab': {'value': 'dialog_input', 'options': [{'key': 'dialog_input', 'help': '接收用户从对话框输入的内容。!(input)', 'label': '对话框输入'}, {'key': 'form_input', 'help': '将会在用户会话界面弹出一个表单，接收用户从表单提交的内容。!(form)', 'label': '表单输入'}]}, 'name': '输入', 'type': 'input', 'expand': False, 'description': '接收用户在会话页面的输入，支持 2 种形式：对话框输入，表单输入。', 'group_params': [{'name': '', 'params': [{'key': 'user_input', 'tab': 'dialog_input', 'type': 'var', 'label': '用户输入内容', 'global': 'key'}, {'key': 'form_input', 'tab': 'form_input', 'type': 'form', 'label': '+ 添加表单项', 'value': [], 'global': 'item:form_input'}, {'key': 'dialog_files_content', 'tab': 'dialog_input', 'type': 'var', 'label': '上传文件内容', 'global': 'key'}, {'key': 'dialog_files_content_size', 'min': 0, 'tab': 'dialog_input', 'type': 'char_number', 'label': '文件内容长度上限', 'value': 15000}, {'key': 'dialog_file_accept', 'tab': 'dialog_input', 'type': 'select_fileaccept', 'label': '上传文件类型', 'value': 'all'}, {'key': 'dialog_image_files', 'tab': 'dialog_input', 'help': '提取上传文件中的图片文件，当助手或大模型节点使用多模态大模型时，可传入此图片。', 'type': 'var', 'label': '上传图片文件', 'global': 'key'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 151}, 'position': {'x': 947, 'y': 85}, 'selected': False}, {'id': 'agent_1d0c8', 'data': {'v': 2, 'id': 'agent_1d0c8', 'tab': {'value': 'single', 'options': [{'key': 'single', 'label': '单次运行'}, {'key': 'batch', 'label': '批量运行'}]}, 'name': '多轮对话助手', 'type': 'agent', 'expand': True, 'description': 'AI 自主进行任务规划，选择合适的知识库或工具进行调用。', 'group_params': [{'params': [{'key': 'batch_variable', 'tab': 'batch', 'help': '选择需要批处理的变量，将会多次运行本节点，每次运行时从选择的变量中取一项赋值给batch_variable进行处理。', 'type': 'user_question', 'label': '批处理变量', 'value': [], 'global': 'self', 'linkage': 'output', 'required': True, 'placeholder': '请选择批处理变量'}]}, {'name': '模型设置', 'params': [{'key': 'model_id', 'type': 'agent_model', 'label': '模型', 'value': 2, 'required': True, 'placeholder': '请选择模型'}, {'key': 'temperature', 'step': 0.1, 'type': 'slide', 'label': '温度', 'scope': [0, 2], 'value': 0.7}]}, {'name': '提示词', 'params': [{'key': 'system_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '系统提示词', 'value': '你是一个熟悉各行各业的 AI 助理，善于根据用户需求，适时调用合适的工具来回答用户问题', 'required': True, 'placeholder': '助手画像'}, {'key': 'user_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '用户提示词', 'value': '{{#input_2775b.user_input#}}', 'varZh': {'input_2775b.user_input': '输入/user_input'}, 'required': True, 'placeholder': '用户消息内容'}, {'key': 'chat_history_flag', 'help': '带入模型上下文的历史消息条数，为 0 时代表不包含上下文信息。', 'step': 1, 'type': 'slide_switch', 'label': '历史聊天记录', 'scope': [0, 100], 'value': {'flag': True, 'value': 50}}, {'key': 'image_prompt', 'help': '当使用多模态大模型时，可通过此功能传入图片，结合图像内容进行问答', 'type': 'image_prompt', 'label': '视觉', 'value': ''}]}, {'name': '知识库', 'params': [{'key': 'knowledge_id', 'type': 'knowledge_select_multi', 'label': '检索知识库范围', 'value': {'type': 'knowledge', 'value': []}, 'placeholder': '请选择知识库'}]}, {'name': '数据库', 'params': [{'key': 'sql_agent', 'type': 'sql_config', 'value': {'open': False, 'db_name': '', 'db_address': '', 'db_password': '', 'db_username': ''}}]}, {'name': '工具', 'params': [{'key': 'tool_list', 'type': 'add_tool', 'label': '+ 添加工具', 'value': []}]}, {'name': '输出', 'params': [{'key': 'output_user', 'help': '一般在问答等场景开启，文档审核、报告生成等场景可关闭。', 'type': 'switch', 'label': '将输出结果展示在会话中', 'value': True}, {'key': 'output', 'type': 'var', 'label': '输出变量', 'value': [], 'global': 'code:value.map(el => ({ label: el.label, value: el.key }))'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 1207}, 'position': {'x': 1416.1256404922672, 'y': 89.69476666183581}, 'selected': True}], 'edges': [{'id': 'xy-edge__start_553b9right_handle-input_2775bleft_handle', 'type': 'customEdge', 'source': 'start_553b9', 'target': 'input_2775b', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__input_2775bright_handle-agent_1d0c8left_handle', 'type': 'customEdge', 'source': 'input_2775b', 'target': 'agent_1d0c8', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__agent_1d0c8right_handle-input_2775bleft_handle', 'type': 'customEdge', 'source': 'agent_1d0c8', 'target': 'input_2775b', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}], 'viewport': {'x': 186.00521793194332, 'y': -0.22168128703947332, 'zoom': 0.6371751491016153}}}
[2025-08-22 15:07:47.447898] [INFO process-12860-26392 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:07:47.503973] [INFO process-12860-23748 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:07:47.540021] [INFO process-12860-32336 bisheng.api.services.audit_log:109] - trace=9d48c9568126477d8d8560c8a2df6be1 act=create_chat_workflow user=<EMAIL> ip=127.0.0.1 flow=ac8b23aed25947908ac042dc5b5f1594
[2025-08-22 15:07:48.514283] [INFO process-12860-26392 bisheng.utils.threadpool:67] - trace=2 async_task_added fun=consume_message args=
[2025-08-22 15:07:48.528963] [INFO process-12860-23748 bisheng.utils.threadpool:67] - trace=9d48c9568126477d8d8560c8a2df6be1 async_task_added fun=wrapper_task args=9d48c9568126477d8d8560c8a2df6be1
[2025-08-22 15:07:55.893902] [INFO process-12860-26940 bisheng.chat.clients.base:68] - trace=1 client_id=b27d7a70849649c5a402da24171ff1f1 trace_id=765bb59d4da6483da71527b5d88a0269 message={'action': 'input', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'chat_id': 'bfd40c7fc538b3d86925d91c60c18fb4', 'data': {'input_2775b': {'data': {'user_input': '你好，请问你知道小米手机吗？', 'dialog_files_content': []}, 'message': '你好，请问你知道小米手机吗？', 'message_id': 40, 'category': 'question', 'extra': '', 'source': 0}}}
[2025-08-22 15:07:55.915314] [INFO process-12860-9920 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:07:55.930355] [INFO process-12860-1672 bisheng.chat.clients.workflow_client:182] - trace=765bb59d4da6483da71527b5d88a0269 get user input: {'input_2775b': {'data': {'user_input': '你好，请问你知道小米手机吗？', 'dialog_files_content': []}, 'message': '你好，请问你知道小米手机吗？', 'message_id': 40, 'category': 'question', 'extra': '', 'source': 0}}
[2025-08-22 15:07:56.940085] [INFO process-12860-9920 bisheng.utils.threadpool:67] - trace=765bb59d4da6483da71527b5d88a0269 async_task_added fun=wrapper_task args=765bb59d4da6483da71527b5d88a0269
[2025-08-22 15:08:13.491123] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=4915ec935a004ea2a11d909f943d7568 POST /api/v1/user/logout
[2025-08-22 15:08:13.509336] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=4915ec935a004ea2a11d909f943d7568 POST /api/v1/user/logout 201 timecost=19.21
[2025-08-22 15:08:13.533108] [INFO process-12860-26940 bisheng.chat.manager:243] - trace=1 act=rcv_client_disconnect (1005, None)
[2025-08-22 15:08:13.542312] [INFO process-12860-26940 bisheng.chat.manager:181] - trace=1 close_client client_key=b27d7a70849649c5a402da24171ff1f1
[2025-08-22 15:08:13.549901] [ERROR process-12860-26940 bisheng.chat.manager:194] - trace=1 Unexpected ASGI message 'websocket.close', after sending 'websocket.close'.
[2025-08-22 15:08:13.558882] [WARNING process-12860-26940 bisheng.chat.manager:179] - trace=1 close_client client_key=b27d7a70849649c5a402da24171ff1f1 not in active_clients
[2025-08-22 15:08:13.566240] [ERROR process-12860-30524 bisheng.chat.clients.base:61] - trace=2 consume_message error {'is_bot': 1, 'message': '', 'type': 'close', 'category': 'processing', 'intermediate_steps': '', 'files': [], 'user_id': 1, 'message_id': None, 'source': 0, 'sender': None, 'receiver': None, 'liked': 0, 'extra': '{"client_key": "b27d7a70849649c5a402da24171ff1f1"}', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'chat_id': 'bfd40c7fc538b3d86925d91c60c18fb4'} error: Cannot call "send" once a close message has been sent.
[2025-08-22 15:08:13.753777] [INFO process-12860-26940 bisheng.utils.http_middleware:21] - trace=aa3fe3023350457f8b6a2dd20573bb39 GET /api/v1/user/get_captcha
[2025-08-22 15:08:13.784833] [INFO process-12860-26940 bisheng.api.v1.user:673] - trace=aa3fe3023350457f8b6a2dd20573bb39 get_captcha captcha_char=9Fhw
[2025-08-22 15:08:13.839493] [INFO process-12860-26940 bisheng.utils.http_middleware:24] - trace=aa3fe3023350457f8b6a2dd20573bb39 GET /api/v1/user/get_captcha 200 timecost=85.716
[2025-08-22 15:08:35.727887] [INFO process-12860-26940 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=websocket_send_json_415d4554a76c4c9bbcd20721e6891442 task=<Future at 0x271dcd562c0 state=finished returned Future> res=True
[2025-08-22 15:08:35.734088] [INFO process-12860-26940 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=e9e5c2ed9c1f472195040306bc8c3698 task=<Future at 0x271dce91960 state=finished returned Future> res=False
[2025-08-22 15:08:35.740524] [INFO process-12860-26940 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=1e1591be54df40dbafc885c39b0cfca8 task=<Future at 0x271dcd541c0 state=finished returned Future> res=False
[2025-08-22 15:08:35.746485] [INFO process-12860-26940 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=425484a71d614dc39b03c4199a26abc1 task=<Future at 0x271dd097430 state=finished returned Future> res=False
[2025-08-22 15:08:35.752605] [INFO process-12860-26940 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=websocket_send_json_4774d1a912af4c4db68acce7904d403d task=<Future at 0x271dd1ea980 state=finished returned Future> res=False
[2025-08-22 15:08:35.758140] [INFO process-12860-26940 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=109dd73b188341d0889480b1c86189b4 task=<Future at 0x271dd1ebf10 state=finished returned Future> res=False
[2025-08-22 15:08:35.764670] [INFO process-12860-26940 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=991360c033c84449b35b04a6f6104e97 task=<Future at 0x271dd1eab90 state=finished returned Future> res=False
[2025-08-22 15:08:35.771675] [INFO process-12860-26940 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=5fc9a00c1e454e0fb851534db646c3f5 task=<Future at 0x271dd1fc9a0 state=finished returned Future> res=False
[2025-08-22 15:08:35.777002] [INFO process-12860-26940 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=920f2eee38134951a1bbd434b6593ee1 task=<Future at 0x271dd2665c0 state=finished returned Future> res=False
[2025-08-22 15:08:35.783003] [INFO process-12860-26940 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=websocket_send_json_8e3e10d9c8d24d3dbde59e2370b0ee9a task=<Future at 0x271dd29aa70 state=finished returned Future> res=False
[2025-08-22 15:08:35.789054] [INFO process-12860-26940 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=aa9f294a397b44daa84a652b7198ac3e task=<Future at 0x271dd264cd0 state=finished returned Future> res=False
[2025-08-22 15:08:35.795021] [INFO process-12860-26940 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=b03d15b59ee84dd8967fdef846206186 task=<Future at 0x271dd1e9ba0 state=finished returned Future> res=False
[2025-08-22 15:08:35.800040] [INFO process-12860-26940 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=websocket_send_json_b27d7a70849649c5a402da24171ff1f1 task=<Future at 0x271dd2d55a0 state=finished returned Future> res=False
[2025-08-22 15:08:35.805874] [INFO process-12860-26940 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=9d48c9568126477d8d8560c8a2df6be1 task=<Future at 0x271dd2b9bd0 state=finished returned Future> res=False
[2025-08-22 15:08:35.810885] [INFO process-12860-26940 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=765bb59d4da6483da71527b5d88a0269 task=<Future at 0x271dd1fdc30 state=finished returned Future> res=False
[2025-08-22 15:09:42.818830] [INFO process-31596-28760 datasets.config:58] - trace=1 PyTorch version 2.8.0 available.
[2025-08-22 15:09:49.511289] [INFO process-31596-28760 bisheng.interface.utils:92] - trace=1 LLM caching setup with InMemoryCache
[2025-08-22 15:09:51.444131] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=aa6d0149631c4ec684bafe0dd28ca2a3 GET /api/v1/user/public_key
[2025-08-22 15:09:51.513445] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=aa6d0149631c4ec684bafe0dd28ca2a3 GET /api/v1/user/public_key 200 timecost=69.314
[2025-08-22 15:09:51.838547] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=fb443f02be6f4a8a9566931940fc3282 POST /api/v1/user/login
[2025-08-22 15:09:51.843605] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=4ec5d3250fcf4079a698c4483196053e GET /api/v1/user/get_captcha
[2025-08-22 15:09:51.872098] [INFO process-31596-28760 bisheng.api.v1.user:673] - trace=4ec5d3250fcf4079a698c4483196053e get_captcha captcha_char=FdAv
[2025-08-22 15:09:51.929955] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=4ec5d3250fcf4079a698c4483196053e GET /api/v1/user/get_captcha 200 timecost=86.35
[2025-08-22 15:09:52.030797] [INFO process-31596-28760 bisheng.api.services.audit_log:399] - trace=fb443f02be6f4a8a9566931940fc3282 act=user_login user=<EMAIL> ip=127.0.0.1 user_id=1
[2025-08-22 15:09:52.064644] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=fb443f02be6f4a8a9566931940fc3282 POST /api/v1/user/login 200 timecost=226.097
[2025-08-22 15:09:52.181588] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=fe9a2b1684a24855b5021ecf61ad51dc GET /api/v1/web/config
[2025-08-22 15:09:52.199219] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=fe9a2b1684a24855b5021ecf61ad51dc GET /api/v1/web/config 200 timecost=17.631
[2025-08-22 15:09:54.757910] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=1af41b352f204dafb531a2e561480efc GET /api/v1/user/info
[2025-08-22 15:09:54.769104] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=227b30e5e21347a880bcfea20f2b0e33 GET /api/v1/env
[2025-08-22 15:09:54.779103] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=400b3abf86de41099f99a3c4a280287f GET /api/v1/all
[2025-08-22 15:09:54.861174] [INFO process-31596-3556 bisheng.interface.custom.utils:343] - trace=400b3abf86de41099f99a3c4a280287f Building custom components from ['D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\components']
[2025-08-22 15:09:55.176753] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=1af41b352f204dafb531a2e561480efc GET /api/v1/user/info 200 timecost=418.844
[2025-08-22 15:09:55.203681] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=227b30e5e21347a880bcfea20f2b0e33 GET /api/v1/env 200 timecost=434.577
[2025-08-22 15:09:55.231621] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=b1270414e9114fab95df496e1c156b77 GET /api/v1/component
[2025-08-22 15:09:55.336899] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=6df890f324ab457cb030d2d964129219 GET /api/v1/tag
[2025-08-22 15:09:55.381130] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=f3cf5de54dc14969b88f8aef85d9bd35 GET /api/v1/tag/home
[2025-08-22 15:09:55.975972] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=b1270414e9114fab95df496e1c156b77 GET /api/v1/component 200 timecost=744.35
[2025-08-22 15:09:56.070245] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=37e06bdc52bd4c1f9deebd2ddb703493 GET /api/v1/workstation/config
[2025-08-22 15:09:56.165900] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=4eeda6c722e3484db720132258b4e14d GET /api/v1/chat/list
[2025-08-22 15:09:56.308408] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=6df890f324ab457cb030d2d964129219 GET /api/v1/tag 200 timecost=971.509
[2025-08-22 15:09:56.318832] [INFO process-31596-3556 bisheng.interface.custom.utils:354] - trace=400b3abf86de41099f99a3c4a280287f Loading 1 component(s) from category custom_components
[2025-08-22 15:09:56.415738] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=f3cf5de54dc14969b88f8aef85d9bd35 GET /api/v1/tag/home 200 timecost=1034.609
[2025-08-22 15:09:56.448545] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=978cb6d9182441f98a2616e7d047b5e2 GET /api/v1/chat/online
[2025-08-22 15:09:57.199978] [ERROR process-31596-3556 __main__:33] - trace=1 GET http://127.0.0.1:7860/api/v1/workstation/config 'NoneType' object has no attribute 'model_dump'
[2025-08-22 15:09:57.212506] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=400b3abf86de41099f99a3c4a280287f GET /api/v1/all 200 timecost=2433.403
[2025-08-22 15:09:57.255697] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=4eeda6c722e3484db720132258b4e14d GET /api/v1/chat/list 200 timecost=1089.797
[2025-08-22 15:09:57.277898] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=c260b3980c60443f8a80cf99b56224f4 GET /api/v1/tag
[2025-08-22 15:09:57.380704] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=c260b3980c60443f8a80cf99b56224f4 GET /api/v1/tag 200 timecost=102.805
[2025-08-22 15:09:57.420115] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=978cb6d9182441f98a2616e7d047b5e2 GET /api/v1/chat/online 200 timecost=971.57
[2025-08-22 15:09:58.059807] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=da2a1bc721d348099d8ff154fea09edb GET /api/v1/chat/online
[2025-08-22 15:09:58.222201] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=da2a1bc721d348099d8ff154fea09edb GET /api/v1/chat/online 200 timecost=162.394
[2025-08-22 15:09:58.982700] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=672a11cebe434db5b4fc8992eff7034e GET /api/v1/flows/ac8b23aed25947908ac042dc5b5f1594
[2025-08-22 15:09:59.074625] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=672a11cebe434db5b4fc8992eff7034e GET /api/v1/flows/ac8b23aed25947908ac042dc5b5f1594 200 timecost=91.925
[2025-08-22 15:09:59.250125] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=603bb1821d9f48c7aface202a88b0807 GET /api/v1/chat/history
[2025-08-22 15:09:59.315007] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=603bb1821d9f48c7aface202a88b0807 GET /api/v1/chat/history 200 timecost=64.881
[2025-08-22 15:09:59.811326] [INFO process-31596-27860 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:09:59.827726] [INFO process-31596-28760 bisheng.chat.clients.base:68] - trace=1 client_id=83588f0ac6e14746879ada3e0510b3eb trace_id=89e448368b924a04a0c917c2f73f3f59 message={'action': 'init_data', 'chat_id': 'f18a7c85fc945bc84a478fe4facb0776', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'data': {'status': 2, 'description': '支持添加知识库和工具', 'user_id': 1, 'guide_word': None, 'create_time': '2025-08-21T10:54:24', 'logo': '', 'name': '多轮对话-479e3', 'flow_type': 10, 'update_time': '2025-08-21T10:58:11', 'id': 'ac8b23aed25947908ac042dc5b5f1594', 'nodes': [{'id': 'start_553b9', 'data': {'v': 1, 'id': 'start_553b9', 'name': '开始', 'type': 'start', 'description': '工作流运行的起始节点。', 'group_params': [{'name': '开场引导', 'params': [{'key': 'guide_word', 'type': 'textarea', 'label': '开场白', 'value': '您好，请问想聊些什么呢？', 'placeholder': '每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。'}, {'key': 'guide_question', 'help': '为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', 'type': 'input_list', 'label': '引导问题', 'value': ['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], 'placeholder': '请输入引导问题'}]}, {'name': '全局变量', 'params': [{'key': 'current_time', 'type': 'var', 'label': '当前时间', 'value': '', 'global': 'key'}, {'key': 'chat_history', 'type': 'chat_history_num', 'value': 10, 'global': 'key'}, {'key': 'preset_question', 'help': '适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', 'type': 'input_list', 'label': '预置问题列表', 'value': [{'key': 'f4cbae', 'value': ''}], 'global': 'item:input_list', 'placeholder': '输入批量预置问题'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 705}, 'position': {'x': 491, 'y': 63.125}, 'selected': False}, {'id': 'input_2775b', 'data': {'v': 2, 'id': 'input_2775b', 'tab': {'value': 'dialog_input', 'options': [{'key': 'dialog_input', 'help': '接收用户从对话框输入的内容。!(input)', 'label': '对话框输入'}, {'key': 'form_input', 'help': '将会在用户会话界面弹出一个表单，接收用户从表单提交的内容。!(form)', 'label': '表单输入'}]}, 'name': '输入', 'type': 'input', 'expand': False, 'description': '接收用户在会话页面的输入，支持 2 种形式：对话框输入，表单输入。', 'group_params': [{'name': '', 'params': [{'key': 'user_input', 'tab': 'dialog_input', 'type': 'var', 'label': '用户输入内容', 'global': 'key'}, {'key': 'form_input', 'tab': 'form_input', 'type': 'form', 'label': '+ 添加表单项', 'value': [], 'global': 'item:form_input'}, {'key': 'dialog_files_content', 'tab': 'dialog_input', 'type': 'var', 'label': '上传文件内容', 'global': 'key'}, {'key': 'dialog_files_content_size', 'min': 0, 'tab': 'dialog_input', 'type': 'char_number', 'label': '文件内容长度上限', 'value': 15000}, {'key': 'dialog_file_accept', 'tab': 'dialog_input', 'type': 'select_fileaccept', 'label': '上传文件类型', 'value': 'all'}, {'key': 'dialog_image_files', 'tab': 'dialog_input', 'help': '提取上传文件中的图片文件，当助手或大模型节点使用多模态大模型时，可传入此图片。', 'type': 'var', 'label': '上传图片文件', 'global': 'key'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 151}, 'position': {'x': 947, 'y': 85}, 'selected': False}, {'id': 'agent_1d0c8', 'data': {'v': 2, 'id': 'agent_1d0c8', 'tab': {'value': 'single', 'options': [{'key': 'single', 'label': '单次运行'}, {'key': 'batch', 'label': '批量运行'}]}, 'name': '多轮对话助手', 'type': 'agent', 'expand': True, 'description': 'AI 自主进行任务规划，选择合适的知识库或工具进行调用。', 'group_params': [{'params': [{'key': 'batch_variable', 'tab': 'batch', 'help': '选择需要批处理的变量，将会多次运行本节点，每次运行时从选择的变量中取一项赋值给batch_variable进行处理。', 'type': 'user_question', 'label': '批处理变量', 'value': [], 'global': 'self', 'linkage': 'output', 'required': True, 'placeholder': '请选择批处理变量'}]}, {'name': '模型设置', 'params': [{'key': 'model_id', 'type': 'agent_model', 'label': '模型', 'value': 2, 'required': True, 'placeholder': '请选择模型'}, {'key': 'temperature', 'step': 0.1, 'type': 'slide', 'label': '温度', 'scope': [0, 2], 'value': 0.7}]}, {'name': '提示词', 'params': [{'key': 'system_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '系统提示词', 'value': '你是一个熟悉各行各业的 AI 助理，善于根据用户需求，适时调用合适的工具来回答用户问题', 'required': True, 'placeholder': '助手画像'}, {'key': 'user_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '用户提示词', 'value': '{{#input_2775b.user_input#}}', 'varZh': {'input_2775b.user_input': '输入/user_input'}, 'required': True, 'placeholder': '用户消息内容'}, {'key': 'chat_history_flag', 'help': '带入模型上下文的历史消息条数，为 0 时代表不包含上下文信息。', 'step': 1, 'type': 'slide_switch', 'label': '历史聊天记录', 'scope': [0, 100], 'value': {'flag': True, 'value': 50}}, {'key': 'image_prompt', 'help': '当使用多模态大模型时，可通过此功能传入图片，结合图像内容进行问答', 'type': 'image_prompt', 'label': '视觉', 'value': ''}]}, {'name': '知识库', 'params': [{'key': 'knowledge_id', 'type': 'knowledge_select_multi', 'label': '检索知识库范围', 'value': {'type': 'knowledge', 'value': []}, 'placeholder': '请选择知识库'}]}, {'name': '数据库', 'params': [{'key': 'sql_agent', 'type': 'sql_config', 'value': {'open': False, 'db_name': '', 'db_address': '', 'db_password': '', 'db_username': ''}}]}, {'name': '工具', 'params': [{'key': 'tool_list', 'type': 'add_tool', 'label': '+ 添加工具', 'value': []}]}, {'name': '输出', 'params': [{'key': 'output_user', 'help': '一般在问答等场景开启，文档审核、报告生成等场景可关闭。', 'type': 'switch', 'label': '将输出结果展示在会话中', 'value': True}, {'key': 'output', 'type': 'var', 'label': '输出变量', 'value': [], 'global': 'code:value.map(el => ({ label: el.label, value: el.key }))'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 1207}, 'position': {'x': 1416.1256404922672, 'y': 89.69476666183581}, 'selected': True}], 'edges': [{'id': 'xy-edge__start_553b9right_handle-input_2775bleft_handle', 'type': 'customEdge', 'source': 'start_553b9', 'target': 'input_2775b', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__input_2775bright_handle-agent_1d0c8left_handle', 'type': 'customEdge', 'source': 'input_2775b', 'target': 'agent_1d0c8', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__agent_1d0c8right_handle-input_2775bleft_handle', 'type': 'customEdge', 'source': 'agent_1d0c8', 'target': 'input_2775b', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}], 'viewport': {'x': 186.00521793194332, 'y': -0.22168128703947332, 'zoom': 0.6371751491016153}}}
[2025-08-22 15:09:59.981853] [INFO process-31596-32420 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:10:00.018268] [INFO process-31596-31388 bisheng.api.services.audit_log:109] - trace=89e448368b924a04a0c917c2f73f3f59 act=create_chat_workflow user=<EMAIL> ip=127.0.0.1 flow=ac8b23aed25947908ac042dc5b5f1594
[2025-08-22 15:10:00.855987] [INFO process-31596-27860 bisheng.utils.threadpool:67] - trace=2 async_task_added fun=consume_message args=
[2025-08-22 15:10:00.996137] [INFO process-31596-32420 bisheng.utils.threadpool:67] - trace=89e448368b924a04a0c917c2f73f3f59 async_task_added fun=wrapper_task args=89e448368b924a04a0c917c2f73f3f59
[2025-08-22 15:10:16.963553] [INFO process-31596-28760 bisheng.chat.clients.base:68] - trace=1 client_id=83588f0ac6e14746879ada3e0510b3eb trace_id=b5e008e0044c4085a3218195ffbf8b58 message={'action': 'input', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'chat_id': 'f18a7c85fc945bc84a478fe4facb0776', 'data': {'input_2775b': {'data': {'user_input': '你好，请问你知道小米手机吗？？？？？', 'dialog_files_content': []}, 'message': '你好，请问你知道小米手机吗？？？？？', 'message_id': 42, 'category': 'question', 'extra': '', 'source': 0}}}
[2025-08-22 15:10:16.984309] [INFO process-31596-27860 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:10:17.005634] [INFO process-31596-31416 bisheng.chat.clients.workflow_client:182] - trace=b5e008e0044c4085a3218195ffbf8b58 get user input: {'input_2775b': {'data': {'user_input': '你好，请问你知道小米手机吗？？？？？', 'dialog_files_content': []}, 'message': '你好，请问你知道小米手机吗？？？？？', 'message_id': 42, 'category': 'question', 'extra': '', 'source': 0}}
[2025-08-22 15:10:18.002566] [INFO process-31596-27860 bisheng.utils.threadpool:67] - trace=b5e008e0044c4085a3218195ffbf8b58 async_task_added fun=wrapper_task args=b5e008e0044c4085a3218195ffbf8b58
[2025-08-22 15:10:18.394839] [ERROR process-31596-28760 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=93>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=93>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000001BE57AEF010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 64631, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000001BE57AEE050>
              └ <__main__.PyDB object at 0x000001BE57AC5540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000001BE57AEE0E0>
           └ <__main__.PyDB object at 0x000001BE57AC5540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000001BE574CEF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x000001BE17E60640>
    │       └ <function run at 0x000001BE17121090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000001BE17120AF0>
    └ <uvicorn.server.Server object at 0x000001BE1EEFDA50>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001BE17120B80>
           │       │   └ <uvicorn.server.Server object at 0x000001BE1EEFDA50>
           │       └ <function _patch_asyncio.<locals>.run at 0x000001BE57B357E0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x000001BE57B36290>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x000001BE57B36320>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001BE579EFA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=93>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=93>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=93>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=93>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=15>
           │    └ <_ProactorSocketTransport fd=8888 read=<_OverlappedFuture pending overlapped=<pending, 0x1be202fb5b0> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=93>

AssertionError: assert f is self._write_fut
[2025-08-22 15:10:19.389414] [ERROR process-31596-28760 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=15>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=15>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000001BE57AEF010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 64631, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000001BE57AEE050>
              └ <__main__.PyDB object at 0x000001BE57AC5540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000001BE57AEE0E0>
           └ <__main__.PyDB object at 0x000001BE57AC5540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000001BE574CEF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x000001BE17E60640>
    │       └ <function run at 0x000001BE17121090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000001BE17120AF0>
    └ <uvicorn.server.Server object at 0x000001BE1EEFDA50>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001BE17120B80>
           │       │   └ <uvicorn.server.Server object at 0x000001BE1EEFDA50>
           │       └ <function _patch_asyncio.<locals>.run at 0x000001BE57B357E0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x000001BE57B36290>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x000001BE57B36320>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001BE579EFA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=15>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=15>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=15>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=15>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=27>
           │    └ <_ProactorSocketTransport fd=8888 read=<_OverlappedFuture pending overlapped=<pending, 0x1be202fb5b0> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=15>

AssertionError: assert f is self._write_fut
[2025-08-22 15:10:21.607993] [ERROR process-31596-28760 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=53>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=53>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000001BE57AEF010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 64631, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000001BE57AEE050>
              └ <__main__.PyDB object at 0x000001BE57AC5540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000001BE57AEE0E0>
           └ <__main__.PyDB object at 0x000001BE57AC5540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000001BE574CEF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x000001BE17E60640>
    │       └ <function run at 0x000001BE17121090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000001BE17120AF0>
    └ <uvicorn.server.Server object at 0x000001BE1EEFDA50>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001BE17120B80>
           │       │   └ <uvicorn.server.Server object at 0x000001BE1EEFDA50>
           │       └ <function _patch_asyncio.<locals>.run at 0x000001BE57B357E0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x000001BE57B36290>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x000001BE57B36320>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001BE579EFA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=53>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=53>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=53>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=53>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=10>
           │    └ <_ProactorSocketTransport fd=8888 read=<_OverlappedFuture pending overlapped=<pending, 0x1be20731870> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=53>

AssertionError: assert f is self._write_fut
[2025-08-22 15:10:56.041634] [INFO process-31596-28760 bisheng.chat.clients.base:68] - trace=1 client_id=83588f0ac6e14746879ada3e0510b3eb trace_id=73116ab4d6834a0cbbeb19887a638fba message={'action': 'input', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'chat_id': 'f18a7c85fc945bc84a478fe4facb0776', 'data': {'input_2775b': {'data': {'user_input': '你好，请问你知道小米手机吗？？？？？', 'dialog_files_content': []}, 'message': '你好，请问你知道小米手机吗？？？？？', 'message_id': 45, 'category': 'question', 'extra': '', 'source': 0}}}
[2025-08-22 15:10:56.061603] [INFO process-31596-21288 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:10:56.080407] [INFO process-31596-25204 bisheng.chat.clients.workflow_client:182] - trace=73116ab4d6834a0cbbeb19887a638fba get user input: {'input_2775b': {'data': {'user_input': '你好，请问你知道小米手机吗？？？？？', 'dialog_files_content': []}, 'message': '你好，请问你知道小米手机吗？？？？？', 'message_id': 45, 'category': 'question', 'extra': '', 'source': 0}}
[2025-08-22 15:10:57.085539] [INFO process-31596-21288 bisheng.utils.threadpool:67] - trace=73116ab4d6834a0cbbeb19887a638fba async_task_added fun=wrapper_task args=73116ab4d6834a0cbbeb19887a638fba
[2025-08-22 15:11:12.082214] [INFO process-31596-28760 bisheng.chat.clients.base:68] - trace=1 client_id=83588f0ac6e14746879ada3e0510b3eb trace_id=b673212de96644a2b816b5c50a02d2b1 message={'action': 'stop'}
[2025-08-22 15:11:12.120851] [INFO process-31596-28760 bisheng.chat.clients.base:68] - trace=1 client_id=83588f0ac6e14746879ada3e0510b3eb trace_id=fec75b474e78428d96b65475fbf1a6c0 message={'action': 'init_data', 'chat_id': 'f18a7c85fc945bc84a478fe4facb0776', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'data': {'status': 2, 'description': '支持添加知识库和工具', 'user_id': 1, 'guide_word': None, 'create_time': '2025-08-21T10:54:24', 'logo': '', 'name': '多轮对话-479e3', 'flow_type': 10, 'update_time': '2025-08-21T10:58:11', 'id': 'ac8b23aed25947908ac042dc5b5f1594', 'nodes': [{'id': 'start_553b9', 'data': {'v': 1, 'id': 'start_553b9', 'name': '开始', 'type': 'start', 'description': '工作流运行的起始节点。', 'group_params': [{'name': '开场引导', 'params': [{'key': 'guide_word', 'type': 'textarea', 'label': '开场白', 'value': '您好，请问想聊些什么呢？', 'placeholder': '每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。'}, {'key': 'guide_question', 'help': '为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', 'type': 'input_list', 'label': '引导问题', 'value': ['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], 'placeholder': '请输入引导问题'}]}, {'name': '全局变量', 'params': [{'key': 'current_time', 'type': 'var', 'label': '当前时间', 'value': '', 'global': 'key'}, {'key': 'chat_history', 'type': 'chat_history_num', 'value': 10, 'global': 'key'}, {'key': 'preset_question', 'help': '适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', 'type': 'input_list', 'label': '预置问题列表', 'value': [{'key': 'f4cbae', 'value': ''}], 'global': 'item:input_list', 'placeholder': '输入批量预置问题'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 705}, 'position': {'x': 491, 'y': 63.125}, 'selected': False}, {'id': 'input_2775b', 'data': {'v': 2, 'id': 'input_2775b', 'tab': {'value': 'dialog_input', 'options': [{'key': 'dialog_input', 'help': '接收用户从对话框输入的内容。!(input)', 'label': '对话框输入'}, {'key': 'form_input', 'help': '将会在用户会话界面弹出一个表单，接收用户从表单提交的内容。!(form)', 'label': '表单输入'}]}, 'name': '输入', 'type': 'input', 'expand': False, 'description': '接收用户在会话页面的输入，支持 2 种形式：对话框输入，表单输入。', 'group_params': [{'name': '', 'params': [{'key': 'user_input', 'tab': 'dialog_input', 'type': 'var', 'label': '用户输入内容', 'global': 'key'}, {'key': 'form_input', 'tab': 'form_input', 'type': 'form', 'label': '+ 添加表单项', 'value': [], 'global': 'item:form_input'}, {'key': 'dialog_files_content', 'tab': 'dialog_input', 'type': 'var', 'label': '上传文件内容', 'global': 'key'}, {'key': 'dialog_files_content_size', 'min': 0, 'tab': 'dialog_input', 'type': 'char_number', 'label': '文件内容长度上限', 'value': 15000}, {'key': 'dialog_file_accept', 'tab': 'dialog_input', 'type': 'select_fileaccept', 'label': '上传文件类型', 'value': 'all'}, {'key': 'dialog_image_files', 'tab': 'dialog_input', 'help': '提取上传文件中的图片文件，当助手或大模型节点使用多模态大模型时，可传入此图片。', 'type': 'var', 'label': '上传图片文件', 'global': 'key'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 151}, 'position': {'x': 947, 'y': 85}, 'selected': False}, {'id': 'agent_1d0c8', 'data': {'v': 2, 'id': 'agent_1d0c8', 'tab': {'value': 'single', 'options': [{'key': 'single', 'label': '单次运行'}, {'key': 'batch', 'label': '批量运行'}]}, 'name': '多轮对话助手', 'type': 'agent', 'expand': True, 'description': 'AI 自主进行任务规划，选择合适的知识库或工具进行调用。', 'group_params': [{'params': [{'key': 'batch_variable', 'tab': 'batch', 'help': '选择需要批处理的变量，将会多次运行本节点，每次运行时从选择的变量中取一项赋值给batch_variable进行处理。', 'type': 'user_question', 'label': '批处理变量', 'value': [], 'global': 'self', 'linkage': 'output', 'required': True, 'placeholder': '请选择批处理变量'}]}, {'name': '模型设置', 'params': [{'key': 'model_id', 'type': 'agent_model', 'label': '模型', 'value': 2, 'required': True, 'placeholder': '请选择模型'}, {'key': 'temperature', 'step': 0.1, 'type': 'slide', 'label': '温度', 'scope': [0, 2], 'value': 0.7}]}, {'name': '提示词', 'params': [{'key': 'system_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '系统提示词', 'value': '你是一个熟悉各行各业的 AI 助理，善于根据用户需求，适时调用合适的工具来回答用户问题', 'required': True, 'placeholder': '助手画像'}, {'key': 'user_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '用户提示词', 'value': '{{#input_2775b.user_input#}}', 'varZh': {'input_2775b.user_input': '输入/user_input'}, 'required': True, 'placeholder': '用户消息内容'}, {'key': 'chat_history_flag', 'help': '带入模型上下文的历史消息条数，为 0 时代表不包含上下文信息。', 'step': 1, 'type': 'slide_switch', 'label': '历史聊天记录', 'scope': [0, 100], 'value': {'flag': True, 'value': 50}}, {'key': 'image_prompt', 'help': '当使用多模态大模型时，可通过此功能传入图片，结合图像内容进行问答', 'type': 'image_prompt', 'label': '视觉', 'value': ''}]}, {'name': '知识库', 'params': [{'key': 'knowledge_id', 'type': 'knowledge_select_multi', 'label': '检索知识库范围', 'value': {'type': 'knowledge', 'value': []}, 'placeholder': '请选择知识库'}]}, {'name': '数据库', 'params': [{'key': 'sql_agent', 'type': 'sql_config', 'value': {'open': False, 'db_name': '', 'db_address': '', 'db_password': '', 'db_username': ''}}]}, {'name': '工具', 'params': [{'key': 'tool_list', 'type': 'add_tool', 'label': '+ 添加工具', 'value': []}]}, {'name': '输出', 'params': [{'key': 'output_user', 'help': '一般在问答等场景开启，文档审核、报告生成等场景可关闭。', 'type': 'switch', 'label': '将输出结果展示在会话中', 'value': True}, {'key': 'output', 'type': 'var', 'label': '输出变量', 'value': [], 'global': 'code:value.map(el => ({ label: el.label, value: el.key }))'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 1207}, 'position': {'x': 1416.1256404922672, 'y': 89.69476666183581}, 'selected': True}], 'edges': [{'id': 'xy-edge__start_553b9right_handle-input_2775bleft_handle', 'type': 'customEdge', 'source': 'start_553b9', 'target': 'input_2775b', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__input_2775bright_handle-agent_1d0c8left_handle', 'type': 'customEdge', 'source': 'input_2775b', 'target': 'agent_1d0c8', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__agent_1d0c8right_handle-input_2775bleft_handle', 'type': 'customEdge', 'source': 'agent_1d0c8', 'target': 'input_2775b', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}], 'viewport': {'x': 186.00521793194332, 'y': -0.22168128703947332, 'zoom': 0.6371751491016153}}}
[2025-08-22 15:11:12.340796] [INFO process-31596-18204 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:11:13.362501] [INFO process-31596-18204 bisheng.utils.threadpool:67] - trace=fec75b474e78428d96b65475fbf1a6c0 async_task_added fun=wrapper_task args=fec75b474e78428d96b65475fbf1a6c0
[2025-08-22 15:11:16.025372] [INFO process-31596-28760 bisheng.chat.clients.base:68] - trace=1 client_id=83588f0ac6e14746879ada3e0510b3eb trace_id=b10158dbdfea4b4a84c754bbd10f81c1 message={'action': 'input', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'chat_id': 'f18a7c85fc945bc84a478fe4facb0776', 'data': {'input_2775b': {'data': {'user_input': '最近关于低空经济的政策', 'dialog_files_content': []}, 'message': '最近关于低空经济的政策', 'message_id': 47, 'category': 'question', 'extra': '', 'source': 0}}}
[2025-08-22 15:11:16.039490] [INFO process-31596-32420 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:11:16.052593] [INFO process-31596-17712 bisheng.chat.clients.workflow_client:182] - trace=b10158dbdfea4b4a84c754bbd10f81c1 get user input: {'input_2775b': {'data': {'user_input': '最近关于低空经济的政策', 'dialog_files_content': []}, 'message': '最近关于低空经济的政策', 'message_id': 47, 'category': 'question', 'extra': '', 'source': 0}}
[2025-08-22 15:11:17.046541] [INFO process-31596-32420 bisheng.utils.threadpool:67] - trace=b10158dbdfea4b4a84c754bbd10f81c1 async_task_added fun=wrapper_task args=b10158dbdfea4b4a84c754bbd10f81c1
[2025-08-22 15:11:18.370470] [ERROR process-31596-28760 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=15>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=15>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000001BE57AEF010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 64631, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000001BE57AEE050>
              └ <__main__.PyDB object at 0x000001BE57AC5540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000001BE57AEE0E0>
           └ <__main__.PyDB object at 0x000001BE57AC5540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000001BE574CEF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x000001BE17E60640>
    │       └ <function run at 0x000001BE17121090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000001BE17120AF0>
    └ <uvicorn.server.Server object at 0x000001BE1EEFDA50>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001BE17120B80>
           │       │   └ <uvicorn.server.Server object at 0x000001BE1EEFDA50>
           │       └ <function _patch_asyncio.<locals>.run at 0x000001BE57B357E0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x000001BE57B36290>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x000001BE57B36320>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001BE579EFA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=15>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=15>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=15>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=15>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=59>
           │    └ <_ProactorSocketTransport fd=8888 read=<_OverlappedFuture pending overlapped=<pending, 0x1be20731870> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=15>

AssertionError: assert f is self._write_fut
[2025-08-22 15:11:19.537828] [ERROR process-31596-28760 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000001BE57AEF010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 64631, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000001BE57AEE050>
              └ <__main__.PyDB object at 0x000001BE57AC5540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000001BE57AEE0E0>
           └ <__main__.PyDB object at 0x000001BE57AC5540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000001BE574CEF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x000001BE17E60640>
    │       └ <function run at 0x000001BE17121090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000001BE17120AF0>
    └ <uvicorn.server.Server object at 0x000001BE1EEFDA50>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001BE17120B80>
           │       │   └ <uvicorn.server.Server object at 0x000001BE1EEFDA50>
           │       └ <function _patch_asyncio.<locals>.run at 0x000001BE57B357E0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x000001BE57B36290>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x000001BE57B36320>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001BE579EFA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=62>
           │    └ <_ProactorSocketTransport fd=8888 read=<_OverlappedFuture pending overlapped=<pending, 0x1be20731870> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=10>

AssertionError: assert f is self._write_fut
[2025-08-22 15:11:21.191285] [ERROR process-31596-28760 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=18>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=18>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000001BE57AEF010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 64631, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000001BE57AEE050>
              └ <__main__.PyDB object at 0x000001BE57AC5540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000001BE57AEE0E0>
           └ <__main__.PyDB object at 0x000001BE57AC5540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000001BE574CEF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x000001BE17E60640>
    │       └ <function run at 0x000001BE17121090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000001BE17120AF0>
    └ <uvicorn.server.Server object at 0x000001BE1EEFDA50>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001BE17120B80>
           │       │   └ <uvicorn.server.Server object at 0x000001BE1EEFDA50>
           │       └ <function _patch_asyncio.<locals>.run at 0x000001BE57B357E0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x000001BE57B36290>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x000001BE57B36320>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001BE579EFA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=18>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=18>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=18>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=18>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=68>
           │    └ <_ProactorSocketTransport fd=8888 read=<_OverlappedFuture pending overlapped=<pending, 0x1be202fb5b0> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=18>

AssertionError: assert f is self._write_fut
[2025-08-22 15:11:24.046747] [ERROR process-31596-28760 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000001BE57AEF010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 64631, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000001BE57AEE050>
              └ <__main__.PyDB object at 0x000001BE57AC5540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000001BE57AEE0E0>
           └ <__main__.PyDB object at 0x000001BE57AC5540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000001BE574CEF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x000001BE17E60640>
    │       └ <function run at 0x000001BE17121090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000001BE17120AF0>
    └ <uvicorn.server.Server object at 0x000001BE1EEFDA50>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001BE17120B80>
           │       │   └ <uvicorn.server.Server object at 0x000001BE1EEFDA50>
           │       └ <function _patch_asyncio.<locals>.run at 0x000001BE57B357E0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x000001BE57B36290>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x000001BE57B36320>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001BE579EFA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=10>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=72>
           │    └ <_ProactorSocketTransport fd=8888 read=<_OverlappedFuture pending overlapped=<pending, 0x1be202fb5b0> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=10>

AssertionError: assert f is self._write_fut
[2025-08-22 15:11:24.373269] [ERROR process-31596-28760 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=16>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=16>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000001BE57AEF010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 64631, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000001BE57AEE050>
              └ <__main__.PyDB object at 0x000001BE57AC5540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000001BE57AEE0E0>
           └ <__main__.PyDB object at 0x000001BE57AC5540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000001BE574CEF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x000001BE17E60640>
    │       └ <function run at 0x000001BE17121090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000001BE17120AF0>
    └ <uvicorn.server.Server object at 0x000001BE1EEFDA50>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001BE17120B80>
           │       │   └ <uvicorn.server.Server object at 0x000001BE1EEFDA50>
           │       └ <function _patch_asyncio.<locals>.run at 0x000001BE57B357E0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x000001BE57B36290>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x000001BE57B36320>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001BE579EFA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=16>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=16>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=16>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=16>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=10>
           │    └ <_ProactorSocketTransport fd=8888 read=<_OverlappedFuture pending overlapped=<pending, 0x1be202fb5b0> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=16>

AssertionError: assert f is self._write_fut
[2025-08-22 15:11:25.266020] [ERROR process-31596-28760 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=42>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=42>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000001BE57AEF010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 64631, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000001BE57AEE050>
              └ <__main__.PyDB object at 0x000001BE57AC5540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000001BE57AEE0E0>
           └ <__main__.PyDB object at 0x000001BE57AC5540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000001BE574CEF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x000001BE17E60640>
    │       └ <function run at 0x000001BE17121090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000001BE17120AF0>
    └ <uvicorn.server.Server object at 0x000001BE1EEFDA50>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001BE17120B80>
           │       │   └ <uvicorn.server.Server object at 0x000001BE1EEFDA50>
           │       └ <function _patch_asyncio.<locals>.run at 0x000001BE57B357E0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x000001BE57B36290>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x000001BE57B36320>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001BE579EFA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=42>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=42>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=42>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=42>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=17>
           │    └ <_ProactorSocketTransport fd=8888 read=<_OverlappedFuture pending overlapped=<pending, 0x1be202fb5b0> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=42>

AssertionError: assert f is self._write_fut
[2025-08-22 15:11:27.884470] [ERROR process-31596-28760 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=15>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=15>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000001BE57AEF010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 64631, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000001BE57AEE050>
              └ <__main__.PyDB object at 0x000001BE57AC5540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000001BE57AEE0E0>
           └ <__main__.PyDB object at 0x000001BE57AC5540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000001BE574CEF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x000001BE17E60640>
    │       └ <function run at 0x000001BE17121090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000001BE17120AF0>
    └ <uvicorn.server.Server object at 0x000001BE1EEFDA50>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001BE17120B80>
           │       │   └ <uvicorn.server.Server object at 0x000001BE1EEFDA50>
           │       └ <function _patch_asyncio.<locals>.run at 0x000001BE57B357E0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x000001BE57B36290>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x000001BE57B36320>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001BE579EFA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=15>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=15>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=15>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=15>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=20>
           │    └ <_ProactorSocketTransport fd=8888 read=<_OverlappedFuture pending overlapped=<pending, 0x1be202fb5b0> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=15>

AssertionError: assert f is self._write_fut
[2025-08-22 15:11:28.396843] [ERROR process-31596-28760 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=44>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=44>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000001BE57AEF010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 64631, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000001BE57AEE050>
              └ <__main__.PyDB object at 0x000001BE57AC5540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000001BE57AEE0E0>
           └ <__main__.PyDB object at 0x000001BE57AC5540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000001BE574CEF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x000001BE17E60640>
    │       └ <function run at 0x000001BE17121090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000001BE17120AF0>
    └ <uvicorn.server.Server object at 0x000001BE1EEFDA50>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001BE17120B80>
           │       │   └ <uvicorn.server.Server object at 0x000001BE1EEFDA50>
           │       └ <function _patch_asyncio.<locals>.run at 0x000001BE57B357E0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x000001BE57B36290>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x000001BE57B36320>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001BE579EFA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=44>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=44>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=44>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=44>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=14>
           │    └ <_ProactorSocketTransport fd=8888 read=<_OverlappedFuture pending overlapped=<pending, 0x1be202fb5b0> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=44>

AssertionError: assert f is self._write_fut
[2025-08-22 15:11:28.926398] [ERROR process-31596-28760 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...ed result=100>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...ed result=100>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000001BE57AEF010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 64631, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000001BE57AEE050>
              └ <__main__.PyDB object at 0x000001BE57AC5540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000001BE57AEE0E0>
           └ <__main__.PyDB object at 0x000001BE57AC5540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000001BE574CEF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x000001BE17E60640>
    │       └ <function run at 0x000001BE17121090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000001BE17120AF0>
    └ <uvicorn.server.Server object at 0x000001BE1EEFDA50>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001BE17120B80>
           │       │   └ <uvicorn.server.Server object at 0x000001BE1EEFDA50>
           │       └ <function _patch_asyncio.<locals>.run at 0x000001BE57B357E0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x000001BE57B36290>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x000001BE57B36320>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001BE579EFA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...ed result=100>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...ed result=100>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...ed result=100>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...ed result=100>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=13>
           │    └ <_ProactorSocketTransport fd=8888 read=<_OverlappedFuture pending overlapped=<pending, 0x1be202fb5b0> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=100>

AssertionError: assert f is self._write_fut
[2025-08-22 15:11:37.605397] [INFO process-31596-28760 bisheng.chat.manager:243] - trace=1 act=rcv_client_disconnect (1005, None)
[2025-08-22 15:11:37.611957] [INFO process-31596-28760 bisheng.chat.manager:181] - trace=1 close_client client_key=83588f0ac6e14746879ada3e0510b3eb
[2025-08-22 15:11:37.618795] [ERROR process-31596-28760 bisheng.chat.manager:194] - trace=1 Unexpected ASGI message 'websocket.close', after sending 'websocket.close'.
[2025-08-22 15:11:37.624616] [WARNING process-31596-28760 bisheng.chat.manager:179] - trace=1 close_client client_key=83588f0ac6e14746879ada3e0510b3eb not in active_clients
[2025-08-22 15:11:37.838085] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=a781ebc64b554abf9a26771ac76ac838 GET /api/v1/skill/template
[2025-08-22 15:11:37.894259] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=a781ebc64b554abf9a26771ac76ac838 GET /api/v1/skill/template 200 timecost=56.174
[2025-08-22 15:11:37.960744] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=accfaa4c01a94794a8e3f56a923b5f38 GET /api/v1/tag
[2025-08-22 15:11:37.998412] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=accfaa4c01a94794a8e3f56a923b5f38 GET /api/v1/tag 200 timecost=37.668
[2025-08-22 15:11:38.244327] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=edf25aaa6ea94d6297eac3edee1ff62a GET /api/v1/workflow/list
[2025-08-22 15:11:38.322051] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=edf25aaa6ea94d6297eac3edee1ff62a GET /api/v1/workflow/list 200 timecost=77.724
[2025-08-22 15:11:38.660592] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=3021c8496545471297c2b380b8079d75 GET /api/v1/skill/template
[2025-08-22 15:11:38.723345] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=3021c8496545471297c2b380b8079d75 GET /api/v1/skill/template 200 timecost=63.262
[2025-08-22 15:11:39.173252] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=d55ac18531134f809a833d1b747ea5ab GET /api/v1/flows/ac8b23aed25947908ac042dc5b5f1594
[2025-08-22 15:11:39.216682] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=d55ac18531134f809a833d1b747ea5ab GET /api/v1/flows/ac8b23aed25947908ac042dc5b5f1594 200 timecost=43.431
[2025-08-22 15:11:39.984089] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=66f4c284248c454abb987062691d0f75 GET /api/v1/workflow/versions
[2025-08-22 15:11:39.993533] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=e38f453b199c4960a6548f9c65cc0fc0 GET /api/v1/assistant/tool_list
[2025-08-22 15:11:40.005547] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=afaa53cbf3064832b0b14edafefce4e5 GET /api/v1/llm/assistant/llm_list
[2025-08-22 15:11:40.012531] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=5607a2f17f424cc1b70d304cbffa4de2 GET /api/v1/knowledge
[2025-08-22 15:11:40.020918] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=4da75476a57e453d860c6a0dfbc9df67 GET /api/v1/assistant/tool_list
[2025-08-22 15:11:40.182423] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=afaa53cbf3064832b0b14edafefce4e5 GET /api/v1/llm/assistant/llm_list 200 timecost=176.875
[2025-08-22 15:11:40.231643] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=66f4c284248c454abb987062691d0f75 GET /api/v1/workflow/versions 200 timecost=247.554
[2025-08-22 15:11:40.369275] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=5607a2f17f424cc1b70d304cbffa4de2 GET /api/v1/knowledge 200 timecost=356.744
[2025-08-22 15:11:40.379973] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=4da75476a57e453d860c6a0dfbc9df67 GET /api/v1/assistant/tool_list 200 timecost=359.055
[2025-08-22 15:11:40.421374] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=e38f453b199c4960a6548f9c65cc0fc0 GET /api/v1/assistant/tool_list 200 timecost=427.841
[2025-08-22 15:11:51.944420] [INFO process-31596-28760 bisheng.chat.clients.base:68] - trace=1 client_id=2ebfc5e558db431f92a6e72dcacdeeef trace_id=f896a737caab45aa8f6eb99204c94609 message={'action': 'init_data', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'data': {'status': 2, 'description': '支持添加知识库和工具', 'user_id': 1, 'guide_word': None, 'create_time': '2025-08-21T10:54:24', 'logo': '', 'name': '多轮对话-479e3', 'flow_type': 10, 'update_time': '2025-08-21T10:58:11', 'id': 'ac8b23aed25947908ac042dc5b5f1594', 'nodes': [{'id': 'start_553b9', 'data': {'v': 1, 'id': 'start_553b9', 'name': '开始', 'type': 'start', 'description': '工作流运行的起始节点。', 'group_params': [{'name': '开场引导', 'params': [{'key': 'guide_word', 'type': 'textarea', 'label': '开场白', 'value': '您好，请问想聊些什么呢？', 'placeholder': '每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。'}, {'key': 'guide_question', 'help': '为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', 'type': 'input_list', 'label': '引导问题', 'value': ['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], 'placeholder': '请输入引导问题'}]}, {'name': '全局变量', 'params': [{'key': 'current_time', 'type': 'var', 'label': '当前时间', 'value': '', 'global': 'key'}, {'key': 'chat_history', 'type': 'chat_history_num', 'value': 10, 'global': 'key'}, {'key': 'preset_question', 'help': '适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', 'type': 'input_list', 'label': '预置问题列表', 'value': [{'key': 'f4cbae', 'value': ''}], 'global': 'item:input_list', 'placeholder': '输入批量预置问题'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 702}, 'position': {'x': 491, 'y': 63.125}, 'selected': False}, {'id': 'input_2775b', 'data': {'v': 2, 'id': 'input_2775b', 'tab': {'value': 'dialog_input', 'options': [{'key': 'dialog_input', 'help': '接收用户从对话框输入的内容。!(input)', 'label': '对话框输入'}, {'key': 'form_input', 'help': '将会在用户会话界面弹出一个表单，接收用户从表单提交的内容。!(form)', 'label': '表单输入'}]}, 'name': '输入', 'type': 'input', 'expand': False, 'description': '接收用户在会话页面的输入，支持 2 种形式：对话框输入，表单输入。', 'group_params': [{'name': '', 'params': [{'key': 'user_input', 'tab': 'dialog_input', 'type': 'var', 'label': '用户输入内容', 'global': 'key'}, {'key': 'form_input', 'tab': 'form_input', 'type': 'form', 'label': '+ 添加表单项', 'value': [], 'global': 'item:form_input'}, {'key': 'dialog_files_content', 'tab': 'dialog_input', 'type': 'var', 'label': '上传文件内容', 'global': 'key'}, {'key': 'dialog_files_content_size', 'min': 0, 'tab': 'dialog_input', 'type': 'char_number', 'label': '文件内容长度上限', 'value': 15000}, {'key': 'dialog_file_accept', 'tab': 'dialog_input', 'type': 'select_fileaccept', 'label': '上传文件类型', 'value': 'all'}, {'key': 'dialog_image_files', 'tab': 'dialog_input', 'help': '提取上传文件中的图片文件，当助手或大模型节点使用多模态大模型时，可传入此图片。', 'type': 'var', 'label': '上传图片文件', 'global': 'key'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 149}, 'position': {'x': 947, 'y': 85}, 'selected': False}, {'id': 'agent_1d0c8', 'data': {'v': 2, 'id': 'agent_1d0c8', 'tab': {'value': 'single', 'options': [{'key': 'single', 'label': '单次运行'}, {'key': 'batch', 'label': '批量运行'}]}, 'name': '多轮对话助手', 'type': 'agent', 'expand': True, 'description': 'AI 自主进行任务规划，选择合适的知识库或工具进行调用。', 'group_params': [{'params': [{'key': 'batch_variable', 'tab': 'batch', 'help': '选择需要批处理的变量，将会多次运行本节点，每次运行时从选择的变量中取一项赋值给batch_variable进行处理。', 'type': 'user_question', 'label': '批处理变量', 'value': [], 'global': 'self', 'linkage': 'output', 'required': True, 'placeholder': '请选择批处理变量'}]}, {'name': '模型设置', 'params': [{'key': 'model_id', 'type': 'agent_model', 'label': '模型', 'value': 2, 'required': True, 'placeholder': '请选择模型'}, {'key': 'temperature', 'step': 0.1, 'type': 'slide', 'label': '温度', 'scope': [0, 2], 'value': 0.7}]}, {'name': '提示词', 'params': [{'key': 'system_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '系统提示词', 'value': '你是一个熟悉各行各业的 AI 助理，善于根据用户需求，适时调用合适的工具来回答用户问题', 'required': True, 'placeholder': '助手画像'}, {'key': 'user_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '用户提示词', 'value': '{{#input_2775b.user_input#}}', 'varZh': {'input_2775b.user_input': '输入/user_input'}, 'required': True, 'placeholder': '用户消息内容'}, {'key': 'chat_history_flag', 'help': '带入模型上下文的历史消息条数，为 0 时代表不包含上下文信息。', 'step': 1, 'type': 'slide_switch', 'label': '历史聊天记录', 'scope': [0, 100], 'value': {'flag': True, 'value': 50}}, {'key': 'image_prompt', 'help': '当使用多模态大模型时，可通过此功能传入图片，结合图像内容进行问答', 'type': 'image_prompt', 'label': '视觉', 'value': ''}]}, {'name': '知识库', 'params': [{'key': 'knowledge_id', 'type': 'knowledge_select_multi', 'label': '检索知识库范围', 'value': {'type': 'knowledge', 'value': []}, 'placeholder': '请选择知识库'}]}, {'name': '数据库', 'params': [{'key': 'sql_agent', 'type': 'sql_config', 'value': {'open': False, 'db_name': '', 'db_address': '', 'db_password': '', 'db_username': ''}}]}, {'name': '工具', 'params': [{'key': 'tool_list', 'type': 'add_tool', 'label': '+ 添加工具', 'value': []}]}, {'name': '输出', 'params': [{'key': 'output_user', 'help': '一般在问答等场景开启，文档审核、报告生成等场景可关闭。', 'type': 'switch', 'label': '将输出结果展示在会话中', 'value': True}, {'key': 'output', 'type': 'var', 'label': '输出变量', 'value': [], 'global': 'code:value.map(el => ({ label: el.label, value: el.key }))'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 1202}, 'position': {'x': 1416.1256404922672, 'y': 89.69476666183581}, 'selected': True}], 'edges': [{'id': 'xy-edge__start_553b9right_handle-input_2775bleft_handle', 'type': 'customEdge', 'source': 'start_553b9', 'target': 'input_2775b', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__input_2775bright_handle-agent_1d0c8left_handle', 'type': 'customEdge', 'source': 'input_2775b', 'target': 'agent_1d0c8', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__agent_1d0c8right_handle-input_2775bleft_handle', 'type': 'customEdge', 'source': 'agent_1d0c8', 'target': 'input_2775b', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}], 'viewport': {'x': 202.67642774879823, 'y': -1.8593657130471684, 'zoom': 0.7423265855532226}}}
[2025-08-22 15:11:52.019186] [INFO process-31596-27860 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:11:52.055593] [INFO process-31596-21288 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:11:53.065677] [INFO process-31596-21288 bisheng.utils.threadpool:67] - trace=f896a737caab45aa8f6eb99204c94609 async_task_added fun=wrapper_task args=f896a737caab45aa8f6eb99204c94609
[2025-08-22 15:11:53.068767] [INFO process-31596-27860 bisheng.utils.threadpool:67] - trace=2 async_task_added fun=consume_message args=
[2025-08-22 15:11:56.646237] [INFO process-31596-28760 bisheng.chat.clients.base:68] - trace=1 client_id=2ebfc5e558db431f92a6e72dcacdeeef trace_id=2a92d4c458864673bc9506c6ef8117b5 message={'action': 'input', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'data': {'input_2775b': {'data': {'user_input': '今天贵州茅台股价情况？', 'dialog_files_content': []}, 'message': '今天贵州茅台股价情况？', 'message_id': '4090497d3a6f46cbbed3caa7c2929692', 'category': 'question', 'extra': '', 'source': 0}}}
[2025-08-22 15:11:56.665786] [INFO process-31596-32420 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:11:56.678973] [INFO process-31596-25164 bisheng.chat.clients.workflow_client:182] - trace=2a92d4c458864673bc9506c6ef8117b5 get user input: {'input_2775b': {'data': {'user_input': '今天贵州茅台股价情况？', 'dialog_files_content': []}, 'message': '今天贵州茅台股价情况？', 'message_id': '4090497d3a6f46cbbed3caa7c2929692', 'category': 'question', 'extra': '', 'source': 0}}
[2025-08-22 15:11:57.683560] [INFO process-31596-32420 bisheng.utils.threadpool:67] - trace=2a92d4c458864673bc9506c6ef8117b5 async_task_added fun=wrapper_task args=2a92d4c458864673bc9506c6ef8117b5
[2025-08-22 15:12:43.596270] [INFO process-31596-28760 bisheng.chat.clients.base:68] - trace=1 client_id=2ebfc5e558db431f92a6e72dcacdeeef trace_id=f49fd527ed0741789d18d61e7397e812 message={'action': 'stop'}
[2025-08-22 15:12:43.628392] [INFO process-31596-28760 bisheng.chat.clients.base:68] - trace=1 client_id=2ebfc5e558db431f92a6e72dcacdeeef trace_id=15940e29823846909e43e9b1fb5a5df8 message={'action': 'init_data', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'data': {'status': 2, 'description': '支持添加知识库和工具', 'user_id': 1, 'guide_word': None, 'create_time': '2025-08-21T10:54:24', 'logo': '', 'name': '多轮对话-479e3', 'flow_type': 10, 'update_time': '2025-08-21T10:58:11', 'id': 'ac8b23aed25947908ac042dc5b5f1594', 'nodes': [{'id': 'start_553b9', 'data': {'v': 1, 'id': 'start_553b9', 'name': '开始', 'type': 'start', 'description': '工作流运行的起始节点。', 'group_params': [{'name': '开场引导', 'params': [{'key': 'guide_word', 'type': 'textarea', 'label': '开场白', 'value': '您好，请问想聊些什么呢？', 'placeholder': '每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。'}, {'key': 'guide_question', 'help': '为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', 'type': 'input_list', 'label': '引导问题', 'value': ['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], 'placeholder': '请输入引导问题'}]}, {'name': '全局变量', 'params': [{'key': 'current_time', 'type': 'var', 'label': '当前时间', 'value': '', 'global': 'key'}, {'key': 'chat_history', 'type': 'chat_history_num', 'value': 10, 'global': 'key'}, {'key': 'preset_question', 'help': '适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', 'type': 'input_list', 'label': '预置问题列表', 'value': [{'key': 'f4cbae', 'value': ''}], 'global': 'item:input_list', 'placeholder': '输入批量预置问题'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 702}, 'position': {'x': 491, 'y': 63.125}, 'selected': False}, {'id': 'input_2775b', 'data': {'v': 2, 'id': 'input_2775b', 'tab': {'value': 'dialog_input', 'options': [{'key': 'dialog_input', 'help': '接收用户从对话框输入的内容。!(input)', 'label': '对话框输入'}, {'key': 'form_input', 'help': '将会在用户会话界面弹出一个表单，接收用户从表单提交的内容。!(form)', 'label': '表单输入'}]}, 'name': '输入', 'type': 'input', 'expand': False, 'description': '接收用户在会话页面的输入，支持 2 种形式：对话框输入，表单输入。', 'group_params': [{'name': '', 'params': [{'key': 'user_input', 'tab': 'dialog_input', 'type': 'var', 'label': '用户输入内容', 'global': 'key'}, {'key': 'form_input', 'tab': 'form_input', 'type': 'form', 'label': '+ 添加表单项', 'value': [], 'global': 'item:form_input'}, {'key': 'dialog_files_content', 'tab': 'dialog_input', 'type': 'var', 'label': '上传文件内容', 'global': 'key'}, {'key': 'dialog_files_content_size', 'min': 0, 'tab': 'dialog_input', 'type': 'char_number', 'label': '文件内容长度上限', 'value': 15000}, {'key': 'dialog_file_accept', 'tab': 'dialog_input', 'type': 'select_fileaccept', 'label': '上传文件类型', 'value': 'all'}, {'key': 'dialog_image_files', 'tab': 'dialog_input', 'help': '提取上传文件中的图片文件，当助手或大模型节点使用多模态大模型时，可传入此图片。', 'type': 'var', 'label': '上传图片文件', 'global': 'key'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 149}, 'position': {'x': 947, 'y': 85}, 'selected': False}, {'id': 'agent_1d0c8', 'data': {'v': 2, 'id': 'agent_1d0c8', 'tab': {'value': 'single', 'options': [{'key': 'single', 'label': '单次运行'}, {'key': 'batch', 'label': '批量运行'}]}, 'name': '多轮对话助手', 'type': 'agent', 'expand': True, 'description': 'AI 自主进行任务规划，选择合适的知识库或工具进行调用。', 'group_params': [{'params': [{'key': 'batch_variable', 'tab': 'batch', 'help': '选择需要批处理的变量，将会多次运行本节点，每次运行时从选择的变量中取一项赋值给batch_variable进行处理。', 'type': 'user_question', 'label': '批处理变量', 'value': [], 'global': 'self', 'linkage': 'output', 'required': True, 'placeholder': '请选择批处理变量'}]}, {'name': '模型设置', 'params': [{'key': 'model_id', 'type': 'agent_model', 'label': '模型', 'value': 2, 'required': True, 'placeholder': '请选择模型'}, {'key': 'temperature', 'step': 0.1, 'type': 'slide', 'label': '温度', 'scope': [0, 2], 'value': 0.7}]}, {'name': '提示词', 'params': [{'key': 'system_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '系统提示词', 'value': '你是一个熟悉各行各业的 AI 助理，善于根据用户需求，适时调用合适的工具来回答用户问题', 'required': True, 'placeholder': '助手画像'}, {'key': 'user_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '用户提示词', 'value': '{{#input_2775b.user_input#}}', 'varZh': {'input_2775b.user_input': '输入/user_input'}, 'required': True, 'placeholder': '用户消息内容'}, {'key': 'chat_history_flag', 'help': '带入模型上下文的历史消息条数，为 0 时代表不包含上下文信息。', 'step': 1, 'type': 'slide_switch', 'label': '历史聊天记录', 'scope': [0, 100], 'value': {'flag': True, 'value': 50}}, {'key': 'image_prompt', 'help': '当使用多模态大模型时，可通过此功能传入图片，结合图像内容进行问答', 'type': 'image_prompt', 'label': '视觉', 'value': ''}]}, {'name': '知识库', 'params': [{'key': 'knowledge_id', 'type': 'knowledge_select_multi', 'label': '检索知识库范围', 'value': {'type': 'knowledge', 'value': []}, 'placeholder': '请选择知识库'}]}, {'name': '数据库', 'params': [{'key': 'sql_agent', 'type': 'sql_config', 'value': {'open': False, 'db_name': '', 'db_address': '', 'db_password': '', 'db_username': ''}}]}, {'name': '工具', 'params': [{'key': 'tool_list', 'type': 'add_tool', 'label': '+ 添加工具', 'value': []}]}, {'name': '输出', 'params': [{'key': 'output_user', 'help': '一般在问答等场景开启，文档审核、报告生成等场景可关闭。', 'type': 'switch', 'label': '将输出结果展示在会话中', 'value': True}, {'key': 'output', 'type': 'var', 'label': '输出变量', 'value': [], 'global': 'code:value.map(el => ({ label: el.label, value: el.key }))'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 1202}, 'position': {'x': 1416.1256404922672, 'y': 89.69476666183581}, 'selected': True}], 'edges': [{'id': 'xy-edge__start_553b9right_handle-input_2775bleft_handle', 'type': 'customEdge', 'source': 'start_553b9', 'target': 'input_2775b', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__input_2775bright_handle-agent_1d0c8left_handle', 'type': 'customEdge', 'source': 'input_2775b', 'target': 'agent_1d0c8', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__agent_1d0c8right_handle-input_2775bleft_handle', 'type': 'customEdge', 'source': 'agent_1d0c8', 'target': 'input_2775b', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}], 'viewport': {'x': 202.67642774879823, 'y': -1.8593657130471684, 'zoom': 0.7423265855532226}}}
[2025-08-22 15:12:43.780218] [INFO process-31596-21288 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:12:44.790413] [INFO process-31596-21288 bisheng.utils.threadpool:67] - trace=15940e29823846909e43e9b1fb5a5df8 async_task_added fun=wrapper_task args=15940e29823846909e43e9b1fb5a5df8
[2025-08-22 15:12:51.079190] [INFO process-31596-28760 bisheng.chat.manager:243] - trace=1 act=rcv_client_disconnect (1001, None)
[2025-08-22 15:12:51.089562] [INFO process-31596-28760 bisheng.chat.manager:181] - trace=1 close_client client_key=2ebfc5e558db431f92a6e72dcacdeeef
[2025-08-22 15:12:51.261305] [ERROR process-31596-28760 bisheng.chat.manager:194] - trace=1 Unexpected ASGI message 'websocket.close', after sending 'websocket.close'.
[2025-08-22 15:12:51.271176] [WARNING process-31596-28760 bisheng.chat.manager:179] - trace=1 close_client client_key=2ebfc5e558db431f92a6e72dcacdeeef not in active_clients
[2025-08-22 15:12:51.402213] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=f5120c16d6274adea2a75d5d4f37d49e GET /api/v1/web/config
[2025-08-22 15:12:51.426933] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=f5120c16d6274adea2a75d5d4f37d49e GET /api/v1/web/config 200 timecost=24.72
[2025-08-22 15:12:55.247575] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=c592e1532c7a4d73a951235d28a69dca GET /api/v1/user/info
[2025-08-22 15:12:55.260396] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=a7da39dc63254634a99888c6f961cc86 GET /api/v1/env
[2025-08-22 15:12:55.276247] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=1fa212f7c32545a0874fd37ebb1d2dad GET /api/v1/all
[2025-08-22 15:12:55.340137] [INFO process-31596-29828 bisheng.interface.custom.utils:343] - trace=1fa212f7c32545a0874fd37ebb1d2dad Building custom components from ['D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\components']
[2025-08-22 15:12:55.740228] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=c592e1532c7a4d73a951235d28a69dca GET /api/v1/user/info 200 timecost=492.653
[2025-08-22 15:12:55.773329] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=a7da39dc63254634a99888c6f961cc86 GET /api/v1/env 200 timecost=512.933
[2025-08-22 15:12:55.824419] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=971efeb8ebbe4af9b41184d95aa92c65 GET /api/v1/component
[2025-08-22 15:12:55.851222] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=44a8873921e64e32801b8a2ee9dd05c0 GET /api/v1/flows/ac8b23aed25947908ac042dc5b5f1594
[2025-08-22 15:12:55.883870] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=c2fec069e46d40f2bbe9c6c22be98a7c GET /api/v1/workstation/config
[2025-08-22 15:12:56.652310] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=44a8873921e64e32801b8a2ee9dd05c0 GET /api/v1/flows/ac8b23aed25947908ac042dc5b5f1594 200 timecost=802.084
[2025-08-22 15:12:56.674434] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=971efeb8ebbe4af9b41184d95aa92c65 GET /api/v1/component 200 timecost=850.015
[2025-08-22 15:12:56.701888] [ERROR process-31596-32184 __main__:33] - trace=1 GET http://127.0.0.1:7860/api/v1/workstation/config 'NoneType' object has no attribute 'model_dump'
[2025-08-22 15:12:56.725406] [INFO process-31596-29828 bisheng.interface.custom.utils:354] - trace=1fa212f7c32545a0874fd37ebb1d2dad Loading 1 component(s) from category custom_components
[2025-08-22 15:12:57.800514] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=1fa212f7c32545a0874fd37ebb1d2dad GET /api/v1/all 200 timecost=2523.272
[2025-08-22 15:12:57.818003] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=655f09fc5dc14cdb85a1de1277b90e7f GET /api/v1/workflow/versions
[2025-08-22 15:12:57.835053] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=89349f26ab444e32a225e1aa14cf9c98 GET /api/v1/assistant/tool_list
[2025-08-22 15:12:57.852110] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=cfdf371790c74abb80990f23f915a446 GET /api/v1/llm/assistant/llm_list
[2025-08-22 15:12:57.873740] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=b2c93db3455a4cebbeead8a165017bac GET /api/v1/knowledge
[2025-08-22 15:12:57.893982] [INFO process-31596-28760 bisheng.utils.http_middleware:21] - trace=0b61f4bf1fc042089c4288194a3df32f GET /api/v1/assistant/tool_list
[2025-08-22 15:12:58.160638] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=cfdf371790c74abb80990f23f915a446 GET /api/v1/llm/assistant/llm_list 200 timecost=308.528
[2025-08-22 15:12:58.191119] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=655f09fc5dc14cdb85a1de1277b90e7f GET /api/v1/workflow/versions 200 timecost=373.116
[2025-08-22 15:12:58.344040] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=0b61f4bf1fc042089c4288194a3df32f GET /api/v1/assistant/tool_list 200 timecost=450.058
[2025-08-22 15:12:58.406029] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=89349f26ab444e32a225e1aa14cf9c98 GET /api/v1/assistant/tool_list 200 timecost=569.963
[2025-08-22 15:12:58.449115] [INFO process-31596-28760 bisheng.utils.http_middleware:24] - trace=b2c93db3455a4cebbeead8a165017bac GET /api/v1/knowledge 200 timecost=574.374
[2025-08-22 15:13:02.025197] [INFO process-31596-28760 bisheng.chat.clients.base:68] - trace=1 client_id=25ffe75c41aa4cfca856ecb9f0e888d5 trace_id=eb1ffc303aef41dba79888cd0c7003a3 message={'action': 'init_data', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'data': {'status': 2, 'description': '支持添加知识库和工具', 'user_id': 1, 'guide_word': None, 'create_time': '2025-08-21T10:54:24', 'logo': '', 'name': '多轮对话-479e3', 'flow_type': 10, 'update_time': '2025-08-21T10:58:11', 'id': 'ac8b23aed25947908ac042dc5b5f1594', 'nodes': [{'id': 'start_553b9', 'data': {'v': 1, 'id': 'start_553b9', 'name': '开始', 'type': 'start', 'description': '工作流运行的起始节点。', 'group_params': [{'name': '开场引导', 'params': [{'key': 'guide_word', 'type': 'textarea', 'label': '开场白', 'value': '您好，请问想聊些什么呢？', 'placeholder': '每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。'}, {'key': 'guide_question', 'help': '为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', 'type': 'input_list', 'label': '引导问题', 'value': ['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], 'placeholder': '请输入引导问题'}]}, {'name': '全局变量', 'params': [{'key': 'current_time', 'type': 'var', 'label': '当前时间', 'value': '', 'global': 'key'}, {'key': 'chat_history', 'type': 'chat_history_num', 'value': 10, 'global': 'key'}, {'key': 'preset_question', 'help': '适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', 'type': 'input_list', 'label': '预置问题列表', 'value': [{'key': 'f4cbae', 'value': ''}], 'global': 'item:input_list', 'placeholder': '输入批量预置问题'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 702}, 'position': {'x': 491, 'y': 63.125}, 'selected': False}, {'id': 'input_2775b', 'data': {'v': 2, 'id': 'input_2775b', 'tab': {'value': 'dialog_input', 'options': [{'key': 'dialog_input', 'help': '接收用户从对话框输入的内容。!(input)', 'label': '对话框输入'}, {'key': 'form_input', 'help': '将会在用户会话界面弹出一个表单，接收用户从表单提交的内容。!(form)', 'label': '表单输入'}]}, 'name': '输入', 'type': 'input', 'expand': False, 'description': '接收用户在会话页面的输入，支持 2 种形式：对话框输入，表单输入。', 'group_params': [{'name': '', 'params': [{'key': 'user_input', 'tab': 'dialog_input', 'type': 'var', 'label': '用户输入内容', 'global': 'key'}, {'key': 'form_input', 'tab': 'form_input', 'type': 'form', 'label': '+ 添加表单项', 'value': [], 'global': 'item:form_input'}, {'key': 'dialog_files_content', 'tab': 'dialog_input', 'type': 'var', 'label': '上传文件内容', 'global': 'key'}, {'key': 'dialog_files_content_size', 'min': 0, 'tab': 'dialog_input', 'type': 'char_number', 'label': '文件内容长度上限', 'value': 15000}, {'key': 'dialog_file_accept', 'tab': 'dialog_input', 'type': 'select_fileaccept', 'label': '上传文件类型', 'value': 'all'}, {'key': 'dialog_image_files', 'tab': 'dialog_input', 'help': '提取上传文件中的图片文件，当助手或大模型节点使用多模态大模型时，可传入此图片。', 'type': 'var', 'label': '上传图片文件', 'global': 'key'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 149}, 'position': {'x': 947, 'y': 85}, 'selected': False}, {'id': 'agent_1d0c8', 'data': {'v': 2, 'id': 'agent_1d0c8', 'tab': {'value': 'single', 'options': [{'key': 'single', 'label': '单次运行'}, {'key': 'batch', 'label': '批量运行'}]}, 'name': '多轮对话助手', 'type': 'agent', 'expand': True, 'description': 'AI 自主进行任务规划，选择合适的知识库或工具进行调用。', 'group_params': [{'params': [{'key': 'batch_variable', 'tab': 'batch', 'help': '选择需要批处理的变量，将会多次运行本节点，每次运行时从选择的变量中取一项赋值给batch_variable进行处理。', 'type': 'user_question', 'label': '批处理变量', 'value': [], 'global': 'self', 'linkage': 'output', 'required': True, 'placeholder': '请选择批处理变量'}]}, {'name': '模型设置', 'params': [{'key': 'model_id', 'type': 'agent_model', 'label': '模型', 'value': 2, 'required': True, 'placeholder': '请选择模型'}, {'key': 'temperature', 'step': 0.1, 'type': 'slide', 'label': '温度', 'scope': [0, 2], 'value': 0.7}]}, {'name': '提示词', 'params': [{'key': 'system_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '系统提示词', 'value': '你是一个熟悉各行各业的 AI 助理，善于根据用户需求，适时调用合适的工具来回答用户问题', 'required': True, 'placeholder': '助手画像'}, {'key': 'user_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '用户提示词', 'value': '{{#input_2775b.user_input#}}', 'varZh': {'input_2775b.user_input': '输入/user_input'}, 'required': True, 'placeholder': '用户消息内容'}, {'key': 'chat_history_flag', 'help': '带入模型上下文的历史消息条数，为 0 时代表不包含上下文信息。', 'step': 1, 'type': 'slide_switch', 'label': '历史聊天记录', 'scope': [0, 100], 'value': {'flag': True, 'value': 50}}, {'key': 'image_prompt', 'help': '当使用多模态大模型时，可通过此功能传入图片，结合图像内容进行问答', 'type': 'image_prompt', 'label': '视觉', 'value': ''}]}, {'name': '知识库', 'params': [{'key': 'knowledge_id', 'type': 'knowledge_select_multi', 'label': '检索知识库范围', 'value': {'type': 'knowledge', 'value': []}, 'placeholder': '请选择知识库'}]}, {'name': '数据库', 'params': [{'key': 'sql_agent', 'type': 'sql_config', 'value': {'open': False, 'db_name': '', 'db_address': '', 'db_password': '', 'db_username': ''}}]}, {'name': '工具', 'params': [{'key': 'tool_list', 'type': 'add_tool', 'label': '+ 添加工具', 'value': []}]}, {'name': '输出', 'params': [{'key': 'output_user', 'help': '一般在问答等场景开启，文档审核、报告生成等场景可关闭。', 'type': 'switch', 'label': '将输出结果展示在会话中', 'value': True}, {'key': 'output', 'type': 'var', 'label': '输出变量', 'value': [], 'global': 'code:value.map(el => ({ label: el.label, value: el.key }))'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 1202}, 'position': {'x': 1416.1256404922672, 'y': 89.69476666183581}, 'selected': True}], 'edges': [{'id': 'xy-edge__start_553b9right_handle-input_2775bleft_handle', 'type': 'customEdge', 'source': 'start_553b9', 'target': 'input_2775b', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__input_2775bright_handle-agent_1d0c8left_handle', 'type': 'customEdge', 'source': 'input_2775b', 'target': 'agent_1d0c8', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__agent_1d0c8right_handle-input_2775bleft_handle', 'type': 'customEdge', 'source': 'agent_1d0c8', 'target': 'input_2775b', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}], 'viewport': {'x': 202.67642774879823, 'y': -1.8593657130471684, 'zoom': 0.7423265855532226}}}
[2025-08-22 15:13:02.233335] [INFO process-31596-18204 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:13:02.337971] [INFO process-31596-27860 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:13:03.338266] [INFO process-31596-18204 bisheng.utils.threadpool:67] - trace=2 async_task_added fun=consume_message args=
[2025-08-22 15:13:03.368709] [INFO process-31596-27860 bisheng.utils.threadpool:67] - trace=eb1ffc303aef41dba79888cd0c7003a3 async_task_added fun=wrapper_task args=eb1ffc303aef41dba79888cd0c7003a3
[2025-08-22 15:13:09.672371] [INFO process-31596-28760 bisheng.chat.clients.base:68] - trace=1 client_id=25ffe75c41aa4cfca856ecb9f0e888d5 trace_id=5410e0ef44184872a27144c695384d76 message={'action': 'input', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'data': {'input_2775b': {'data': {'user_input': '你好。。。。。。', 'dialog_files_content': []}, 'message': '你好。。。。。。', 'message_id': '09a79f1647654b70963aca7d624f3944', 'category': 'question', 'extra': '', 'source': 0}}}
[2025-08-22 15:13:09.706145] [INFO process-31596-21288 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:13:09.736090] [INFO process-31596-21360 bisheng.chat.clients.workflow_client:182] - trace=5410e0ef44184872a27144c695384d76 get user input: {'input_2775b': {'data': {'user_input': '你好。。。。。。', 'dialog_files_content': []}, 'message': '你好。。。。。。', 'message_id': '09a79f1647654b70963aca7d624f3944', 'category': 'question', 'extra': '', 'source': 0}}
[2025-08-22 15:13:10.732346] [INFO process-31596-21288 bisheng.utils.threadpool:67] - trace=5410e0ef44184872a27144c695384d76 async_task_added fun=wrapper_task args=5410e0ef44184872a27144c695384d76
[2025-08-22 15:13:57.517131] [INFO process-31596-28760 bisheng.chat.manager:243] - trace=1 act=rcv_client_disconnect (1012, None)
[2025-08-22 15:13:57.522749] [INFO process-31596-28760 bisheng.chat.manager:181] - trace=1 close_client client_key=25ffe75c41aa4cfca856ecb9f0e888d5
[2025-08-22 15:13:57.527762] [ERROR process-31596-28760 bisheng.chat.manager:194] - trace=1 Unexpected ASGI message 'websocket.close', after sending 'websocket.close'.
[2025-08-22 15:13:57.528761] [ERROR process-31596-29740 bisheng.chat.clients.base:61] - trace=2 consume_message error {'is_bot': 1, 'message': '', 'type': 'close', 'category': 'processing', 'intermediate_steps': '', 'files': [], 'user_id': 1, 'message_id': None, 'source': 0, 'sender': None, 'receiver': None, 'liked': 0, 'extra': '{"client_key": "25ffe75c41aa4cfca856ecb9f0e888d5"}', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'chat_id': ''} error: Cannot call "send" once a close message has been sent.
[2025-08-22 15:13:57.541967] [WARNING process-31596-28760 bisheng.chat.manager:179] - trace=1 close_client client_key=25ffe75c41aa4cfca856ecb9f0e888d5 not in active_clients
[2025-08-22 15:13:57.639984] [INFO process-31596-28760 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=websocket_send_json_83588f0ac6e14746879ada3e0510b3eb task=<Future at 0x1be20434970 state=finished returned Future> res=True
[2025-08-22 15:13:57.645139] [INFO process-31596-28760 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=89e448368b924a04a0c917c2f73f3f59 task=<Future at 0x1be2075fc70 state=finished returned Future> res=False
[2025-08-22 15:13:57.652937] [INFO process-31596-28760 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=b5e008e0044c4085a3218195ffbf8b58 task=<Future at 0x1be2075f6a0 state=finished returned Future> res=False
[2025-08-22 15:13:57.664023] [INFO process-31596-28760 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=73116ab4d6834a0cbbeb19887a638fba task=<Future at 0x1be205dd420 state=finished returned Future> res=False
[2025-08-22 15:13:57.673953] [INFO process-31596-28760 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=fec75b474e78428d96b65475fbf1a6c0 task=<Future at 0x1be203b0a60 state=finished returned Future> res=False
[2025-08-22 15:13:57.682866] [INFO process-31596-28760 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=b10158dbdfea4b4a84c754bbd10f81c1 task=<Future at 0x1be208d6380 state=finished returned Future> res=False
[2025-08-22 15:13:57.693384] [INFO process-31596-28760 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=websocket_send_json_2ebfc5e558db431f92a6e72dcacdeeef task=<Future at 0x1be20962c80 state=finished returned Future> res=True
[2025-08-22 15:13:57.702906] [INFO process-31596-28760 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=f896a737caab45aa8f6eb99204c94609 task=<Future at 0x1be20954730 state=finished returned Future> res=False
[2025-08-22 15:13:57.707947] [INFO process-31596-28760 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=2a92d4c458864673bc9506c6ef8117b5 task=<Future at 0x1be20954a60 state=finished returned Future> res=False
[2025-08-22 15:13:57.712940] [INFO process-31596-28760 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=15940e29823846909e43e9b1fb5a5df8 task=<Future at 0x1be208eee30 state=finished returned Future> res=False
[2025-08-22 15:13:57.719320] [INFO process-31596-28760 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=websocket_send_json_25ffe75c41aa4cfca856ecb9f0e888d5 task=<Future at 0x1be209821d0 state=finished returned Future> res=False
[2025-08-22 15:13:57.724841] [INFO process-31596-28760 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=eb1ffc303aef41dba79888cd0c7003a3 task=<Future at 0x1be20980850 state=finished returned Future> res=False
[2025-08-22 15:13:57.729352] [INFO process-31596-28760 bisheng.utils.threadpool:115] - trace=1 clean_pending_task key=5410e0ef44184872a27144c695384d76 task=<Future at 0x1be209c68c0 state=finished returned Future> res=False
[2025-08-22 15:15:38.127133] [INFO process-5916-264 datasets.config:58] - trace=1 PyTorch version 2.8.0 available.
[2025-08-22 15:15:47.229648] [INFO process-5916-264 bisheng.interface.utils:92] - trace=1 LLM caching setup with InMemoryCache
[2025-08-22 15:16:18.940733] [INFO process-5916-23580 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:16:18.954330] [INFO process-5916-264 bisheng.chat.clients.base:68] - trace=1 client_id=8cce76dfbb4240e7b7f16aa24580ae6b trace_id=72b79b5cb07743e3acc6988d7f8486ca message={'action': 'init_data', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'data': {'status': 2, 'description': '支持添加知识库和工具', 'user_id': 1, 'guide_word': None, 'create_time': '2025-08-21T10:54:24', 'logo': '', 'name': '多轮对话-479e3', 'flow_type': 10, 'update_time': '2025-08-21T10:58:11', 'id': 'ac8b23aed25947908ac042dc5b5f1594', 'nodes': [{'id': 'start_553b9', 'data': {'v': 1, 'id': 'start_553b9', 'name': '开始', 'type': 'start', 'description': '工作流运行的起始节点。', 'group_params': [{'name': '开场引导', 'params': [{'key': 'guide_word', 'type': 'textarea', 'label': '开场白', 'value': '您好，请问想聊些什么呢？', 'placeholder': '每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。'}, {'key': 'guide_question', 'help': '为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', 'type': 'input_list', 'label': '引导问题', 'value': ['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], 'placeholder': '请输入引导问题'}]}, {'name': '全局变量', 'params': [{'key': 'current_time', 'type': 'var', 'label': '当前时间', 'value': '', 'global': 'key'}, {'key': 'chat_history', 'type': 'chat_history_num', 'value': 10, 'global': 'key'}, {'key': 'preset_question', 'help': '适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', 'type': 'input_list', 'label': '预置问题列表', 'value': [{'key': 'f4cbae', 'value': ''}], 'global': 'item:input_list', 'placeholder': '输入批量预置问题'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 702}, 'position': {'x': 491, 'y': 63.125}, 'selected': False}, {'id': 'input_2775b', 'data': {'v': 2, 'id': 'input_2775b', 'tab': {'value': 'dialog_input', 'options': [{'key': 'dialog_input', 'help': '接收用户从对话框输入的内容。!(input)', 'label': '对话框输入'}, {'key': 'form_input', 'help': '将会在用户会话界面弹出一个表单，接收用户从表单提交的内容。!(form)', 'label': '表单输入'}]}, 'name': '输入', 'type': 'input', 'expand': False, 'description': '接收用户在会话页面的输入，支持 2 种形式：对话框输入，表单输入。', 'group_params': [{'name': '', 'params': [{'key': 'user_input', 'tab': 'dialog_input', 'type': 'var', 'label': '用户输入内容', 'global': 'key'}, {'key': 'form_input', 'tab': 'form_input', 'type': 'form', 'label': '+ 添加表单项', 'value': [], 'global': 'item:form_input'}, {'key': 'dialog_files_content', 'tab': 'dialog_input', 'type': 'var', 'label': '上传文件内容', 'global': 'key'}, {'key': 'dialog_files_content_size', 'min': 0, 'tab': 'dialog_input', 'type': 'char_number', 'label': '文件内容长度上限', 'value': 15000}, {'key': 'dialog_file_accept', 'tab': 'dialog_input', 'type': 'select_fileaccept', 'label': '上传文件类型', 'value': 'all'}, {'key': 'dialog_image_files', 'tab': 'dialog_input', 'help': '提取上传文件中的图片文件，当助手或大模型节点使用多模态大模型时，可传入此图片。', 'type': 'var', 'label': '上传图片文件', 'global': 'key'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 149}, 'position': {'x': 947, 'y': 85}, 'selected': False}, {'id': 'agent_1d0c8', 'data': {'v': 2, 'id': 'agent_1d0c8', 'tab': {'value': 'single', 'options': [{'key': 'single', 'label': '单次运行'}, {'key': 'batch', 'label': '批量运行'}]}, 'name': '多轮对话助手', 'type': 'agent', 'expand': True, 'description': 'AI 自主进行任务规划，选择合适的知识库或工具进行调用。', 'group_params': [{'params': [{'key': 'batch_variable', 'tab': 'batch', 'help': '选择需要批处理的变量，将会多次运行本节点，每次运行时从选择的变量中取一项赋值给batch_variable进行处理。', 'type': 'user_question', 'label': '批处理变量', 'value': [], 'global': 'self', 'linkage': 'output', 'required': True, 'placeholder': '请选择批处理变量'}]}, {'name': '模型设置', 'params': [{'key': 'model_id', 'type': 'agent_model', 'label': '模型', 'value': 2, 'required': True, 'placeholder': '请选择模型'}, {'key': 'temperature', 'step': 0.1, 'type': 'slide', 'label': '温度', 'scope': [0, 2], 'value': 0.7}]}, {'name': '提示词', 'params': [{'key': 'system_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '系统提示词', 'value': '你是一个熟悉各行各业的 AI 助理，善于根据用户需求，适时调用合适的工具来回答用户问题', 'required': True, 'placeholder': '助手画像'}, {'key': 'user_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '用户提示词', 'value': '{{#input_2775b.user_input#}}', 'varZh': {'input_2775b.user_input': '输入/user_input'}, 'required': True, 'placeholder': '用户消息内容'}, {'key': 'chat_history_flag', 'help': '带入模型上下文的历史消息条数，为 0 时代表不包含上下文信息。', 'step': 1, 'type': 'slide_switch', 'label': '历史聊天记录', 'scope': [0, 100], 'value': {'flag': True, 'value': 50}}, {'key': 'image_prompt', 'help': '当使用多模态大模型时，可通过此功能传入图片，结合图像内容进行问答', 'type': 'image_prompt', 'label': '视觉', 'value': ''}]}, {'name': '知识库', 'params': [{'key': 'knowledge_id', 'type': 'knowledge_select_multi', 'label': '检索知识库范围', 'value': {'type': 'knowledge', 'value': []}, 'placeholder': '请选择知识库'}]}, {'name': '数据库', 'params': [{'key': 'sql_agent', 'type': 'sql_config', 'value': {'open': False, 'db_name': '', 'db_address': '', 'db_password': '', 'db_username': ''}}]}, {'name': '工具', 'params': [{'key': 'tool_list', 'type': 'add_tool', 'label': '+ 添加工具', 'value': []}]}, {'name': '输出', 'params': [{'key': 'output_user', 'help': '一般在问答等场景开启，文档审核、报告生成等场景可关闭。', 'type': 'switch', 'label': '将输出结果展示在会话中', 'value': True}, {'key': 'output', 'type': 'var', 'label': '输出变量', 'value': [], 'global': 'code:value.map(el => ({ label: el.label, value: el.key }))'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 1202}, 'position': {'x': 1416.1256404922672, 'y': 89.69476666183581}, 'selected': True}], 'edges': [{'id': 'xy-edge__start_553b9right_handle-input_2775bleft_handle', 'type': 'customEdge', 'source': 'start_553b9', 'target': 'input_2775b', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__input_2775bright_handle-agent_1d0c8left_handle', 'type': 'customEdge', 'source': 'input_2775b', 'target': 'agent_1d0c8', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__agent_1d0c8right_handle-input_2775bleft_handle', 'type': 'customEdge', 'source': 'agent_1d0c8', 'target': 'input_2775b', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}], 'viewport': {'x': 202.67642774879823, 'y': -1.8593657130471684, 'zoom': 0.7423265855532226}}}
[2025-08-22 15:16:19.239518] [INFO process-5916-31892 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:16:20.022296] [INFO process-5916-23580 bisheng.utils.threadpool:67] - trace=2 async_task_added fun=consume_message args=
[2025-08-22 15:16:20.272361] [INFO process-5916-31892 bisheng.utils.threadpool:67] - trace=72b79b5cb07743e3acc6988d7f8486ca async_task_added fun=wrapper_task args=72b79b5cb07743e3acc6988d7f8486ca
[2025-08-22 15:16:21.700487] [INFO process-5916-264 bisheng.chat.clients.base:68] - trace=1 client_id=8cce76dfbb4240e7b7f16aa24580ae6b trace_id=74753318a0b340138c96555eed0b1917 message={'action': 'input', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'data': {'input_2775b': {'data': {'user_input': '最近关于低空经济的政策', 'dialog_files_content': []}, 'message': '最近关于低空经济的政策', 'message_id': '30bbd0e09bee4c3ebf55c52d0ea66283', 'category': 'question', 'extra': '', 'source': 0}}}
[2025-08-22 15:16:21.739743] [INFO process-5916-23580 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:16:21.775414] [INFO process-5916-20800 bisheng.chat.clients.workflow_client:182] - trace=74753318a0b340138c96555eed0b1917 get user input: {'input_2775b': {'data': {'user_input': '最近关于低空经济的政策', 'dialog_files_content': []}, 'message': '最近关于低空经济的政策', 'message_id': '30bbd0e09bee4c3ebf55c52d0ea66283', 'category': 'question', 'extra': '', 'source': 0}}
[2025-08-22 15:16:22.762984] [INFO process-5916-23580 bisheng.utils.threadpool:67] - trace=74753318a0b340138c96555eed0b1917 async_task_added fun=wrapper_task args=74753318a0b340138c96555eed0b1917
[2025-08-22 15:16:36.141398] [INFO process-5916-264 bisheng.chat.clients.base:68] - trace=1 client_id=8cce76dfbb4240e7b7f16aa24580ae6b trace_id=66aa9bae002f42eaa4014eaa9d7d5bdf message={'action': 'stop'}
[2025-08-22 15:16:36.158805] [INFO process-5916-264 bisheng.chat.clients.base:68] - trace=1 client_id=8cce76dfbb4240e7b7f16aa24580ae6b trace_id=0d79571125cf447098e0df1c1e484ef6 message={'action': 'init_data', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'data': {'status': 2, 'description': '支持添加知识库和工具', 'user_id': 1, 'guide_word': None, 'create_time': '2025-08-21T10:54:24', 'logo': '', 'name': '多轮对话-479e3', 'flow_type': 10, 'update_time': '2025-08-21T10:58:11', 'id': 'ac8b23aed25947908ac042dc5b5f1594', 'nodes': [{'id': 'start_553b9', 'data': {'v': 1, 'id': 'start_553b9', 'name': '开始', 'type': 'start', 'description': '工作流运行的起始节点。', 'group_params': [{'name': '开场引导', 'params': [{'key': 'guide_word', 'type': 'textarea', 'label': '开场白', 'value': '您好，请问想聊些什么呢？', 'placeholder': '每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。'}, {'key': 'guide_question', 'help': '为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', 'type': 'input_list', 'label': '引导问题', 'value': ['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], 'placeholder': '请输入引导问题'}]}, {'name': '全局变量', 'params': [{'key': 'current_time', 'type': 'var', 'label': '当前时间', 'value': '', 'global': 'key'}, {'key': 'chat_history', 'type': 'chat_history_num', 'value': 10, 'global': 'key'}, {'key': 'preset_question', 'help': '适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', 'type': 'input_list', 'label': '预置问题列表', 'value': [{'key': 'f4cbae', 'value': ''}], 'global': 'item:input_list', 'placeholder': '输入批量预置问题'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 702}, 'position': {'x': 491, 'y': 63.125}, 'selected': False}, {'id': 'input_2775b', 'data': {'v': 2, 'id': 'input_2775b', 'tab': {'value': 'dialog_input', 'options': [{'key': 'dialog_input', 'help': '接收用户从对话框输入的内容。!(input)', 'label': '对话框输入'}, {'key': 'form_input', 'help': '将会在用户会话界面弹出一个表单，接收用户从表单提交的内容。!(form)', 'label': '表单输入'}]}, 'name': '输入', 'type': 'input', 'expand': False, 'description': '接收用户在会话页面的输入，支持 2 种形式：对话框输入，表单输入。', 'group_params': [{'name': '', 'params': [{'key': 'user_input', 'tab': 'dialog_input', 'type': 'var', 'label': '用户输入内容', 'global': 'key'}, {'key': 'form_input', 'tab': 'form_input', 'type': 'form', 'label': '+ 添加表单项', 'value': [], 'global': 'item:form_input'}, {'key': 'dialog_files_content', 'tab': 'dialog_input', 'type': 'var', 'label': '上传文件内容', 'global': 'key'}, {'key': 'dialog_files_content_size', 'min': 0, 'tab': 'dialog_input', 'type': 'char_number', 'label': '文件内容长度上限', 'value': 15000}, {'key': 'dialog_file_accept', 'tab': 'dialog_input', 'type': 'select_fileaccept', 'label': '上传文件类型', 'value': 'all'}, {'key': 'dialog_image_files', 'tab': 'dialog_input', 'help': '提取上传文件中的图片文件，当助手或大模型节点使用多模态大模型时，可传入此图片。', 'type': 'var', 'label': '上传图片文件', 'global': 'key'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 149}, 'position': {'x': 947, 'y': 85}, 'selected': False}, {'id': 'agent_1d0c8', 'data': {'v': 2, 'id': 'agent_1d0c8', 'tab': {'value': 'single', 'options': [{'key': 'single', 'label': '单次运行'}, {'key': 'batch', 'label': '批量运行'}]}, 'name': '多轮对话助手', 'type': 'agent', 'expand': True, 'description': 'AI 自主进行任务规划，选择合适的知识库或工具进行调用。', 'group_params': [{'params': [{'key': 'batch_variable', 'tab': 'batch', 'help': '选择需要批处理的变量，将会多次运行本节点，每次运行时从选择的变量中取一项赋值给batch_variable进行处理。', 'type': 'user_question', 'label': '批处理变量', 'value': [], 'global': 'self', 'linkage': 'output', 'required': True, 'placeholder': '请选择批处理变量'}]}, {'name': '模型设置', 'params': [{'key': 'model_id', 'type': 'agent_model', 'label': '模型', 'value': 2, 'required': True, 'placeholder': '请选择模型'}, {'key': 'temperature', 'step': 0.1, 'type': 'slide', 'label': '温度', 'scope': [0, 2], 'value': 0.7}]}, {'name': '提示词', 'params': [{'key': 'system_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '系统提示词', 'value': '你是一个熟悉各行各业的 AI 助理，善于根据用户需求，适时调用合适的工具来回答用户问题', 'required': True, 'placeholder': '助手画像'}, {'key': 'user_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '用户提示词', 'value': '{{#input_2775b.user_input#}}', 'varZh': {'input_2775b.user_input': '输入/user_input'}, 'required': True, 'placeholder': '用户消息内容'}, {'key': 'chat_history_flag', 'help': '带入模型上下文的历史消息条数，为 0 时代表不包含上下文信息。', 'step': 1, 'type': 'slide_switch', 'label': '历史聊天记录', 'scope': [0, 100], 'value': {'flag': True, 'value': 50}}, {'key': 'image_prompt', 'help': '当使用多模态大模型时，可通过此功能传入图片，结合图像内容进行问答', 'type': 'image_prompt', 'label': '视觉', 'value': ''}]}, {'name': '知识库', 'params': [{'key': 'knowledge_id', 'type': 'knowledge_select_multi', 'label': '检索知识库范围', 'value': {'type': 'knowledge', 'value': []}, 'placeholder': '请选择知识库'}]}, {'name': '数据库', 'params': [{'key': 'sql_agent', 'type': 'sql_config', 'value': {'open': False, 'db_name': '', 'db_address': '', 'db_password': '', 'db_username': ''}}]}, {'name': '工具', 'params': [{'key': 'tool_list', 'type': 'add_tool', 'label': '+ 添加工具', 'value': []}]}, {'name': '输出', 'params': [{'key': 'output_user', 'help': '一般在问答等场景开启，文档审核、报告生成等场景可关闭。', 'type': 'switch', 'label': '将输出结果展示在会话中', 'value': True}, {'key': 'output', 'type': 'var', 'label': '输出变量', 'value': [], 'global': 'code:value.map(el => ({ label: el.label, value: el.key }))'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 1202}, 'position': {'x': 1416.1256404922672, 'y': 89.69476666183581}, 'selected': True}], 'edges': [{'id': 'xy-edge__start_553b9right_handle-input_2775bleft_handle', 'type': 'customEdge', 'source': 'start_553b9', 'target': 'input_2775b', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__input_2775bright_handle-agent_1d0c8left_handle', 'type': 'customEdge', 'source': 'input_2775b', 'target': 'agent_1d0c8', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__agent_1d0c8right_handle-input_2775bleft_handle', 'type': 'customEdge', 'source': 'agent_1d0c8', 'target': 'input_2775b', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}], 'viewport': {'x': 202.67642774879823, 'y': -1.8593657130471684, 'zoom': 0.7423265855532226}}}
[2025-08-22 15:16:36.232811] [INFO process-5916-26592 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:16:37.242498] [INFO process-5916-26592 bisheng.utils.threadpool:67] - trace=0d79571125cf447098e0df1c1e484ef6 async_task_added fun=wrapper_task args=0d79571125cf447098e0df1c1e484ef6
[2025-08-22 15:16:39.174519] [INFO process-5916-264 bisheng.chat.clients.base:68] - trace=1 client_id=8cce76dfbb4240e7b7f16aa24580ae6b trace_id=ba66a3b712fb4add9780e4e7355d84d3 message={'action': 'input', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'data': {'input_2775b': {'data': {'user_input': '今天贵州茅台股价情况？', 'dialog_files_content': []}, 'message': '今天贵州茅台股价情况？', 'message_id': 'b99eda5bb862420e8840e9673c2c3619', 'category': 'question', 'extra': '', 'source': 0}}}
[2025-08-22 15:16:39.193524] [INFO process-5916-31664 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:16:39.208856] [INFO process-5916-26804 bisheng.chat.clients.workflow_client:182] - trace=ba66a3b712fb4add9780e4e7355d84d3 get user input: {'input_2775b': {'data': {'user_input': '今天贵州茅台股价情况？', 'dialog_files_content': []}, 'message': '今天贵州茅台股价情况？', 'message_id': 'b99eda5bb862420e8840e9673c2c3619', 'category': 'question', 'extra': '', 'source': 0}}
[2025-08-22 15:16:40.213753] [INFO process-5916-31664 bisheng.utils.threadpool:67] - trace=ba66a3b712fb4add9780e4e7355d84d3 async_task_added fun=wrapper_task args=ba66a3b712fb4add9780e4e7355d84d3
[2025-08-22 15:17:31.573391] [INFO process-5916-264 bisheng.chat.clients.base:68] - trace=1 client_id=8cce76dfbb4240e7b7f16aa24580ae6b trace_id=804d7ba0db4f4c2ab4a6801af7f158d8 message={'action': 'stop'}
[2025-08-22 15:17:31.594477] [INFO process-5916-264 bisheng.chat.clients.base:68] - trace=1 client_id=8cce76dfbb4240e7b7f16aa24580ae6b trace_id=0787bfebe7a84f74aab2d43d9e89e073 message={'action': 'init_data', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'data': {'status': 2, 'description': '支持添加知识库和工具', 'user_id': 1, 'guide_word': None, 'create_time': '2025-08-21T10:54:24', 'logo': '', 'name': '多轮对话-479e3', 'flow_type': 10, 'update_time': '2025-08-21T10:58:11', 'id': 'ac8b23aed25947908ac042dc5b5f1594', 'nodes': [{'id': 'start_553b9', 'data': {'v': 1, 'id': 'start_553b9', 'name': '开始', 'type': 'start', 'description': '工作流运行的起始节点。', 'group_params': [{'name': '开场引导', 'params': [{'key': 'guide_word', 'type': 'textarea', 'label': '开场白', 'value': '您好，请问想聊些什么呢？', 'placeholder': '每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。'}, {'key': 'guide_question', 'help': '为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', 'type': 'input_list', 'label': '引导问题', 'value': ['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], 'placeholder': '请输入引导问题'}]}, {'name': '全局变量', 'params': [{'key': 'current_time', 'type': 'var', 'label': '当前时间', 'value': '', 'global': 'key'}, {'key': 'chat_history', 'type': 'chat_history_num', 'value': 10, 'global': 'key'}, {'key': 'preset_question', 'help': '适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', 'type': 'input_list', 'label': '预置问题列表', 'value': [{'key': 'f4cbae', 'value': ''}], 'global': 'item:input_list', 'placeholder': '输入批量预置问题'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 702}, 'position': {'x': 491, 'y': 63.125}, 'selected': False}, {'id': 'input_2775b', 'data': {'v': 2, 'id': 'input_2775b', 'tab': {'value': 'dialog_input', 'options': [{'key': 'dialog_input', 'help': '接收用户从对话框输入的内容。!(input)', 'label': '对话框输入'}, {'key': 'form_input', 'help': '将会在用户会话界面弹出一个表单，接收用户从表单提交的内容。!(form)', 'label': '表单输入'}]}, 'name': '输入', 'type': 'input', 'expand': False, 'description': '接收用户在会话页面的输入，支持 2 种形式：对话框输入，表单输入。', 'group_params': [{'name': '', 'params': [{'key': 'user_input', 'tab': 'dialog_input', 'type': 'var', 'label': '用户输入内容', 'global': 'key'}, {'key': 'form_input', 'tab': 'form_input', 'type': 'form', 'label': '+ 添加表单项', 'value': [], 'global': 'item:form_input'}, {'key': 'dialog_files_content', 'tab': 'dialog_input', 'type': 'var', 'label': '上传文件内容', 'global': 'key'}, {'key': 'dialog_files_content_size', 'min': 0, 'tab': 'dialog_input', 'type': 'char_number', 'label': '文件内容长度上限', 'value': 15000}, {'key': 'dialog_file_accept', 'tab': 'dialog_input', 'type': 'select_fileaccept', 'label': '上传文件类型', 'value': 'all'}, {'key': 'dialog_image_files', 'tab': 'dialog_input', 'help': '提取上传文件中的图片文件，当助手或大模型节点使用多模态大模型时，可传入此图片。', 'type': 'var', 'label': '上传图片文件', 'global': 'key'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 149}, 'position': {'x': 947, 'y': 85}, 'selected': False}, {'id': 'agent_1d0c8', 'data': {'v': 2, 'id': 'agent_1d0c8', 'tab': {'value': 'single', 'options': [{'key': 'single', 'label': '单次运行'}, {'key': 'batch', 'label': '批量运行'}]}, 'name': '多轮对话助手', 'type': 'agent', 'expand': True, 'description': 'AI 自主进行任务规划，选择合适的知识库或工具进行调用。', 'group_params': [{'params': [{'key': 'batch_variable', 'tab': 'batch', 'help': '选择需要批处理的变量，将会多次运行本节点，每次运行时从选择的变量中取一项赋值给batch_variable进行处理。', 'type': 'user_question', 'label': '批处理变量', 'value': [], 'global': 'self', 'linkage': 'output', 'required': True, 'placeholder': '请选择批处理变量'}]}, {'name': '模型设置', 'params': [{'key': 'model_id', 'type': 'agent_model', 'label': '模型', 'value': 2, 'required': True, 'placeholder': '请选择模型'}, {'key': 'temperature', 'step': 0.1, 'type': 'slide', 'label': '温度', 'scope': [0, 2], 'value': 0.7}]}, {'name': '提示词', 'params': [{'key': 'system_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '系统提示词', 'value': '你是一个熟悉各行各业的 AI 助理，善于根据用户需求，适时调用合适的工具来回答用户问题', 'required': True, 'placeholder': '助手画像'}, {'key': 'user_prompt', 'test': 'var', 'type': 'var_textarea', 'label': '用户提示词', 'value': '{{#input_2775b.user_input#}}', 'varZh': {'input_2775b.user_input': '输入/user_input'}, 'required': True, 'placeholder': '用户消息内容'}, {'key': 'chat_history_flag', 'help': '带入模型上下文的历史消息条数，为 0 时代表不包含上下文信息。', 'step': 1, 'type': 'slide_switch', 'label': '历史聊天记录', 'scope': [0, 100], 'value': {'flag': True, 'value': 50}}, {'key': 'image_prompt', 'help': '当使用多模态大模型时，可通过此功能传入图片，结合图像内容进行问答', 'type': 'image_prompt', 'label': '视觉', 'value': ''}]}, {'name': '知识库', 'params': [{'key': 'knowledge_id', 'type': 'knowledge_select_multi', 'label': '检索知识库范围', 'value': {'type': 'knowledge', 'value': []}, 'placeholder': '请选择知识库'}]}, {'name': '数据库', 'params': [{'key': 'sql_agent', 'type': 'sql_config', 'value': {'open': False, 'db_name': '', 'db_address': '', 'db_password': '', 'db_username': ''}}]}, {'name': '工具', 'params': [{'key': 'tool_list', 'type': 'add_tool', 'label': '+ 添加工具', 'value': []}]}, {'name': '输出', 'params': [{'key': 'output_user', 'help': '一般在问答等场景开启，文档审核、报告生成等场景可关闭。', 'type': 'switch', 'label': '将输出结果展示在会话中', 'value': True}, {'key': 'output', 'type': 'var', 'label': '输出变量', 'value': [], 'global': 'code:value.map(el => ({ label: el.label, value: el.key }))'}]}]}, 'type': 'flowNode', 'dragging': False, 'measured': {'width': 334, 'height': 1202}, 'position': {'x': 1416.1256404922672, 'y': 89.69476666183581}, 'selected': True}], 'edges': [{'id': 'xy-edge__start_553b9right_handle-input_2775bleft_handle', 'type': 'customEdge', 'source': 'start_553b9', 'target': 'input_2775b', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__input_2775bright_handle-agent_1d0c8left_handle', 'type': 'customEdge', 'source': 'input_2775b', 'target': 'agent_1d0c8', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}, {'id': 'xy-edge__agent_1d0c8right_handle-input_2775bleft_handle', 'type': 'customEdge', 'source': 'agent_1d0c8', 'target': 'input_2775b', 'animated': True, 'sourceHandle': 'right_handle', 'targetHandle': 'left_handle'}], 'viewport': {'x': 202.67642774879823, 'y': -1.8593657130471684, 'zoom': 0.7423265855532226}}}
[2025-08-22 15:17:31.696924] [INFO process-5916-31892 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:17:32.715173] [INFO process-5916-31892 bisheng.utils.threadpool:67] - trace=0787bfebe7a84f74aab2d43d9e89e073 async_task_added fun=wrapper_task args=0787bfebe7a84f74aab2d43d9e89e073
[2025-08-22 15:17:35.756186] [INFO process-5916-264 bisheng.chat.clients.base:68] - trace=1 client_id=8cce76dfbb4240e7b7f16aa24580ae6b trace_id=51d382ce30da46bd8f94552be75b35f1 message={'action': 'input', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'data': {'input_2775b': {'data': {'user_input': 'i今后请我喝请我喝青蛙', 'dialog_files_content': []}, 'message': 'i今后请我喝请我喝青蛙', 'message_id': 'cd41d1b5ee5b4d7bb0be731c1b1e9831', 'category': 'question', 'extra': '', 'source': 0}}}
[2025-08-22 15:17:35.786210] [INFO process-5916-23580 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:17:35.809371] [INFO process-5916-22216 bisheng.chat.clients.workflow_client:182] - trace=51d382ce30da46bd8f94552be75b35f1 get user input: {'input_2775b': {'data': {'user_input': 'i今后请我喝请我喝青蛙', 'dialog_files_content': []}, 'message': 'i今后请我喝请我喝青蛙', 'message_id': 'cd41d1b5ee5b4d7bb0be731c1b1e9831', 'category': 'question', 'extra': '', 'source': 0}}
[2025-08-22 15:17:36.805301] [INFO process-5916-23580 bisheng.utils.threadpool:67] - trace=51d382ce30da46bd8f94552be75b35f1 async_task_added fun=wrapper_task args=51d382ce30da46bd8f94552be75b35f1
[2025-08-22 15:17:39.510817] [ERROR process-5916-264 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=18>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=18>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000001FD382DB010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 55305, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000001FD382DA050>
              └ <__main__.PyDB object at 0x000001FD382B5540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000001FD382DA0E0>
           └ <__main__.PyDB object at 0x000001FD382B5540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000001FD37CBEF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x000001FD785D0220>
    │       └ <function run at 0x000001FD77401090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000001FD77400AF0>
    └ <uvicorn.server.Server object at 0x000001FD7F0BB5E0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001FD77400B80>
           │       │   └ <uvicorn.server.Server object at 0x000001FD7F0BB5E0>
           │       └ <function _patch_asyncio.<locals>.run at 0x000001FD383217E0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x000001FD38322290>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x000001FD38322320>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001FD381DFA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=18>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=18>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=18>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=18>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=50>
           │    └ <_ProactorSocketTransport fd=7048 read=<_OverlappedFuture pending overlapped=<pending, 0x1fd7f149ab0> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=18>

AssertionError: assert f is self._write_fut
[2025-08-22 15:17:43.084813] [ERROR process-5916-264 asyncio.base_events:1729] - trace=1 Exception in callback _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=28>)
handle: <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=28>)>
Traceback (most recent call last):

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2238, in <module>
    main()
    └ <function main at 0x000001FD382DB010>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 2220, in main
    globals = debugger.run(setup['file'], None, None, is_module)
              │        │   │                          └ False
              │        │   └ {'port': 55305, 'vm_type': None, 'client': '127.0.0.1', 'server': False, 'DEBUG_RECORD_SOCKET_READS': False, 'multiproc': Fal...
              │        └ <function PyDB.run at 0x000001FD382DA050>
              └ <__main__.PyDB object at 0x000001FD382B5540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1530, in run
    return self._exec(is_module, entry_point_fn, module_name, file, globals, locals)
           │    │     │          │               │            │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
           │    │     │          │               │            └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
           │    │     │          │               └ None
           │    │     │          └ ''
           │    │     └ False
           │    └ <function PyDB._exec at 0x000001FD382DA0E0>
           └ <__main__.PyDB object at 0x000001FD382B5540>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\pydevd.py", line 1537, in _exec
    pydev_imports.execfile(file, globals, locals)  # execute the script
    │             │        │     │        └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
    │             │        └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
    │             └ <function execfile at 0x000001FD37CBEF80>
    └ <module '_pydev_bundle.pydev_imports' from 'D:\\Program Files\\JetBrains\\PyCharm 2024.1.2\\plugins\\python\\helpers\\pydev\\...

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers\pydev\_pydev_imps\_pydev_execfile.py", line 18, in execfile
    exec(compile(contents+"\n", file, 'exec'), glob, loc)
                 │              │              │     └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              │              └ {'__name__': '__main__', '__doc__': None, '__package__': '', '__loader__': <_frozen_importlib_external.SourceFileLoader objec...
                 │              └ 'D:\\projects\\postwise-v2.0\\postwise\\src\\backend\\bisheng\\main.py'
                 └ 'from contextlib import asynccontextmanager\nfrom pathlib import Path\nfrom typing import Optional\n\nfrom fastapi import Fas...

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\main.py", line 153, in <module>
    uvicorn.run(app, host='0.0.0.0', port=7860, workers=1)
    │       │   └ <fastapi.applications.FastAPI object at 0x000001FD785D0220>
    │       └ <function run at 0x000001FD77401090>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\uvicorn\\_...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000001FD77400AF0>
    └ <uvicorn.server.Server object at 0x000001FD7F0BB5E0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001FD77400B80>
           │       │   └ <uvicorn.server.Server object at 0x000001FD7F0BB5E0>
           │       └ <function _patch_asyncio.<locals>.run at 0x000001FD383217E0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 138, in run
    return loop.run_until_complete(task)
           │    │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python310\li...
           │    └ <function _patch_loop.<locals>.run_until_complete at 0x000001FD38322290>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 234, in run_until_complete
    self._run_once()
    │    └ <function _patch_loop.<locals>._run_once at 0x000001FD38322320>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "D:\Program Files\JetBrains\PyCharm 2024.1.2\plugins\python\helpers-pro\pydevd_asyncio\pydevd_nest_asyncio.py", line 286, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001FD381DFA30>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=28>)>

> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=28>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=28>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBaseWritePipeTransport._loop_writing(<_OverlappedF...hed result=28>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\proactor_events.py", line 377, in _loop_writing
    assert f is self._write_fut
           │    │    └ <_OverlappedFuture finished result=20>
           │    └ <_ProactorSocketTransport fd=7048 read=<_OverlappedFuture pending overlapped=<pending, 0x1fd7f149ab0> cb=[_ProactorReadPipeTr...
           └ <_OverlappedFuture finished result=28>

AssertionError: assert f is self._write_fut
[2025-08-22 15:18:03.681456] [INFO process-5916-264 bisheng.chat.clients.base:68] - trace=1 client_id=8cce76dfbb4240e7b7f16aa24580ae6b trace_id=aadca5582426440dae9d33c4a04fd4d6 message={'action': 'input', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'data': {'input_2775b': {'data': {'user_input': '### 4. **地方试点与实践**\n   - **深圳**：2023年率先开通首条无人机城际物流航线，探索城市低空交通管理平台。\n   - **安徽芜湖**：依托国家级通用航空产业基地，发展无人机研发制造和低空运营服务。\n   - **湖南**：在张家界等地试点低空旅游和应急救援网络，推动低空经济与文旅融合。\n\n### 5. **安全与监管**\n   - **强化“分类监管”**\n     对不同用途飞行（如载人、载货、娱乐）实施差异化管理，推动无人机实名登记和飞行数据实时监控。\n   - **立法推进**\n     《无人驾驶航空器飞行管理暂行条例》（已通过国务院审议）将明确飞行规则、责任主体和处罚机制。\n\n### 6. **未来方向**\n   - **2024年政策预期**\n     随着指导意见正式出台，预计各地将细化配套政策，低空经济可能被纳入“十四五”规划重点工程，相关产业链（如电池、飞控系统、低空 大数据）将获得更多资金和资源支持。\n\n### 建议关注点\n   - 若需具体政策原文或地方实施细则，可查阅国家空管委、民航局官网或地方发改委公告。\n   - 重点关注低空制造（如eVTOL整机及零部件）、低空运营（物流、旅游）和低空服务（空管技术、保险）三大赛道。', 'dialog_files_content': []}, 'message': '### 4. **地方试点与实践**\n   - **深圳**：2023年率先开通首条无人机城际物流航线，探索城市低空交通管理平台。\n   - **安徽芜湖**：依托国家级通用航空产业基地，发展无人机研发制造和低空运营服务。\n   - **湖南**：在张家界等地试点低空旅游和应急救援网络，推动低空经济与文旅融合。\n\n### 5. **安全与监管**\n   - **强化“分类监管”**\n     对不同用途飞行（如载人、载货、娱乐）实施差异化管理，推动无人机实名登记和飞行数据实时监控。\n   - **立法推进**\n     《无人驾驶航空器飞行管理暂行条例》（已通过国务院审议）将明确飞行规则、责任主体和处罚机制。\n\n### 6. **未来方向**\n   - **2024年政策预期**\n     随着指导意见正式出台，预计各地将细化配套政策，低空经济可能被纳入“十四五”规划重点工程，相关产业链（如电池、飞控系统、低空 大数据）将获得更多资金和资源支持。\n\n### 建议关注点\n   - 若需具体政策原文或地方实施细则，可查阅国家空管委、民航局官网或地方发改委公告。\n   - 重点关注低空制造（如eVTOL整机及零部件）、低空运营（物流、旅游）和低空服务（空管技术、保险）三大赛道。', 'message_id': 'e24d3d4480b94f13809a438aadabcfa4', 'category': 'question', 'extra': '', 'source': 0}}}
[2025-08-22 15:18:03.722347] [INFO process-5916-26592 bisheng.utils.threadpool:59] - trace=1 Creating new event loop <ProactorEventLoop running=True closed=False debug=False>
[2025-08-22 15:18:03.749216] [INFO process-5916-31792 bisheng.chat.clients.workflow_client:182] - trace=aadca5582426440dae9d33c4a04fd4d6 get user input: {'input_2775b': {'data': {'user_input': '### 4. **地方试点与实践**\n   - **深圳**：2023年率先开通首条无人机城际物流航线，探索城市低空交通管理平台。\n   - **安徽芜湖**：依托国家级通用航空产业基地，发展无人机研发制造和低空运营服务。\n   - **湖南**：在张家界等地试点低空旅游和应急救援网络，推动低空经济与文旅融合。\n\n### 5. **安全与监管**\n   - **强化“分类监管”**\n     对不同用途飞行（如载人、载货、娱乐）实施差异化管理，推动无人机实名登记和飞行数据实时监控。\n   - **立法推进**\n     《无人驾驶航空器飞行管理暂行条例》（已通过国务院审议）将明确飞行规则、责任主体和处罚机制。\n\n### 6. **未来方向**\n   - **2024年政策预期**\n     随着指导意见正式出台，预计各地将细化配套政策，低空经济可能被纳入“十四五”规划重点工程，相关产业链（如电池、飞控系统、低空 大数据）将获得更多资金和资源支持。\n\n### 建议关注点\n   - 若需具体政策原文或地方实施细则，可查阅国家空管委、民航局官网或地方发改委公告。\n   - 重点关注低空制造（如eVTOL整机及零部件）、低空运营（物流、旅游）和低空服务（空管技术、保险）三大赛道。', 'dialog_files_content': []}, 'message': '### 4. **地方试点与实践**\n   - **深圳**：2023年率先开通首条无人机城际物流航线，探索城市低空交通管理平台。\n   - **安徽芜湖**：依托国家级通用航空产业基地，发展无人机研发制造和低空运营服务。\n   - **湖南**：在张家界等地试点低空旅游和应急救援网络，推动低空经济与文旅融合。\n\n### 5. **安全与监管**\n   - **强化“分类监管”**\n     对不同用途飞行（如载人、载货、娱乐）实施差异化管理，推动无人机实名登记和飞行数据实时监控。\n   - **立法推进**\n     《无人驾驶航空器飞行管理暂行条例》（已通过国务院审议）将明确飞行规则、责任主体和处罚机制。\n\n### 6. **未来方向**\n   - **2024年政策预期**\n     随着指导意见正式出台，预计各地将细化配套政策，低空经济可能被纳入“十四五”规划重点工程，相关产业链（如电池、飞控系统、低空 大数据）将获得更多资金和资源支持。\n\n### 建议关注点\n   - 若需具体政策原文或地方实施细则，可查阅国家空管委、民航局官网或地方发改委公告。\n   - 重点关注低空制造（如eVTOL整机及零部件）、低空运营（物流、旅游）和低空服务（空管技术、保险）三大赛道。', 'message_id': 'e24d3d4480b94f13809a438aadabcfa4', 'category': 'question', 'extra': '', 'source': 0}}
[2025-08-22 15:18:04.748691] [INFO process-5916-26592 bisheng.utils.threadpool:67] - trace=aadca5582426440dae9d33c4a04fd4d6 async_task_added fun=wrapper_task args=aadca5582426440dae9d33c4a04fd4d6
[2025-08-22 15:21:49.272842] [INFO process-5916-264 bisheng.chat.manager:243] - trace=1 act=rcv_client_disconnect (1006, None)
[2025-08-22 15:21:49.289509] [INFO process-5916-264 bisheng.chat.manager:181] - trace=1 close_client client_key=8cce76dfbb4240e7b7f16aa24580ae6b
[2025-08-22 15:21:49.302616] [ERROR process-5916-264 bisheng.chat.manager:194] - trace=1 Unexpected ASGI message 'websocket.close', after sending 'websocket.close'.
[2025-08-22 15:21:49.316982] [ERROR process-5916-23824 bisheng.chat.clients.base:61] - trace=2 consume_message error {'is_bot': 1, 'message': '', 'type': 'close', 'category': 'processing', 'intermediate_steps': '', 'files': [], 'user_id': 1, 'message_id': None, 'source': 0, 'sender': None, 'receiver': None, 'liked': 0, 'extra': '{"client_key": "8cce76dfbb4240e7b7f16aa24580ae6b"}', 'flow_id': 'ac8b23aed25947908ac042dc5b5f1594', 'chat_id': ''} error: Cannot call "send" once a close message has been sent.
[2025-08-22 15:21:49.344418] [WARNING process-5916-264 bisheng.chat.manager:179] - trace=1 close_client client_key=8cce76dfbb4240e7b7f16aa24580ae6b not in active_clients
[2025-08-22 15:24:35.935246] [INFO process-27048-21344 datasets.config:58] - trace=1 PyTorch version 2.8.0 available.
[2025-08-22 15:24:43.237746] [INFO process-27048-21344 bisheng.interface.utils:92] - trace=1 LLM caching setup with InMemoryCache
