[2025-08-21 16:52:42.124305] [INFO process-76240-78516 datasets.config:58] - trace=1 PyTorch version 2.8.0 available.
[2025-08-21 17:33:39.276726] [INFO process-81152-79324 datasets.config:58] - trace=1 PyTorch version 2.8.0 available.
[2025-08-21 17:39:14.626798] [INFO process-82016-78060 datasets.config:58] - trace=1 PyTorch version 2.8.0 available.
[2025-08-21 17:55:37.790262] [INFO process-43872-77996 datasets.config:58] - trace=1 PyTorch version 2.8.0 available.
[2025-08-21 19:57:33.677687] [INFO process-25024-26708 datasets.config:58] - trace=1 PyTorch version 2.8.0 available.
[2025-08-21 19:59:55.495156] [INFO process-30792-31192 datasets.config:58] - trace=1 PyTorch version 2.8.0 available.
[2025-08-21 20:31:41.051389] [INFO process-33628-30248 datasets.config:58] - trace=1 PyTorch version 2.8.0 available.
[2025-08-21 20:34:26.110524] [INFO process-27640-32516 datasets.config:58] - trace=1 PyTorch version 2.8.0 available.
[2025-08-21 20:39:36.237505] [INFO process-30956-16448 datasets.config:58] - trace=1 PyTorch version 2.8.0 available.
[2025-08-21 20:44:23.590096] [INFO process-32644-15628 datasets.config:58] - trace=1 PyTorch version 2.8.0 available.
[2025-08-21 21:03:26.820341] [INFO process-7312-34480 datasets.config:58] - trace=1 PyTorch version 2.8.0 available.
[2025-08-21 21:04:01.176746] [INFO process-7312-34480 bisheng.workflow.nodes.start.start:32] - trace=c1763dce09224bada325d55c8cf658dd === StartNode self 内部信息 ===
[2025-08-21 21:04:01.188647] [INFO process-7312-34480 bisheng.workflow.nodes.start.start:34] - trace=c1763dce09224bada325d55c8cf658dd id: start_cf1d5
[2025-08-21 21:04:01.198053] [INFO process-7312-34480 bisheng.workflow.nodes.start.start:34] - trace=c1763dce09224bada325d55c8cf658dd type: start
[2025-08-21 21:04:01.206645] [INFO process-7312-34480 bisheng.workflow.nodes.start.start:34] - trace=c1763dce09224bada325d55c8cf658dd name: 开始
[2025-08-21 21:04:01.220272] [INFO process-7312-34480 bisheng.workflow.nodes.start.start:34] - trace=c1763dce09224bada325d55c8cf658dd description: 工作流运行的起始节点。
[2025-08-21 21:04:01.240006] [INFO process-7312-34480 bisheng.workflow.nodes.start.start:34] - trace=c1763dce09224bada325d55c8cf658dd target_edges: [EdgeBase(id='xy-edge__start_cf1d5right_handle-input_cd2e7left_handle', source='start_cf1d5', sourceHandle='right_handle', sourceType='', target='input_cd2e7', targetHandle='left_handle', targetType='')]
[2025-08-21 21:04:01.249558] [INFO process-7312-34480 bisheng.workflow.nodes.start.start:34] - trace=c1763dce09224bada325d55c8cf658dd user_id: 1
[2025-08-21 21:04:01.256761] [INFO process-7312-34480 bisheng.workflow.nodes.start.start:34] - trace=c1763dce09224bada325d55c8cf658dd workflow_id: af36bdf6ec1946d7ad88cce80712bbce
[2025-08-21 21:04:01.265560] [INFO process-7312-34480 bisheng.workflow.nodes.start.start:34] - trace=c1763dce09224bada325d55c8cf658dd graph_state: history_memory=ConversationBufferWindowMemory(chat_memory=InMemoryChatMessageHistory(messages=[]), k=10) variables_pool={}
[2025-08-21 21:04:01.276710] [INFO process-7312-34480 bisheng.workflow.nodes.start.start:34] - trace=c1763dce09224bada325d55c8cf658dd node_data: id='start_cf1d5' type='start' name='开始' description='工作流运行的起始节点。' group_params=[NodeGroupParams(name='开场引导', params=[NodeParams(key='guide_word', label='开场白', value='', type='textarea', help='', tab='', placeholder='每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。', required=False, options=None), NodeParams(key='guide_question', label='引导问题', value=[''], type='input_list', help='为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', tab='', placeholder='请输入引导问题', required=False, options=None)], description=''), NodeGroupParams(name='全局变量', params=[NodeParams(key='current_time', label='当前时间', value='', type='var', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='chat_history', label='', value=10, type='chat_history_num', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='user_id', label='用户ID', value='', type='var', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='app_id', label='应用ID', value='', type='var', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='preset_question', label='预置问题列表', value=[{'key': '785b7d', 'value': ''}], type='input_list', help='适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', tab='', placeholder='输入批量预置问题', required=False, options=None)], description='')] tab={} tool_key='' v=1
[2025-08-21 21:04:01.306348] [INFO process-7312-34480 bisheng.workflow.nodes.start.start:34] - trace=c1763dce09224bada325d55c8cf658dd node_params: {'guide_word': '', 'guide_question': [''], 'current_time': '2025-08-21 21:04:00', 'chat_history': 10, 'user_id': '', 'app_id': '', 'preset_question': [{'key': '785b7d', 'value': ''}]}
[2025-08-21 21:04:01.315271] [INFO process-7312-34480 bisheng.workflow.nodes.start.start:34] - trace=c1763dce09224bada325d55c8cf658dd other_node_variable: {}
[2025-08-21 21:04:01.321795] [INFO process-7312-34480 bisheng.workflow.nodes.start.start:34] - trace=c1763dce09224bada325d55c8cf658dd current_step: 0
[2025-08-21 21:04:01.327796] [INFO process-7312-34480 bisheng.workflow.nodes.start.start:34] - trace=c1763dce09224bada325d55c8cf658dd max_steps: 50
[2025-08-21 21:04:01.333851] [INFO process-7312-34480 bisheng.workflow.nodes.start.start:34] - trace=c1763dce09224bada325d55c8cf658dd callback_manager: <bisheng.worker.workflow.redis_callback.RedisCallback object at 0x000002CA207AFD00>
[2025-08-21 21:04:01.340948] [INFO process-7312-34480 bisheng.workflow.nodes.start.start:34] - trace=c1763dce09224bada325d55c8cf658dd tmp_collection_name: tmp_workflow_data
[2025-08-21 21:04:01.345524] [INFO process-7312-34480 bisheng.workflow.nodes.start.start:34] - trace=c1763dce09224bada325d55c8cf658dd stop_flag: False
[2025-08-21 21:04:01.350848] [INFO process-7312-34480 bisheng.workflow.nodes.start.start:34] - trace=c1763dce09224bada325d55c8cf658dd exec_unique_id: 75cbc5d425af44c6b48f46d41f220f8f
[2025-08-21 21:04:01.358073] [INFO process-7312-34480 bisheng.workflow.nodes.start.start:35] - trace=c1763dce09224bada325d55c8cf658dd === StartNode self 信息结束 ===
[2025-08-21 21:12:56.372473] [INFO process-35508-12804 datasets.config:58] - trace=1 PyTorch version 2.8.0 available.
[2025-08-21 21:16:05.868527] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:32] - trace=d3cda272d3d44d3b8078e58167a5f747 === StartNode self 内部信息 ===
[2025-08-21 21:16:05.872269] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=d3cda272d3d44d3b8078e58167a5f747 id: start_cf1d5
[2025-08-21 21:16:05.875272] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=d3cda272d3d44d3b8078e58167a5f747 type: start
[2025-08-21 21:16:05.879271] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=d3cda272d3d44d3b8078e58167a5f747 name: 开始
[2025-08-21 21:16:05.882760] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=d3cda272d3d44d3b8078e58167a5f747 description: 工作流运行的起始节点。
[2025-08-21 21:16:05.886472] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=d3cda272d3d44d3b8078e58167a5f747 target_edges: [EdgeBase(id='xy-edge__start_cf1d5right_handle-input_cd2e7left_handle', source='start_cf1d5', sourceHandle='right_handle', sourceType='', target='input_cd2e7', targetHandle='left_handle', targetType='')]
[2025-08-21 21:16:05.890583] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=d3cda272d3d44d3b8078e58167a5f747 user_id: 1
[2025-08-21 21:16:05.893696] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=d3cda272d3d44d3b8078e58167a5f747 workflow_id: af36bdf6ec1946d7ad88cce80712bbce
[2025-08-21 21:16:05.897003] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=d3cda272d3d44d3b8078e58167a5f747 graph_state: history_memory=ConversationBufferWindowMemory(chat_memory=InMemoryChatMessageHistory(messages=[]), k=10) variables_pool={}
[2025-08-21 21:16:05.902102] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=d3cda272d3d44d3b8078e58167a5f747 node_data: id='start_cf1d5' type='start' name='开始' description='工作流运行的起始节点。' group_params=[NodeGroupParams(name='开场引导', params=[NodeParams(key='guide_word', label='开场白', value='', type='textarea', help='', tab='', placeholder='每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。', required=False, options=None), NodeParams(key='guide_question', label='引导问题', value=[''], type='input_list', help='为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', tab='', placeholder='请输入引导问题', required=False, options=None)], description=''), NodeGroupParams(name='全局变量', params=[NodeParams(key='current_time', label='当前时间', value='', type='var', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='chat_history', label='', value=10, type='chat_history_num', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='user_id', label='用户ID', value='', type='var', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='app_id', label='应用ID', value='', type='var', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='preset_question', label='预置问题列表', value=[{'key': '785b7d', 'value': ''}], type='input_list', help='适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', tab='', placeholder='输入批量预置问题', required=False, options=None)], description='')] tab={} tool_key='' v=1
[2025-08-21 21:16:05.914793] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=d3cda272d3d44d3b8078e58167a5f747 node_params: {'guide_word': '', 'guide_question': [''], 'current_time': '2025-08-21 21:16:05', 'chat_history': 10, 'user_id': '', 'app_id': '', 'preset_question': [{'key': '785b7d', 'value': ''}]}
[2025-08-21 21:16:05.920195] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=d3cda272d3d44d3b8078e58167a5f747 other_node_variable: {}
[2025-08-21 21:16:05.923192] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=d3cda272d3d44d3b8078e58167a5f747 current_step: 0
[2025-08-21 21:16:05.926534] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=d3cda272d3d44d3b8078e58167a5f747 max_steps: 50
[2025-08-21 21:16:05.929798] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=d3cda272d3d44d3b8078e58167a5f747 callback_manager: <bisheng.worker.workflow.redis_callback.RedisCallback object at 0x0000021891C63F40>
[2025-08-21 21:16:05.933008] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=d3cda272d3d44d3b8078e58167a5f747 tmp_collection_name: tmp_workflow_data
[2025-08-21 21:16:05.936529] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=d3cda272d3d44d3b8078e58167a5f747 stop_flag: False
[2025-08-21 21:16:05.958453] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=d3cda272d3d44d3b8078e58167a5f747 exec_unique_id: dbde14874f3c476ab1f16c31cec5d169
[2025-08-21 21:16:05.962085] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:35] - trace=d3cda272d3d44d3b8078e58167a5f747 === StartNode self 信息结束 ===
[2025-08-21 21:58:50.938199] [ERROR process-35508-12804 bisheng.worker.workflow.tasks:98] - trace=2fbb8f93f89240648248ace7636f4842 continue_workflow error
Traceback (most recent call last):

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
           │         │     └ {'__name__': '__main__', '__doc__': 'Entry-point for the :program:`celery` umbrella command.', '__package__': 'celery', '__lo...
           │         └ <code object <module> at 0x00000218B276B100, file "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pa...
           └ <function _run_code at 0x00000218B2789CF0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': 'Entry-point for the :program:`celery` umbrella command.', '__package__': 'celery', '__lo...
         └ <code object <module> at 0x00000218B276B100, file "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pa...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\__main__.py", line 19, in <module>
    main()
    └ <function main at 0x00000218B278A710>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\__main__.py", line 15, in main
    sys.exit(_main())
    │   │    └ <function main at 0x00000218B40B0550>
    │   └ <built-in function exit>
    └ <module 'sys' (built-in)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
           └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {'auto_envvar_prefix': 'CELERY'}
           │    │     └ ()
           │    └ <function Command.main at 0x00000218B3673C70>
           └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x00000218B255FB50>
         │    └ <function Group.invoke at 0x00000218B367C670>
         └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1830, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x00000218B408B190>
           │               │       │       └ <function Command.invoke at 0x00000218B3673B50>
           │               │       └ <CeleryDaemonCommand worker>
           │               └ <click.core.Context object at 0x00000218B408B190>
           └ <function Group.invoke.<locals>._process_result at 0x00000218B40B0670>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │   │      │    │           └ <click.core.Context object at 0x00000218B408B190>
           │   │      │    └ <function worker at 0x00000218B40B13F0>
           │   │      └ <CeleryDaemonCommand worker>
           │   └ <function Context.invoke at 0x00000218B3672EF0>
           └ <click.core.Context object at 0x00000218B408B190>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │         └ ()
           └ <function worker at 0x00000218B40B13F0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\decorators.py", line 34, in new_func
    return f(get_current_context(), *args, **kwargs)
           │ │                       │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │ │                       └ ()
           │ └ <function get_current_context at 0x00000218B35EF1C0>
           └ <function worker at 0x00000218B40B1360>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
           │ │     │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │ │     └ ()
           │ └ <click.core.Context object at 0x00000218B408B190>
           └ <function worker at 0x00000218B40B12D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\worker.py", line 356, in worker
    worker.start()
    │      └ <function WorkController.start at 0x00000218B3FE2440>
    └ <Worker: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\worker.py", line 203, in start
    self.blueprint.start(self)
    │    │         │     └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │    │         └ <function Blueprint.start at 0x00000218B3FCECB0>
    │    └ <celery.worker.worker.WorkController.Blueprint object at 0x0000021891AE5ED0>
    └ <Worker: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 116, in start
    step.start(parent)
    │    │     └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │    └ <function StartStopStep.start at 0x00000218B3FCFD90>
    └ <step: Consumer>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 365, in start
    return self.obj.start()
           │    │   └ <function Consumer.start at 0x0000021891B70820>
           │    └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
           └ <step: Consumer>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 341, in start
    blueprint.start(self)
    │         │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │         └ <function Blueprint.start at 0x00000218B3FCECB0>
    └ <celery.worker.consumer.consumer.Consumer.Blueprint object at 0x0000021891B2E050>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 116, in start
    step.start(parent)
    │    │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │    └ <function Evloop.start at 0x0000021891B71990>
    └ <step: event loop>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 772, in start
    c.loop(*c.loop_args())
    │ │     │ └ <function Consumer.loop_args at 0x0000021891B70CA0>
    │ │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │ └ <function synloop at 0x0000021891B43F40>
    └ <Consumer: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\loops.py", line 143, in synloop
    _loop_cycle()
    └ <function synloop.<locals>._loop_cycle at 0x0000021891C74310>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\loops.py", line 132, in _loop_cycle
    connection.drain_events(timeout=2.0)
    │          └ <function Connection.drain_events at 0x00000218B3455750>
    └ <Connection: redis://172.20.20.128:6379/2 at 0x21891bf99c0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\connection.py", line 341, in drain_events
    return self.transport.drain_events(self.connection, **kwargs)
           │    │                      │    │             └ {'timeout': 2.0}
           │    │                      │    └ <property object at 0x00000218B3449850>
           │    │                      └ <Connection: redis://172.20.20.128:6379/2 at 0x21891bf99c0>
           │    └ <property object at 0x00000218B3449940>
           └ <Connection: redis://172.20.20.128:6379/2 at 0x21891bf99c0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 997, in drain_events
    get(self._deliver, timeout=timeout)
    │   │    │                 └ 2.0
    │   │    └ <function Transport._deliver at 0x0000021891B39240>
    │   └ <kombu.transport.redis.Transport object at 0x0000021891BFB340>
    └ <bound method MultiChannelPoller.get of <kombu.transport.redis.MultiChannelPoller object at 0x0000021891BFB430>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 598, in get
    ret = self.handle_event(fileno, event)
          │    │            │       └ 1
          │    │            └ 7840
          │    └ <function MultiChannelPoller.handle_event at 0x0000021891B3B370>
          └ <kombu.transport.redis.MultiChannelPoller object at 0x0000021891BFB430>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 580, in handle_event
    return self.on_readable(fileno), self
           │    │           │        └ <kombu.transport.redis.MultiChannelPoller object at 0x0000021891BFB430>
           │    │           └ 7840
           │    └ <function MultiChannelPoller.on_readable at 0x0000021891B3B2E0>
           └ <kombu.transport.redis.MultiChannelPoller object at 0x0000021891BFB430>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 576, in on_readable
    chan.handlers[type]()
    │    │        └ 'BRPOP'
    │    └ {'BRPOP': <bound method Channel._brpop_read of <kombu.transport.redis.Channel object at 0x0000021891BFB3D0>>, 'LISTEN': <boun...
    └ <kombu.transport.redis.Channel object at 0x0000021891BFB3D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 986, in _brpop_read
    self.connection._deliver(loads(bytes_to_str(item)), dest)
    │    │          │        │     │            │       └ 'workflow_celery'
    │    │          │        │     │            └ b'{"body": "W1siMmZiYjhmOTNmODkyNDA2NDgyNDhhY2U3NjM2ZjQ4NDIiLCAiYWYzNmJkZjZlYzE5NDZkN2FkODhjY2U4MDcxMmJiY2UiLCAiIiwgIjEiXSwge...
    │    │          │        │     └ <function bytes_to_str at 0x00000218B33BEF80>
    │    │          │        └ <function loads at 0x00000218B3463010>
    │    │          └ <function Transport._deliver at 0x0000021891B39240>
    │    └ <kombu.transport.redis.Transport object at 0x0000021891BFB340>
    └ <kombu.transport.redis.Channel object at 0x0000021891BFB3D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 1017, in _deliver
    callback(message)
    │        └ {'body': 'W1siMmZiYjhmOTNmODkyNDA2NDgyNDhhY2U3NjM2ZjQ4NDIiLCAiYWYzNmJkZjZlYzE5NDZkN2FkODhjY2U4MDcxMmJiY2UiLCAiIiwgIjEiXSwge30...
    └ <function Channel.basic_consume.<locals>._callback at 0x0000021891C74160>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 639, in _callback
    return callback(message)
           │        └ <Message object at 0x21891c75870 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': '1bbc...
           └ <bound method Consumer._receive_callback of <Consumer: [<Queue workflow_celery -> <Exchange workflow_celery(direct) bound to ...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\messaging.py", line 668, in _receive_callback
    return on_m(message) if on_m else self.receive(decoded, message)
           │    │           │         │    │       │        └ <Message object at 0x21891c75870 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': '1bbc...
           │    │           │         │    │       └ None
           │    │           │         │    └ <function Consumer.receive at 0x00000218B37D2170>
           │    │           │         └ <Consumer: [<Queue workflow_celery -> <Exchange workflow_celery(direct) bound to chan:1> -> workflow_celery bound to chan:1>,...
           │    │           └ <function Consumer.create_task_handler.<locals>.on_task_received at 0x0000021891C740D0>
           │    └ <Message object at 0x21891c75870 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': '1bbc...
           └ <function Consumer.create_task_handler.<locals>.on_task_received at 0x0000021891C740D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 688, in on_task_received
    strategy(
    └ <function default.<locals>.task_message_handler at 0x0000021891C12710>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\strategy.py", line 207, in task_message_handler
    handle(req)
    │      └ <Request: bisheng.worker.workflow.tasks.continue_workflow[5af1d0b4-fd92-43eb-9f53-a27fa330d8c1] ('2fbb8f93f89240648248ace7636...
    └ <bound method WorkController._process_task of <Worker: celery@WIN-S45T7NVPCSH (running)>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\worker.py", line 226, in _process_task
    req.execute_using_pool(self.pool)
    │   │                  │    └ <celery.concurrency.solo.TaskPool object at 0x0000021891B2E2F0>
    │   │                  └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │   └ <function create_request_cls.<locals>.Request.execute_using_pool at 0x0000021891C127A0>
    └ <Request: bisheng.worker.workflow.tasks.continue_workflow[5af1d0b4-fd92-43eb-9f53-a27fa330d8c1] ('2fbb8f93f89240648248ace7636...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\request.py", line 754, in execute_using_pool
    result = apply_async(
             └ <bound method BasePool.apply_async of <celery.concurrency.solo.TaskPool object at 0x0000021891B2E2F0>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\concurrency\base.py", line 153, in apply_async
    return self.on_apply(target, args, kwargs,
           │    │        │       │     └ {}
           │    │        │       └ ('bisheng.worker.workflow.tasks.continue_workflow', '5af1d0b4-fd92-43eb-9f53-a27fa330d8c1', {'lang': 'py', 'task': 'bisheng.w...
           │    │        └ <function fast_trace_task at 0x00000218B404D480>
           │    └ <function apply_target at 0x00000218B33C3130>
           └ <celery.concurrency.solo.TaskPool object at 0x0000021891B2E2F0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\concurrency\base.py", line 30, in apply_target
    ret = target(*args, **kwargs)
          │       │       └ {}
          │       └ ('bisheng.worker.workflow.tasks.continue_workflow', '5af1d0b4-fd92-43eb-9f53-a27fa330d8c1', {'lang': 'py', 'task': 'bisheng.w...
          └ <function fast_trace_task at 0x00000218B404D480>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 651, in fast_trace_task
    R, I, T, Rstr = tasks[task].__trace__(
                    │     └ 'bisheng.worker.workflow.tasks.continue_workflow'
                    └ {'celery.chord': <@task: celery.chord of bisheng at 0x218cf758d30>, 'celery.accumulate': <@task: celery.accumulate of bisheng...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 │    │       └ {}
                 │    └ ['2fbb8f93f89240648248ace7636f4842', 'af36bdf6ec1946d7ad88cce80712bbce', '', '1']
                 └ <@task: bisheng.worker.workflow.tasks.continue_workflow of bisheng at 0x218cf758d30>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           │    │    │       └ {}
           │    │    └ ('2fbb8f93f89240648248ace7636f4842', 'af36bdf6ec1946d7ad88cce80712bbce', '', '1')
           │    └ <staticmethod(<function continue_workflow at 0x000002188A804C10>)>
           └ <@task: bisheng.worker.workflow.tasks.continue_workflow of bisheng at 0x218cf758d30>

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\worker\workflow\tasks.py", line 107, in continue_workflow
    _continue_workflow(unique_id, workflow_id, chat_id, user_id)
    │                  │          │            │        └ '1'
    │                  │          │            └ ''
    │                  │          └ 'af36bdf6ec1946d7ad88cce80712bbce'
    │                  └ '2fbb8f93f89240648248ace7636f4842'
    └ <function _continue_workflow at 0x000002188A804B80>

> File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\worker\workflow\tasks.py", line 84, in _continue_workflow
    raise Exception('workflow object not found maybe data is expired')

Exception: workflow object not found maybe data is expired
[2025-08-21 21:58:51.544691] [WARNING process-35508-12804 bisheng.worker.workflow.tasks:20] - trace=2fbb8f93f89240648248ace7636f4842 workflow object not found for unique_id: 2fbb8f93f89240648248ace7636f4842
[2025-08-21 21:59:12.031207] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:32] - trace=0f6754b17576e62454d8e748b14dbfd7_async_task_id === StartNode self 内部信息 ===
[2025-08-21 21:59:12.038296] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=0f6754b17576e62454d8e748b14dbfd7_async_task_id id: start_cf1d5
[2025-08-21 21:59:12.044838] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=0f6754b17576e62454d8e748b14dbfd7_async_task_id type: start
[2025-08-21 21:59:12.057352] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=0f6754b17576e62454d8e748b14dbfd7_async_task_id name: 开始
[2025-08-21 21:59:12.064396] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=0f6754b17576e62454d8e748b14dbfd7_async_task_id description: 工作流运行的起始节点。
[2025-08-21 21:59:12.071785] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=0f6754b17576e62454d8e748b14dbfd7_async_task_id target_edges: [EdgeBase(id='xy-edge__start_cf1d5right_handle-input_cd2e7left_handle', source='start_cf1d5', sourceHandle='right_handle', sourceType='', target='input_cd2e7', targetHandle='left_handle', targetType='')]
[2025-08-21 21:59:12.079794] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=0f6754b17576e62454d8e748b14dbfd7_async_task_id user_id: 1
[2025-08-21 21:59:12.093350] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=0f6754b17576e62454d8e748b14dbfd7_async_task_id workflow_id: af36bdf6ec1946d7ad88cce80712bbce
[2025-08-21 21:59:12.102108] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=0f6754b17576e62454d8e748b14dbfd7_async_task_id graph_state: history_memory=ConversationBufferWindowMemory(chat_memory=InMemoryChatMessageHistory(messages=[]), k=10) variables_pool={}
[2025-08-21 21:59:12.114327] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=0f6754b17576e62454d8e748b14dbfd7_async_task_id node_data: id='start_cf1d5' type='start' name='开始' description='工作流运行的起始节点。' group_params=[NodeGroupParams(name='开场引导', params=[NodeParams(key='guide_word', label='开场白', value='', type='textarea', help='', tab='', placeholder='每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。', required=False, options=None), NodeParams(key='guide_question', label='引导问题', value=[''], type='input_list', help='为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', tab='', placeholder='请输入引导问题', required=False, options=None)], description=''), NodeGroupParams(name='全局变量', params=[NodeParams(key='current_time', label='当前时间', value='', type='var', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='chat_history', label='', value=10, type='chat_history_num', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='user_id', label='用户ID', value='', type='var', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='app_id', label='应用ID', value='', type='var', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='preset_question', label='预置问题列表', value=[{'key': '785b7d', 'value': ''}], type='input_list', help='适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', tab='', placeholder='输入批量预置问题', required=False, options=None)], description='')] tab={} tool_key='' v=1
[2025-08-21 21:59:12.146005] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=0f6754b17576e62454d8e748b14dbfd7_async_task_id node_params: {'guide_word': '', 'guide_question': [''], 'current_time': '2025-08-21 21:59:11', 'chat_history': 10, 'user_id': '', 'app_id': '', 'preset_question': [{'key': '785b7d', 'value': ''}]}
[2025-08-21 21:59:12.305231] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=0f6754b17576e62454d8e748b14dbfd7_async_task_id other_node_variable: {}
[2025-08-21 21:59:12.314134] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=0f6754b17576e62454d8e748b14dbfd7_async_task_id current_step: 0
[2025-08-21 21:59:12.321571] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=0f6754b17576e62454d8e748b14dbfd7_async_task_id max_steps: 50
[2025-08-21 21:59:12.327306] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=0f6754b17576e62454d8e748b14dbfd7_async_task_id callback_manager: <bisheng.worker.workflow.redis_callback.RedisCallback object at 0x0000021891C8A980>
[2025-08-21 21:59:12.334485] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=0f6754b17576e62454d8e748b14dbfd7_async_task_id tmp_collection_name: tmp_workflow_data
[2025-08-21 21:59:12.339564] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=0f6754b17576e62454d8e748b14dbfd7_async_task_id stop_flag: False
[2025-08-21 21:59:12.345013] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:34] - trace=0f6754b17576e62454d8e748b14dbfd7_async_task_id exec_unique_id: ac67cd2f663e432da45338e02f104a42
[2025-08-21 21:59:12.415332] [INFO process-35508-12804 bisheng.workflow.nodes.start.start:35] - trace=0f6754b17576e62454d8e748b14dbfd7_async_task_id === StartNode self 信息结束 ===
