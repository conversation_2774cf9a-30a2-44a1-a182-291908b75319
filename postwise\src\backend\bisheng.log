[2025-08-22 09:42:01.768643] [INFO process-31136-32368 datasets.config:58] - trace=1 PyTorch version 2.8.0 available.
[2025-08-22 11:06:38.083627] [ERROR process-31136-32368 bisheng.worker.workflow.tasks:98] - trace=98091743dd7cc02b9add7bdc38d588fa_async_task_id continue_workflow error
Traceback (most recent call last):

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
           │         │     └ {'__name__': '__main__', '__doc__': 'Entry-point for the :program:`celery` umbrella command.', '__package__': 'celery', '__lo...
           │         └ <code object <module> at 0x0000023BF98C7100, file "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pa...
           └ <function _run_code at 0x0000023BF98E9CF0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': 'Entry-point for the :program:`celery` umbrella command.', '__package__': 'celery', '__lo...
         └ <code object <module> at 0x0000023BF98C7100, file "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pa...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\__main__.py", line 19, in <module>
    main()
    └ <function main at 0x0000023BF98EA710>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\__main__.py", line 15, in main
    sys.exit(_main())
    │   │    └ <function main at 0x0000023BFB8F0550>
    │   └ <built-in function exit>
    └ <module 'sys' (built-in)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
           └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {'auto_envvar_prefix': 'CELERY'}
           │    │     └ ()
           │    └ <function Command.main at 0x0000023BFAEB3C70>
           └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000023BF967FB50>
         │    └ <function Group.invoke at 0x0000023BFAEBC670>
         └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1830, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000023BFB8CB190>
           │               │       │       └ <function Command.invoke at 0x0000023BFAEB3B50>
           │               │       └ <CeleryDaemonCommand worker>
           │               └ <click.core.Context object at 0x0000023BFB8CB190>
           └ <function Group.invoke.<locals>._process_result at 0x0000023BFB8F0670>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │   │      │    │           └ <click.core.Context object at 0x0000023BFB8CB190>
           │   │      │    └ <function worker at 0x0000023BFB8F13F0>
           │   │      └ <CeleryDaemonCommand worker>
           │   └ <function Context.invoke at 0x0000023BFAEB2EF0>
           └ <click.core.Context object at 0x0000023BFB8CB190>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │         └ ()
           └ <function worker at 0x0000023BFB8F13F0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\decorators.py", line 34, in new_func
    return f(get_current_context(), *args, **kwargs)
           │ │                       │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │ │                       └ ()
           │ └ <function get_current_context at 0x0000023BFAE2F1C0>
           └ <function worker at 0x0000023BFB8F1360>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
           │ │     │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │ │     └ ()
           │ └ <click.core.Context object at 0x0000023BFB8CB190>
           └ <function worker at 0x0000023BFB8F12D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\worker.py", line 356, in worker
    worker.start()
    │      └ <function WorkController.start at 0x0000023BFB822440>
    └ <Worker: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\worker.py", line 203, in start
    self.blueprint.start(self)
    │    │         │     └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │    │         └ <function Blueprint.start at 0x0000023BFB80ECB0>
    │    └ <celery.worker.worker.WorkController.Blueprint object at 0x0000023BD887DD80>
    └ <Worker: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 116, in start
    step.start(parent)
    │    │     └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │    └ <function StartStopStep.start at 0x0000023BFB80FD90>
    └ <step: Consumer>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 365, in start
    return self.obj.start()
           │    │   └ <function Consumer.start at 0x0000023BD8904820>
           │    └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
           └ <step: Consumer>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 341, in start
    blueprint.start(self)
    │         │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │         └ <function Blueprint.start at 0x0000023BFB80ECB0>
    └ <celery.worker.consumer.consumer.Consumer.Blueprint object at 0x0000023BD88C1F00>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 116, in start
    step.start(parent)
    │    │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │    └ <function Evloop.start at 0x0000023BD8905990>
    └ <step: event loop>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 772, in start
    c.loop(*c.loop_args())
    │ │     │ └ <function Consumer.loop_args at 0x0000023BD8904CA0>
    │ │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │ └ <function synloop at 0x0000023BD88D7F40>
    └ <Consumer: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\loops.py", line 143, in synloop
    _loop_cycle()
    └ <function synloop.<locals>._loop_cycle at 0x0000023BD8A040D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\loops.py", line 132, in _loop_cycle
    connection.drain_events(timeout=2.0)
    │          └ <function Connection.drain_events at 0x0000023BFAC95750>
    └ <Connection: redis://172.20.20.128:6379/2 at 0x23bd8981ff0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\connection.py", line 341, in drain_events
    return self.transport.drain_events(self.connection, **kwargs)
           │    │                      │    │             └ {'timeout': 2.0}
           │    │                      │    └ <property object at 0x0000023BFAC895D0>
           │    │                      └ <Connection: redis://172.20.20.128:6379/2 at 0x23bd8981ff0>
           │    └ <property object at 0x0000023BFAC896C0>
           └ <Connection: redis://172.20.20.128:6379/2 at 0x23bd8981ff0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 997, in drain_events
    get(self._deliver, timeout=timeout)
    │   │    │                 └ 2.0
    │   │    └ <function Transport._deliver at 0x0000023BD88C5240>
    │   └ <kombu.transport.redis.Transport object at 0x0000023BD8983040>
    └ <bound method MultiChannelPoller.get of <kombu.transport.redis.MultiChannelPoller object at 0x0000023BD8982AA0>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 598, in get
    ret = self.handle_event(fileno, event)
          │    │            │       └ 1
          │    │            └ 7800
          │    └ <function MultiChannelPoller.handle_event at 0x0000023BD88C7370>
          └ <kombu.transport.redis.MultiChannelPoller object at 0x0000023BD8982AA0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 580, in handle_event
    return self.on_readable(fileno), self
           │    │           │        └ <kombu.transport.redis.MultiChannelPoller object at 0x0000023BD8982AA0>
           │    │           └ 7800
           │    └ <function MultiChannelPoller.on_readable at 0x0000023BD88C72E0>
           └ <kombu.transport.redis.MultiChannelPoller object at 0x0000023BD8982AA0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 576, in on_readable
    chan.handlers[type]()
    │    │        └ 'BRPOP'
    │    └ {'BRPOP': <bound method Channel._brpop_read of <kombu.transport.redis.Channel object at 0x0000023BD89830D0>>, 'LISTEN': <boun...
    └ <kombu.transport.redis.Channel object at 0x0000023BD89830D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 986, in _brpop_read
    self.connection._deliver(loads(bytes_to_str(item)), dest)
    │    │          │        │     │            │       └ 'workflow_celery'
    │    │          │        │     │            └ b'{"body": "W1siOTgwOTE3NDNkZDdjYzAyYjlhZGQ3YmRjMzhkNTg4ZmFfYXN5bmNfdGFza19pZCIsICJhYzhiMjNhZWQyNTk0NzkwOGFjMDQyZGM1YjVmMTU5N...
    │    │          │        │     └ <function bytes_to_str at 0x0000023BFA4AEF80>
    │    │          │        └ <function loads at 0x0000023BFACA3010>
    │    │          └ <function Transport._deliver at 0x0000023BD88C5240>
    │    └ <kombu.transport.redis.Transport object at 0x0000023BD8983040>
    └ <kombu.transport.redis.Channel object at 0x0000023BD89830D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 1017, in _deliver
    callback(message)
    │        └ {'body': 'W1siOTgwOTE3NDNkZDdjYzAyYjlhZGQ3YmRjMzhkNTg4ZmFfYXN5bmNfdGFza19pZCIsICJhYzhiMjNhZWQyNTk0NzkwOGFjMDQyZGM1YjVmMTU5NCI...
    └ <function Channel.basic_consume.<locals>._callback at 0x0000023BD899C280>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 639, in _callback
    return callback(message)
           │        └ <Message object at 0x23bd8a04700 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': '9b94...
           └ <bound method Consumer._receive_callback of <Consumer: [<Queue workflow_celery -> <Exchange workflow_celery(direct) bound to ...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\messaging.py", line 668, in _receive_callback
    return on_m(message) if on_m else self.receive(decoded, message)
           │    │           │         │    │       │        └ <Message object at 0x23bd8a04700 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': '9b94...
           │    │           │         │    │       └ None
           │    │           │         │    └ <function Consumer.receive at 0x0000023BFB012170>
           │    │           │         └ <Consumer: [<Queue workflow_celery -> <Exchange workflow_celery(direct) bound to chan:1> -> workflow_celery bound to chan:1>,...
           │    │           └ <function Consumer.create_task_handler.<locals>.on_task_received at 0x0000023BD888E9E0>
           │    └ <Message object at 0x23bd8a04700 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': '9b94...
           └ <function Consumer.create_task_handler.<locals>.on_task_received at 0x0000023BD888E9E0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 688, in on_task_received
    strategy(
    └ <function default.<locals>.task_message_handler at 0x0000023BD899DCF0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\strategy.py", line 207, in task_message_handler
    handle(req)
    │      └ <Request: bisheng.worker.workflow.tasks.continue_workflow[46733cee-e70a-4fbe-8385-558e8536cad5] ('98091743dd7cc02b9add7bdc38d...
    └ <bound method WorkController._process_task of <Worker: celery@WIN-S45T7NVPCSH (running)>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\worker.py", line 226, in _process_task
    req.execute_using_pool(self.pool)
    │   │                  │    └ <celery.concurrency.solo.TaskPool object at 0x0000023BD88C21A0>
    │   │                  └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │   └ <function create_request_cls.<locals>.Request.execute_using_pool at 0x0000023BD899DD80>
    └ <Request: bisheng.worker.workflow.tasks.continue_workflow[46733cee-e70a-4fbe-8385-558e8536cad5] ('98091743dd7cc02b9add7bdc38d...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\request.py", line 754, in execute_using_pool
    result = apply_async(
             └ <bound method BasePool.apply_async of <celery.concurrency.solo.TaskPool object at 0x0000023BD88C21A0>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\concurrency\base.py", line 153, in apply_async
    return self.on_apply(target, args, kwargs,
           │    │        │       │     └ {}
           │    │        │       └ ('bisheng.worker.workflow.tasks.continue_workflow', '46733cee-e70a-4fbe-8385-558e8536cad5', {'lang': 'py', 'task': 'bisheng.w...
           │    │        └ <function fast_trace_task at 0x0000023BFB88D480>
           │    └ <function apply_target at 0x0000023BFA4B3130>
           └ <celery.concurrency.solo.TaskPool object at 0x0000023BD88C21A0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\concurrency\base.py", line 30, in apply_target
    ret = target(*args, **kwargs)
          │       │       └ {}
          │       └ ('bisheng.worker.workflow.tasks.continue_workflow', '46733cee-e70a-4fbe-8385-558e8536cad5', {'lang': 'py', 'task': 'bisheng.w...
          └ <function fast_trace_task at 0x0000023BFB88D480>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 651, in fast_trace_task
    R, I, T, Rstr = tasks[task].__trace__(
                    │     └ 'bisheng.worker.workflow.tasks.continue_workflow'
                    └ {'celery.chord': <@task: celery.chord of bisheng at 0x23b97298cd0>, 'celery.accumulate': <@task: celery.accumulate of bisheng...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 │    │       └ {}
                 │    └ ['98091743dd7cc02b9add7bdc38d588fa_async_task_id', 'ac8b23aed25947908ac042dc5b5f1594', '98091743dd7cc02b9add7bdc38d588fa', '1']
                 └ <@task: bisheng.worker.workflow.tasks.continue_workflow of bisheng at 0x23b97298cd0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           │    │    │       └ {}
           │    │    └ ('98091743dd7cc02b9add7bdc38d588fa_async_task_id', 'ac8b23aed25947908ac042dc5b5f1594', '98091743dd7cc02b9add7bdc38d588fa', '1')
           │    └ <staticmethod(<function continue_workflow at 0x0000023BD1640AF0>)>
           └ <@task: bisheng.worker.workflow.tasks.continue_workflow of bisheng at 0x23b97298cd0>

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\worker\workflow\tasks.py", line 107, in continue_workflow
    _continue_workflow(unique_id, workflow_id, chat_id, user_id)
    │                  │          │            │        └ '1'
    │                  │          │            └ '98091743dd7cc02b9add7bdc38d588fa'
    │                  │          └ 'ac8b23aed25947908ac042dc5b5f1594'
    │                  └ '98091743dd7cc02b9add7bdc38d588fa_async_task_id'
    └ <function _continue_workflow at 0x0000023BD1640A60>

> File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\worker\workflow\tasks.py", line 84, in _continue_workflow
    raise Exception('workflow object not found maybe data is expired')

Exception: workflow object not found maybe data is expired
[2025-08-22 11:06:38.516739] [WARNING process-31136-32368 bisheng.worker.workflow.tasks:20] - trace=98091743dd7cc02b9add7bdc38d588fa_async_task_id workflow object not found for unique_id: 98091743dd7cc02b9add7bdc38d588fa_async_task_id
[2025-08-22 11:06:49.065124] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:32] - trace=8408265840e1a5f5281880c20928a07a_async_task_id === StartNode self 内部信息 ===
[2025-08-22 11:06:49.070366] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=8408265840e1a5f5281880c20928a07a_async_task_id id: start_553b9
[2025-08-22 11:06:49.074102] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=8408265840e1a5f5281880c20928a07a_async_task_id type: start
[2025-08-22 11:06:49.077204] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=8408265840e1a5f5281880c20928a07a_async_task_id name: 开始
[2025-08-22 11:06:49.081102] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=8408265840e1a5f5281880c20928a07a_async_task_id description: 工作流运行的起始节点。
[2025-08-22 11:06:49.085258] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=8408265840e1a5f5281880c20928a07a_async_task_id target_edges: [EdgeBase(id='xy-edge__start_553b9right_handle-input_2775bleft_handle', source='start_553b9', sourceHandle='right_handle', sourceType='', target='input_2775b', targetHandle='left_handle', targetType='')]
[2025-08-22 11:06:49.090924] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=8408265840e1a5f5281880c20928a07a_async_task_id user_id: 1
[2025-08-22 11:06:49.093865] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=8408265840e1a5f5281880c20928a07a_async_task_id workflow_id: ac8b23aed25947908ac042dc5b5f1594
[2025-08-22 11:06:49.096851] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=8408265840e1a5f5281880c20928a07a_async_task_id graph_state: history_memory=ConversationBufferWindowMemory(chat_memory=InMemoryChatMessageHistory(messages=[]), k=10) variables_pool={}
[2025-08-22 11:06:49.100852] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=8408265840e1a5f5281880c20928a07a_async_task_id node_data: id='start_553b9' type='start' name='开始' description='工作流运行的起始节点。' group_params=[NodeGroupParams(name='开场引导', params=[NodeParams(key='guide_word', label='开场白', value='您好，请问想聊些什么呢？', type='textarea', help='', tab='', placeholder='每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。', required=False, options=None), NodeParams(key='guide_question', label='引导问题', value=['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], type='input_list', help='为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', tab='', placeholder='请输入引导问题', required=False, options=None)], description=''), NodeGroupParams(name='全局变量', params=[NodeParams(key='current_time', label='当前时间', value='', type='var', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='chat_history', label='', value=10, type='chat_history_num', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='preset_question', label='预置问题列表', value=[{'key': 'f4cbae', 'value': ''}], type='input_list', help='适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', tab='', placeholder='输入批量预置问题', required=False, options=None)], description='')] tab={} tool_key='' v=1
[2025-08-22 11:06:49.111644] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=8408265840e1a5f5281880c20928a07a_async_task_id node_params: {'guide_word': '您好，请问想聊些什么呢？', 'guide_question': ['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], 'current_time': '2025-08-22 11:06:47', 'chat_history': 10, 'preset_question': [{'key': 'f4cbae', 'value': ''}]}
[2025-08-22 11:06:49.116631] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=8408265840e1a5f5281880c20928a07a_async_task_id other_node_variable: {}
[2025-08-22 11:06:49.119913] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=8408265840e1a5f5281880c20928a07a_async_task_id current_step: 0
[2025-08-22 11:06:49.122818] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=8408265840e1a5f5281880c20928a07a_async_task_id max_steps: 50
[2025-08-22 11:06:49.124834] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=8408265840e1a5f5281880c20928a07a_async_task_id callback_manager: <bisheng.worker.workflow.redis_callback.RedisCallback object at 0x0000023BD8A1C4C0>
[2025-08-22 11:06:49.129844] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=8408265840e1a5f5281880c20928a07a_async_task_id tmp_collection_name: tmp_workflow_data
[2025-08-22 11:06:49.134632] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=8408265840e1a5f5281880c20928a07a_async_task_id stop_flag: False
[2025-08-22 11:06:49.141181] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=8408265840e1a5f5281880c20928a07a_async_task_id exec_unique_id: 5a1bc08bc5d846f7a0514019552451d5
[2025-08-22 11:06:49.148350] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:35] - trace=8408265840e1a5f5281880c20928a07a_async_task_id === StartNode self 信息结束 ===
[2025-08-22 11:07:19.228202] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:32] - trace=6c9516c0c792197845e62988b7ca189d_async_task_id === StartNode self 内部信息 ===
[2025-08-22 11:07:19.231439] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=6c9516c0c792197845e62988b7ca189d_async_task_id id: start_553b9
[2025-08-22 11:07:19.233420] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=6c9516c0c792197845e62988b7ca189d_async_task_id type: start
[2025-08-22 11:07:19.236146] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=6c9516c0c792197845e62988b7ca189d_async_task_id name: 开始
[2025-08-22 11:07:19.237695] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=6c9516c0c792197845e62988b7ca189d_async_task_id description: 工作流运行的起始节点。
[2025-08-22 11:07:19.240689] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=6c9516c0c792197845e62988b7ca189d_async_task_id target_edges: [EdgeBase(id='xy-edge__start_553b9right_handle-input_2775bleft_handle', source='start_553b9', sourceHandle='right_handle', sourceType='', target='input_2775b', targetHandle='left_handle', targetType='')]
[2025-08-22 11:07:19.243690] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=6c9516c0c792197845e62988b7ca189d_async_task_id user_id: 1
[2025-08-22 11:07:19.246211] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=6c9516c0c792197845e62988b7ca189d_async_task_id workflow_id: ac8b23aed25947908ac042dc5b5f1594
[2025-08-22 11:07:19.248232] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=6c9516c0c792197845e62988b7ca189d_async_task_id graph_state: history_memory=ConversationBufferWindowMemory(chat_memory=InMemoryChatMessageHistory(messages=[]), k=10) variables_pool={}
[2025-08-22 11:07:19.251202] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=6c9516c0c792197845e62988b7ca189d_async_task_id node_data: id='start_553b9' type='start' name='开始' description='工作流运行的起始节点。' group_params=[NodeGroupParams(name='开场引导', params=[NodeParams(key='guide_word', label='开场白', value='您好，请问想聊些什么呢？', type='textarea', help='', tab='', placeholder='每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。', required=False, options=None), NodeParams(key='guide_question', label='引导问题', value=['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], type='input_list', help='为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', tab='', placeholder='请输入引导问题', required=False, options=None)], description=''), NodeGroupParams(name='全局变量', params=[NodeParams(key='current_time', label='当前时间', value='', type='var', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='chat_history', label='', value=10, type='chat_history_num', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='preset_question', label='预置问题列表', value=[{'key': 'f4cbae', 'value': ''}], type='input_list', help='适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', tab='', placeholder='输入批量预置问题', required=False, options=None)], description='')] tab={} tool_key='' v=1
[2025-08-22 11:07:19.257255] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=6c9516c0c792197845e62988b7ca189d_async_task_id node_params: {'guide_word': '您好，请问想聊些什么呢？', 'guide_question': ['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], 'current_time': '2025-08-22 11:07:19', 'chat_history': 10, 'preset_question': [{'key': 'f4cbae', 'value': ''}]}
[2025-08-22 11:07:19.260817] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=6c9516c0c792197845e62988b7ca189d_async_task_id other_node_variable: {}
[2025-08-22 11:07:19.262818] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=6c9516c0c792197845e62988b7ca189d_async_task_id current_step: 0
[2025-08-22 11:07:19.264954] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=6c9516c0c792197845e62988b7ca189d_async_task_id max_steps: 50
[2025-08-22 11:07:19.267937] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=6c9516c0c792197845e62988b7ca189d_async_task_id callback_manager: <bisheng.worker.workflow.redis_callback.RedisCallback object at 0x0000023BD1309630>
[2025-08-22 11:07:19.271058] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=6c9516c0c792197845e62988b7ca189d_async_task_id tmp_collection_name: tmp_workflow_data
[2025-08-22 11:07:19.274058] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=6c9516c0c792197845e62988b7ca189d_async_task_id stop_flag: False
[2025-08-22 11:07:19.277058] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:34] - trace=6c9516c0c792197845e62988b7ca189d_async_task_id exec_unique_id: b766039fb2e3431ab96a3165c67b782e
[2025-08-22 11:07:19.279057] [INFO process-31136-32368 bisheng.workflow.nodes.start.start:35] - trace=6c9516c0c792197845e62988b7ca189d_async_task_id === StartNode self 信息结束 ===
[2025-08-22 11:39:12.607628] [INFO process-31136-32368 bisheng.worker.knowledge.file_worker:36] - trace=1 file_copy_celery start source_id=16 target_id=17
[2025-08-22 11:39:12.636681] [INFO process-31136-32368 bisheng.worker.knowledge.file_worker:80] - trace=1 file_copy_celery end
[2025-08-22 15:00:47.066844] [INFO process-28468-8856 datasets.config:58] - trace=1 PyTorch version 2.8.0 available.
[2025-08-22 15:03:17.092011] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:32] - trace=92bb4d54c7344b0dbc2fed9e2f858025 === StartNode self 内部信息 ===
[2025-08-22 15:03:17.095669] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=92bb4d54c7344b0dbc2fed9e2f858025 id: start_553b9
[2025-08-22 15:03:17.100130] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=92bb4d54c7344b0dbc2fed9e2f858025 type: start
[2025-08-22 15:03:17.104396] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=92bb4d54c7344b0dbc2fed9e2f858025 name: 开始
[2025-08-22 15:03:17.109824] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=92bb4d54c7344b0dbc2fed9e2f858025 description: 工作流运行的起始节点。
[2025-08-22 15:03:17.114840] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=92bb4d54c7344b0dbc2fed9e2f858025 target_edges: [EdgeBase(id='xy-edge__start_553b9right_handle-input_2775bleft_handle', source='start_553b9', sourceHandle='right_handle', sourceType='', target='input_2775b', targetHandle='left_handle', targetType='')]
[2025-08-22 15:03:17.119869] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=92bb4d54c7344b0dbc2fed9e2f858025 user_id: 1
[2025-08-22 15:03:17.123376] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=92bb4d54c7344b0dbc2fed9e2f858025 workflow_id: ac8b23aed25947908ac042dc5b5f1594
[2025-08-22 15:03:17.127901] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=92bb4d54c7344b0dbc2fed9e2f858025 graph_state: history_memory=ConversationBufferWindowMemory(chat_memory=InMemoryChatMessageHistory(messages=[]), k=10) variables_pool={}
[2025-08-22 15:03:17.131803] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=92bb4d54c7344b0dbc2fed9e2f858025 node_data: id='start_553b9' type='start' name='开始' description='工作流运行的起始节点。' group_params=[NodeGroupParams(name='开场引导', params=[NodeParams(key='guide_word', label='开场白', value='您好，请问想聊些什么呢？', type='textarea', help='', tab='', placeholder='每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。', required=False, options=None), NodeParams(key='guide_question', label='引导问题', value=['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], type='input_list', help='为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', tab='', placeholder='请输入引导问题', required=False, options=None)], description=''), NodeGroupParams(name='全局变量', params=[NodeParams(key='current_time', label='当前时间', value='', type='var', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='chat_history', label='', value=10, type='chat_history_num', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='preset_question', label='预置问题列表', value=[{'key': 'f4cbae', 'value': ''}], type='input_list', help='适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', tab='', placeholder='输入批量预置问题', required=False, options=None)], description='')] tab={} tool_key='' v=1
[2025-08-22 15:03:17.144957] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=92bb4d54c7344b0dbc2fed9e2f858025 node_params: {'guide_word': '您好，请问想聊些什么呢？', 'guide_question': ['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], 'current_time': '2025-08-22 15:03:16', 'chat_history': 10, 'preset_question': [{'key': 'f4cbae', 'value': ''}]}
[2025-08-22 15:03:17.153872] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=92bb4d54c7344b0dbc2fed9e2f858025 other_node_variable: {}
[2025-08-22 15:03:17.158600] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=92bb4d54c7344b0dbc2fed9e2f858025 current_step: 0
[2025-08-22 15:03:17.162252] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=92bb4d54c7344b0dbc2fed9e2f858025 max_steps: 50
[2025-08-22 15:03:17.165257] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=92bb4d54c7344b0dbc2fed9e2f858025 callback_manager: <bisheng.worker.workflow.redis_callback.RedisCallback object at 0x00000160D825BD30>
[2025-08-22 15:03:17.171091] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=92bb4d54c7344b0dbc2fed9e2f858025 tmp_collection_name: tmp_workflow_data
[2025-08-22 15:03:17.175107] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=92bb4d54c7344b0dbc2fed9e2f858025 stop_flag: False
[2025-08-22 15:03:17.179625] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=92bb4d54c7344b0dbc2fed9e2f858025 exec_unique_id: d0ef685e3521448fbd1884857f953e2a
[2025-08-22 15:03:17.184644] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:35] - trace=92bb4d54c7344b0dbc2fed9e2f858025 === StartNode self 信息结束 ===
[2025-08-22 15:03:35.453628] [INFO process-28468-8856 bisheng.workflow.callback.llm_callback:35] - trace=92bb4d54c7344b0dbc2fed9e2f858025 on_llm_new_token True outkey=output
[2025-08-22 15:03:45.305185] [INFO process-28468-8856 bisheng.workflow.callback.llm_callback:35] - trace=92bb4d54c7344b0dbc2fed9e2f858025 on_llm_new_token True outkey=output
[2025-08-22 15:04:07.321339] [INFO process-28468-8856 bisheng.worker.workflow.tasks:122] - trace=92bb4d54c7344b0dbc2fed9e2f858025 workflow stop by user 1
[2025-08-22 15:04:17.357054] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:32] - trace=3643d6089784d4254c2bc4c312643031_async_task_id === StartNode self 内部信息 ===
[2025-08-22 15:04:17.361052] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=3643d6089784d4254c2bc4c312643031_async_task_id id: start_553b9
[2025-08-22 15:04:17.364053] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=3643d6089784d4254c2bc4c312643031_async_task_id type: start
[2025-08-22 15:04:17.368465] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=3643d6089784d4254c2bc4c312643031_async_task_id name: 开始
[2025-08-22 15:04:17.371464] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=3643d6089784d4254c2bc4c312643031_async_task_id description: 工作流运行的起始节点。
[2025-08-22 15:04:17.374465] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=3643d6089784d4254c2bc4c312643031_async_task_id target_edges: [EdgeBase(id='xy-edge__start_553b9right_handle-input_2775bleft_handle', source='start_553b9', sourceHandle='right_handle', sourceType='', target='input_2775b', targetHandle='left_handle', targetType='')]
[2025-08-22 15:04:17.379465] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=3643d6089784d4254c2bc4c312643031_async_task_id user_id: 1
[2025-08-22 15:04:17.382625] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=3643d6089784d4254c2bc4c312643031_async_task_id workflow_id: ac8b23aed25947908ac042dc5b5f1594
[2025-08-22 15:04:17.386113] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=3643d6089784d4254c2bc4c312643031_async_task_id graph_state: history_memory=ConversationBufferWindowMemory(chat_memory=InMemoryChatMessageHistory(messages=[]), k=10) variables_pool={}
[2025-08-22 15:04:17.390631] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=3643d6089784d4254c2bc4c312643031_async_task_id node_data: id='start_553b9' type='start' name='开始' description='工作流运行的起始节点。' group_params=[NodeGroupParams(name='开场引导', params=[NodeParams(key='guide_word', label='开场白', value='您好，请问想聊些什么呢？', type='textarea', help='', tab='', placeholder='每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。', required=False, options=None), NodeParams(key='guide_question', label='引导问题', value=['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], type='input_list', help='为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', tab='', placeholder='请输入引导问题', required=False, options=None)], description=''), NodeGroupParams(name='全局变量', params=[NodeParams(key='current_time', label='当前时间', value='', type='var', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='chat_history', label='', value=10, type='chat_history_num', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='preset_question', label='预置问题列表', value=[{'key': 'f4cbae', 'value': ''}], type='input_list', help='适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', tab='', placeholder='输入批量预置问题', required=False, options=None)], description='')] tab={} tool_key='' v=1
[2025-08-22 15:04:17.399634] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=3643d6089784d4254c2bc4c312643031_async_task_id node_params: {'guide_word': '您好，请问想聊些什么呢？', 'guide_question': ['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], 'current_time': '2025-08-22 15:04:17', 'chat_history': 10, 'preset_question': [{'key': 'f4cbae', 'value': ''}]}
[2025-08-22 15:04:17.402626] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=3643d6089784d4254c2bc4c312643031_async_task_id other_node_variable: {}
[2025-08-22 15:04:17.405626] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=3643d6089784d4254c2bc4c312643031_async_task_id current_step: 0
[2025-08-22 15:04:17.406630] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=3643d6089784d4254c2bc4c312643031_async_task_id max_steps: 50
[2025-08-22 15:04:17.409626] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=3643d6089784d4254c2bc4c312643031_async_task_id callback_manager: <bisheng.worker.workflow.redis_callback.RedisCallback object at 0x00000160D81A7C70>
[2025-08-22 15:04:17.412626] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=3643d6089784d4254c2bc4c312643031_async_task_id tmp_collection_name: tmp_workflow_data
[2025-08-22 15:04:17.414627] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=3643d6089784d4254c2bc4c312643031_async_task_id stop_flag: False
[2025-08-22 15:04:17.417114] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=3643d6089784d4254c2bc4c312643031_async_task_id exec_unique_id: 566b4f1a308f4599b9a0d7c55355dc3b
[2025-08-22 15:04:17.420125] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:35] - trace=3643d6089784d4254c2bc4c312643031_async_task_id === StartNode self 信息结束 ===
[2025-08-22 15:04:24.646352] [INFO process-28468-8856 bisheng.workflow.callback.llm_callback:35] - trace=3643d6089784d4254c2bc4c312643031_async_task_id on_llm_new_token True outkey=output
[2025-08-22 15:05:28.101758] [INFO process-28468-8856 bisheng.workflow.callback.llm_callback:35] - trace=3643d6089784d4254c2bc4c312643031_async_task_id on_llm_new_token True outkey=output
[2025-08-22 15:06:52.244644] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:32] - trace=48ee4b24542124511ef3bcdbed62e380_async_task_id === StartNode self 内部信息 ===
[2025-08-22 15:06:52.248169] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=48ee4b24542124511ef3bcdbed62e380_async_task_id id: start_553b9
[2025-08-22 15:06:52.250172] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=48ee4b24542124511ef3bcdbed62e380_async_task_id type: start
[2025-08-22 15:06:52.252473] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=48ee4b24542124511ef3bcdbed62e380_async_task_id name: 开始
[2025-08-22 15:06:52.256998] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=48ee4b24542124511ef3bcdbed62e380_async_task_id description: 工作流运行的起始节点。
[2025-08-22 15:06:52.259410] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=48ee4b24542124511ef3bcdbed62e380_async_task_id target_edges: [EdgeBase(id='xy-edge__start_553b9right_handle-input_2775bleft_handle', source='start_553b9', sourceHandle='right_handle', sourceType='', target='input_2775b', targetHandle='left_handle', targetType='')]
[2025-08-22 15:06:52.261958] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=48ee4b24542124511ef3bcdbed62e380_async_task_id user_id: 1
[2025-08-22 15:06:52.263979] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=48ee4b24542124511ef3bcdbed62e380_async_task_id workflow_id: ac8b23aed25947908ac042dc5b5f1594
[2025-08-22 15:06:52.267044] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=48ee4b24542124511ef3bcdbed62e380_async_task_id graph_state: history_memory=ConversationBufferWindowMemory(chat_memory=InMemoryChatMessageHistory(messages=[]), k=10) variables_pool={}
[2025-08-22 15:06:52.269051] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=48ee4b24542124511ef3bcdbed62e380_async_task_id node_data: id='start_553b9' type='start' name='开始' description='工作流运行的起始节点。' group_params=[NodeGroupParams(name='开场引导', params=[NodeParams(key='guide_word', label='开场白', value='您好，请问想聊些什么呢？', type='textarea', help='', tab='', placeholder='每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。', required=False, options=None), NodeParams(key='guide_question', label='引导问题', value=['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], type='input_list', help='为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', tab='', placeholder='请输入引导问题', required=False, options=None)], description=''), NodeGroupParams(name='全局变量', params=[NodeParams(key='current_time', label='当前时间', value='', type='var', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='chat_history', label='', value=10, type='chat_history_num', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='preset_question', label='预置问题列表', value=[{'key': 'f4cbae', 'value': ''}], type='input_list', help='适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', tab='', placeholder='输入批量预置问题', required=False, options=None)], description='')] tab={} tool_key='' v=1
[2025-08-22 15:06:52.282251] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=48ee4b24542124511ef3bcdbed62e380_async_task_id node_params: {'guide_word': '您好，请问想聊些什么呢？', 'guide_question': ['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], 'current_time': '2025-08-22 15:06:52', 'chat_history': 10, 'preset_question': [{'key': 'f4cbae', 'value': ''}]}
[2025-08-22 15:06:52.284788] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=48ee4b24542124511ef3bcdbed62e380_async_task_id other_node_variable: {}
[2025-08-22 15:06:52.288317] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=48ee4b24542124511ef3bcdbed62e380_async_task_id current_step: 0
[2025-08-22 15:06:52.291316] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=48ee4b24542124511ef3bcdbed62e380_async_task_id max_steps: 50
[2025-08-22 15:06:52.292417] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=48ee4b24542124511ef3bcdbed62e380_async_task_id callback_manager: <bisheng.worker.workflow.redis_callback.RedisCallback object at 0x00000160D95B2710>
[2025-08-22 15:06:52.294970] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=48ee4b24542124511ef3bcdbed62e380_async_task_id tmp_collection_name: tmp_workflow_data
[2025-08-22 15:06:52.299018] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=48ee4b24542124511ef3bcdbed62e380_async_task_id stop_flag: False
[2025-08-22 15:06:52.301010] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:34] - trace=48ee4b24542124511ef3bcdbed62e380_async_task_id exec_unique_id: b66b418549e348ec88e1c8a7f2ca3f72
[2025-08-22 15:06:52.303881] [INFO process-28468-8856 bisheng.workflow.nodes.start.start:35] - trace=48ee4b24542124511ef3bcdbed62e380_async_task_id === StartNode self 信息结束 ===
[2025-08-22 15:07:56.070457] [ERROR process-28468-8856 bisheng.worker.workflow.tasks:98] - trace=bfd40c7fc538b3d86925d91c60c18fb4_async_task_id continue_workflow error
Traceback (most recent call last):

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
           │         │     └ {'__name__': '__main__', '__doc__': 'Entry-point for the :program:`celery` umbrella command.', '__package__': 'celery', '__lo...
           │         └ <code object <module> at 0x00000160F8A2B100, file "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pa...
           └ <function _run_code at 0x00000160F8A49CF0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': 'Entry-point for the :program:`celery` umbrella command.', '__package__': 'celery', '__lo...
         └ <code object <module> at 0x00000160F8A2B100, file "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pa...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\__main__.py", line 19, in <module>
    main()
    └ <function main at 0x00000160F8A4A710>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\__main__.py", line 15, in main
    sys.exit(_main())
    │   │    └ <function main at 0x00000160FAE1C550>
    │   └ <built-in function exit>
    └ <module 'sys' (built-in)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
           └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {'auto_envvar_prefix': 'CELERY'}
           │    │     └ ()
           │    └ <function Command.main at 0x00000160FA3E3C70>
           └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x00000160F881FB50>
         │    └ <function Group.invoke at 0x00000160FA3EC670>
         └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1830, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x00000160FADFB190>
           │               │       │       └ <function Command.invoke at 0x00000160FA3E3B50>
           │               │       └ <CeleryDaemonCommand worker>
           │               └ <click.core.Context object at 0x00000160FADFB190>
           └ <function Group.invoke.<locals>._process_result at 0x00000160FAE1C670>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │   │      │    │           └ <click.core.Context object at 0x00000160FADFB190>
           │   │      │    └ <function worker at 0x00000160FAE1D3F0>
           │   │      └ <CeleryDaemonCommand worker>
           │   └ <function Context.invoke at 0x00000160FA3E2EF0>
           └ <click.core.Context object at 0x00000160FADFB190>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │         └ ()
           └ <function worker at 0x00000160FAE1D3F0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\decorators.py", line 34, in new_func
    return f(get_current_context(), *args, **kwargs)
           │ │                       │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │ │                       └ ()
           │ └ <function get_current_context at 0x00000160FA35F1C0>
           └ <function worker at 0x00000160FAE1D360>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
           │ │     │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │ │     └ ()
           │ └ <click.core.Context object at 0x00000160FADFB190>
           └ <function worker at 0x00000160FAE1D2D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\worker.py", line 356, in worker
    worker.start()
    │      └ <function WorkController.start at 0x00000160FAD52440>
    └ <Worker: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\worker.py", line 203, in start
    self.blueprint.start(self)
    │    │         │     └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │    │         └ <function Blueprint.start at 0x00000160FAD3ECB0>
    │    └ <celery.worker.worker.WorkController.Blueprint object at 0x00000160D80E5EA0>
    └ <Worker: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 116, in start
    step.start(parent)
    │    │     └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │    └ <function StartStopStep.start at 0x00000160FAD3FD90>
    └ <step: Consumer>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 365, in start
    return self.obj.start()
           │    │   └ <function Consumer.start at 0x00000160D8170820>
           │    └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
           └ <step: Consumer>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 341, in start
    blueprint.start(self)
    │         │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │         └ <function Blueprint.start at 0x00000160FAD3ECB0>
    └ <celery.worker.consumer.consumer.Consumer.Blueprint object at 0x00000160D812E020>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 116, in start
    step.start(parent)
    │    │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │    └ <function Evloop.start at 0x00000160D8171990>
    └ <step: event loop>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 772, in start
    c.loop(*c.loop_args())
    │ │     │ └ <function Consumer.loop_args at 0x00000160D8170CA0>
    │ │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │ └ <function synloop at 0x00000160D8143F40>
    └ <Consumer: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\loops.py", line 143, in synloop
    _loop_cycle()
    └ <function synloop.<locals>._loop_cycle at 0x00000160D8274310>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\loops.py", line 132, in _loop_cycle
    connection.drain_events(timeout=2.0)
    │          └ <function Connection.drain_events at 0x00000160FA1C5750>
    └ <Connection: redis://172.20.20.128:6379/2 at 0x160d81e9810>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\connection.py", line 341, in drain_events
    return self.transport.drain_events(self.connection, **kwargs)
           │    │                      │    │             └ {'timeout': 2.0}
           │    │                      │    └ <property object at 0x00000160FA1B98A0>
           │    │                      └ <Connection: redis://172.20.20.128:6379/2 at 0x160d81e9810>
           │    └ <property object at 0x00000160FA1B9990>
           └ <Connection: redis://172.20.20.128:6379/2 at 0x160d81e9810>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 997, in drain_events
    get(self._deliver, timeout=timeout)
    │   │    │                 └ 2.0
    │   │    └ <function Transport._deliver at 0x00000160D8135240>
    │   └ <kombu.transport.redis.Transport object at 0x00000160D81EB160>
    └ <bound method MultiChannelPoller.get of <kombu.transport.redis.MultiChannelPoller object at 0x00000160D81EABC0>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 598, in get
    ret = self.handle_event(fileno, event)
          │    │            │       └ 1
          │    │            └ 7392
          │    └ <function MultiChannelPoller.handle_event at 0x00000160D8137370>
          └ <kombu.transport.redis.MultiChannelPoller object at 0x00000160D81EABC0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 580, in handle_event
    return self.on_readable(fileno), self
           │    │           │        └ <kombu.transport.redis.MultiChannelPoller object at 0x00000160D81EABC0>
           │    │           └ 7392
           │    └ <function MultiChannelPoller.on_readable at 0x00000160D81372E0>
           └ <kombu.transport.redis.MultiChannelPoller object at 0x00000160D81EABC0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 576, in on_readable
    chan.handlers[type]()
    │    │        └ 'BRPOP'
    │    └ {'BRPOP': <bound method Channel._brpop_read of <kombu.transport.redis.Channel object at 0x00000160D81EB1F0>>, 'LISTEN': <boun...
    └ <kombu.transport.redis.Channel object at 0x00000160D81EB1F0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 986, in _brpop_read
    self.connection._deliver(loads(bytes_to_str(item)), dest)
    │    │          │        │     │            │       └ 'workflow_celery'
    │    │          │        │     │            └ b'{"body": "W1siYmZkNDBjN2ZjNTM4YjNkODY5MjVkOTFjNjBjMThmYjRfYXN5bmNfdGFza19pZCIsICJhYzhiMjNhZWQyNTk0NzkwOGFjMDQyZGM1YjVmMTU5N...
    │    │          │        │     └ <function bytes_to_str at 0x00000160F964EF80>
    │    │          │        └ <function loads at 0x00000160FA1D3010>
    │    │          └ <function Transport._deliver at 0x00000160D8135240>
    │    └ <kombu.transport.redis.Transport object at 0x00000160D81EB160>
    └ <kombu.transport.redis.Channel object at 0x00000160D81EB1F0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 1017, in _deliver
    callback(message)
    │        └ {'body': 'W1siYmZkNDBjN2ZjNTM4YjNkODY5MjVkOTFjNjBjMThmYjRfYXN5bmNfdGFza19pZCIsICJhYzhiMjNhZWQyNTk0NzkwOGFjMDQyZGM1YjVmMTU5NCI...
    └ <function Channel.basic_consume.<locals>._callback at 0x00000160D8274160>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 639, in _callback
    return callback(message)
           │        └ <Message object at 0x16093edacb0 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': 'cc83...
           └ <bound method Consumer._receive_callback of <Consumer: [<Queue workflow_celery -> <Exchange workflow_celery(direct) bound to ...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\messaging.py", line 668, in _receive_callback
    return on_m(message) if on_m else self.receive(decoded, message)
           │    │           │         │    │       │        └ <Message object at 0x16093edacb0 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': 'cc83...
           │    │           │         │    │       └ None
           │    │           │         │    └ <function Consumer.receive at 0x00000160FA542170>
           │    │           │         └ <Consumer: [<Queue workflow_celery -> <Exchange workflow_celery(direct) bound to chan:1> -> workflow_celery bound to chan:1>,...
           │    │           └ <function Consumer.create_task_handler.<locals>.on_task_received at 0x00000160D82740D0>
           │    └ <Message object at 0x16093edacb0 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': 'cc83...
           └ <function Consumer.create_task_handler.<locals>.on_task_received at 0x00000160D82740D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 688, in on_task_received
    strategy(
    └ <function default.<locals>.task_message_handler at 0x00000160D8206560>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\strategy.py", line 207, in task_message_handler
    handle(req)
    │      └ <Request: bisheng.worker.workflow.tasks.continue_workflow[7ea84775-eb70-4e67-a27d-2125a6fd8fcd] ('bfd40c7fc538b3d86925d91c60c...
    └ <bound method WorkController._process_task of <Worker: celery@WIN-S45T7NVPCSH (running)>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\worker.py", line 226, in _process_task
    req.execute_using_pool(self.pool)
    │   │                  │    └ <celery.concurrency.solo.TaskPool object at 0x00000160D812E2C0>
    │   │                  └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │   └ <function create_request_cls.<locals>.Request.execute_using_pool at 0x00000160D82065F0>
    └ <Request: bisheng.worker.workflow.tasks.continue_workflow[7ea84775-eb70-4e67-a27d-2125a6fd8fcd] ('bfd40c7fc538b3d86925d91c60c...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\request.py", line 754, in execute_using_pool
    result = apply_async(
             └ <bound method BasePool.apply_async of <celery.concurrency.solo.TaskPool object at 0x00000160D812E2C0>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\concurrency\base.py", line 153, in apply_async
    return self.on_apply(target, args, kwargs,
           │    │        │       │     └ {}
           │    │        │       └ ('bisheng.worker.workflow.tasks.continue_workflow', '7ea84775-eb70-4e67-a27d-2125a6fd8fcd', {'lang': 'py', 'task': 'bisheng.w...
           │    │        └ <function fast_trace_task at 0x00000160FADBD480>
           │    └ <function apply_target at 0x00000160F9653130>
           └ <celery.concurrency.solo.TaskPool object at 0x00000160D812E2C0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\concurrency\base.py", line 30, in apply_target
    ret = target(*args, **kwargs)
          │       │       └ {}
          │       └ ('bisheng.worker.workflow.tasks.continue_workflow', '7ea84775-eb70-4e67-a27d-2125a6fd8fcd', {'lang': 'py', 'task': 'bisheng.w...
          └ <function fast_trace_task at 0x00000160FADBD480>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 651, in fast_trace_task
    R, I, T, Rstr = tasks[task].__trace__(
                    │     └ 'bisheng.worker.workflow.tasks.continue_workflow'
                    └ {'celery.chord': <@task: celery.chord of bisheng at 0x16096a88ca0>, 'celery.accumulate': <@task: celery.accumulate of bisheng...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 │    │       └ {}
                 │    └ ['bfd40c7fc538b3d86925d91c60c18fb4_async_task_id', 'ac8b23aed25947908ac042dc5b5f1594', 'bfd40c7fc538b3d86925d91c60c18fb4', '1']
                 └ <@task: bisheng.worker.workflow.tasks.continue_workflow of bisheng at 0x16096a88ca0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           │    │    │       └ {}
           │    │    └ ('bfd40c7fc538b3d86925d91c60c18fb4_async_task_id', 'ac8b23aed25947908ac042dc5b5f1594', 'bfd40c7fc538b3d86925d91c60c18fb4', '1')
           │    └ <staticmethod(<function continue_workflow at 0x00000160D0E64C10>)>
           └ <@task: bisheng.worker.workflow.tasks.continue_workflow of bisheng at 0x16096a88ca0>

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\worker\workflow\tasks.py", line 107, in continue_workflow
    _continue_workflow(unique_id, workflow_id, chat_id, user_id)
    │                  │          │            │        └ '1'
    │                  │          │            └ 'bfd40c7fc538b3d86925d91c60c18fb4'
    │                  │          └ 'ac8b23aed25947908ac042dc5b5f1594'
    │                  └ 'bfd40c7fc538b3d86925d91c60c18fb4_async_task_id'
    └ <function _continue_workflow at 0x00000160D0E64B80>

> File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\worker\workflow\tasks.py", line 84, in _continue_workflow
    raise Exception('workflow object not found maybe data is expired')

Exception: workflow object not found maybe data is expired
[2025-08-22 15:07:56.313038] [WARNING process-28468-8856 bisheng.worker.workflow.tasks:20] - trace=bfd40c7fc538b3d86925d91c60c18fb4_async_task_id workflow object not found for unique_id: bfd40c7fc538b3d86925d91c60c18fb4_async_task_id
[2025-08-22 15:09:07.749452] [INFO process-27016-31932 datasets.config:58] - trace=1 PyTorch version 2.8.0 available.
[2025-08-22 15:10:56.229481] [ERROR process-27016-31932 bisheng.worker.workflow.tasks:98] - trace=f18a7c85fc945bc84a478fe4facb0776_async_task_id continue_workflow error
Traceback (most recent call last):

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
           │         │     └ {'__name__': '__main__', '__doc__': 'Entry-point for the :program:`celery` umbrella command.', '__package__': 'celery', '__lo...
           │         └ <code object <module> at 0x000001A55AEFB100, file "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pa...
           └ <function _run_code at 0x000001A55AF19CF0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': 'Entry-point for the :program:`celery` umbrella command.', '__package__': 'celery', '__lo...
         └ <code object <module> at 0x000001A55AEFB100, file "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pa...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\__main__.py", line 19, in <module>
    main()
    └ <function main at 0x000001A55AF1A710>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\__main__.py", line 15, in main
    sys.exit(_main())
    │   │    └ <function main at 0x000001A55D2E0550>
    │   └ <built-in function exit>
    └ <module 'sys' (built-in)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
           └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {'auto_envvar_prefix': 'CELERY'}
           │    │     └ ()
           │    └ <function Command.main at 0x000001A55C8A3C70>
           └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000001A55AC3FB50>
         │    └ <function Group.invoke at 0x000001A55C8AC670>
         └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1830, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x000001A55D2BB190>
           │               │       │       └ <function Command.invoke at 0x000001A55C8A3B50>
           │               │       └ <CeleryDaemonCommand worker>
           │               └ <click.core.Context object at 0x000001A55D2BB190>
           └ <function Group.invoke.<locals>._process_result at 0x000001A55D2E0670>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │   │      │    │           └ <click.core.Context object at 0x000001A55D2BB190>
           │   │      │    └ <function worker at 0x000001A55D2E13F0>
           │   │      └ <CeleryDaemonCommand worker>
           │   └ <function Context.invoke at 0x000001A55C8A2EF0>
           └ <click.core.Context object at 0x000001A55D2BB190>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │         └ ()
           └ <function worker at 0x000001A55D2E13F0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\decorators.py", line 34, in new_func
    return f(get_current_context(), *args, **kwargs)
           │ │                       │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │ │                       └ ()
           │ └ <function get_current_context at 0x000001A55C81F1C0>
           └ <function worker at 0x000001A55D2E1360>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
           │ │     │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │ │     └ ()
           │ └ <click.core.Context object at 0x000001A55D2BB190>
           └ <function worker at 0x000001A55D2E12D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\worker.py", line 356, in worker
    worker.start()
    │      └ <function WorkController.start at 0x000001A55D212440>
    └ <Worker: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\worker.py", line 203, in start
    self.blueprint.start(self)
    │    │         │     └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │    │         └ <function Blueprint.start at 0x000001A55D1FECB0>
    │    └ <celery.worker.worker.WorkController.Blueprint object at 0x000001A53A59DAE0>
    └ <Worker: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 116, in start
    step.start(parent)
    │    │     └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │    └ <function StartStopStep.start at 0x000001A55D1FFD90>
    └ <step: Consumer>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 365, in start
    return self.obj.start()
           │    │   └ <function Consumer.start at 0x000001A53A61C820>
           │    └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
           └ <step: Consumer>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 341, in start
    blueprint.start(self)
    │         │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │         └ <function Blueprint.start at 0x000001A55D1FECB0>
    └ <celery.worker.consumer.consumer.Consumer.Blueprint object at 0x000001A53A5E1C60>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 116, in start
    step.start(parent)
    │    │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │    └ <function Evloop.start at 0x000001A53A61D990>
    └ <step: event loop>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 772, in start
    c.loop(*c.loop_args())
    │ │     │ └ <function Consumer.loop_args at 0x000001A53A61CCA0>
    │ │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │ └ <function synloop at 0x000001A53A5F3F40>
    └ <Consumer: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\loops.py", line 143, in synloop
    _loop_cycle()
    └ <function synloop.<locals>._loop_cycle at 0x000001A53A724310>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\loops.py", line 132, in _loop_cycle
    connection.drain_events(timeout=2.0)
    │          └ <function Connection.drain_events at 0x000001A55C685750>
    └ <Connection: redis://172.20.20.128:6379/2 at 0x1a53a699e10>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\connection.py", line 341, in drain_events
    return self.transport.drain_events(self.connection, **kwargs)
           │    │                      │    │             └ {'timeout': 2.0}
           │    │                      │    └ <property object at 0x000001A55C6798A0>
           │    │                      └ <Connection: redis://172.20.20.128:6379/2 at 0x1a53a699e10>
           │    └ <property object at 0x000001A55C679990>
           └ <Connection: redis://172.20.20.128:6379/2 at 0x1a53a699e10>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 997, in drain_events
    get(self._deliver, timeout=timeout)
    │   │    │                 └ 2.0
    │   │    └ <function Transport._deliver at 0x000001A53A5E5240>
    │   └ <kombu.transport.redis.Transport object at 0x000001A53A69AC50>
    └ <bound method MultiChannelPoller.get of <kombu.transport.redis.MultiChannelPoller object at 0x000001A53A69AD40>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 598, in get
    ret = self.handle_event(fileno, event)
          │    │            │       └ 1
          │    │            └ 8172
          │    └ <function MultiChannelPoller.handle_event at 0x000001A53A5E7370>
          └ <kombu.transport.redis.MultiChannelPoller object at 0x000001A53A69AD40>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 580, in handle_event
    return self.on_readable(fileno), self
           │    │           │        └ <kombu.transport.redis.MultiChannelPoller object at 0x000001A53A69AD40>
           │    │           └ 8172
           │    └ <function MultiChannelPoller.on_readable at 0x000001A53A5E72E0>
           └ <kombu.transport.redis.MultiChannelPoller object at 0x000001A53A69AD40>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 576, in on_readable
    chan.handlers[type]()
    │    │        └ 'BRPOP'
    │    └ {'BRPOP': <bound method Channel._brpop_read of <kombu.transport.redis.Channel object at 0x000001A53A69ACE0>>, 'LISTEN': <boun...
    └ <kombu.transport.redis.Channel object at 0x000001A53A69ACE0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 986, in _brpop_read
    self.connection._deliver(loads(bytes_to_str(item)), dest)
    │    │          │        │     │            │       └ 'workflow_celery'
    │    │          │        │     │            └ b'{"body": "W1siZjE4YTdjODVmYzk0NWJjODRhNDc4ZmU0ZmFjYjA3NzZfYXN5bmNfdGFza19pZCIsICJhYzhiMjNhZWQyNTk0NzkwOGFjMDQyZGM1YjVmMTU5N...
    │    │          │        │     └ <function bytes_to_str at 0x000001A55BB0EF80>
    │    │          │        └ <function loads at 0x000001A55C693010>
    │    │          └ <function Transport._deliver at 0x000001A53A5E5240>
    │    └ <kombu.transport.redis.Transport object at 0x000001A53A69AC50>
    └ <kombu.transport.redis.Channel object at 0x000001A53A69ACE0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 1017, in _deliver
    callback(message)
    │        └ {'body': 'W1siZjE4YTdjODVmYzk0NWJjODRhNDc4ZmU0ZmFjYjA3NzZfYXN5bmNfdGFza19pZCIsICJhYzhiMjNhZWQyNTk0NzkwOGFjMDQyZGM1YjVmMTU5NCI...
    └ <function Channel.basic_consume.<locals>._callback at 0x000001A53A724160>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 639, in _callback
    return callback(message)
           │        └ <Message object at 0x1a53a6b8790 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': 'fa88...
           └ <bound method Consumer._receive_callback of <Consumer: [<Queue workflow_celery -> <Exchange workflow_celery(direct) bound to ...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\messaging.py", line 668, in _receive_callback
    return on_m(message) if on_m else self.receive(decoded, message)
           │    │           │         │    │       │        └ <Message object at 0x1a53a6b8790 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': 'fa88...
           │    │           │         │    │       └ None
           │    │           │         │    └ <function Consumer.receive at 0x000001A55CA02170>
           │    │           │         └ <Consumer: [<Queue workflow_celery -> <Exchange workflow_celery(direct) bound to chan:1> -> workflow_celery bound to chan:1>,...
           │    │           └ <function Consumer.create_task_handler.<locals>.on_task_received at 0x000001A53A7240D0>
           │    └ <Message object at 0x1a53a6b8790 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': 'fa88...
           └ <function Consumer.create_task_handler.<locals>.on_task_received at 0x000001A53A7240D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 688, in on_task_received
    strategy(
    └ <function default.<locals>.task_message_handler at 0x000001A53A6BA560>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\strategy.py", line 207, in task_message_handler
    handle(req)
    │      └ <Request: bisheng.worker.workflow.tasks.continue_workflow[307295a1-e11b-4a12-aa96-3d3320128e77] ('f18a7c85fc945bc84a478fe4fac...
    └ <bound method WorkController._process_task of <Worker: celery@WIN-S45T7NVPCSH (running)>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\worker.py", line 226, in _process_task
    req.execute_using_pool(self.pool)
    │   │                  │    └ <celery.concurrency.solo.TaskPool object at 0x000001A53A5E1F00>
    │   │                  └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │   └ <function create_request_cls.<locals>.Request.execute_using_pool at 0x000001A53A6BA5F0>
    └ <Request: bisheng.worker.workflow.tasks.continue_workflow[307295a1-e11b-4a12-aa96-3d3320128e77] ('f18a7c85fc945bc84a478fe4fac...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\request.py", line 754, in execute_using_pool
    result = apply_async(
             └ <bound method BasePool.apply_async of <celery.concurrency.solo.TaskPool object at 0x000001A53A5E1F00>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\concurrency\base.py", line 153, in apply_async
    return self.on_apply(target, args, kwargs,
           │    │        │       │     └ {}
           │    │        │       └ ('bisheng.worker.workflow.tasks.continue_workflow', '307295a1-e11b-4a12-aa96-3d3320128e77', {'lang': 'py', 'task': 'bisheng.w...
           │    │        └ <function fast_trace_task at 0x000001A55D27D480>
           │    └ <function apply_target at 0x000001A55BB13130>
           └ <celery.concurrency.solo.TaskPool object at 0x000001A53A5E1F00>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\concurrency\base.py", line 30, in apply_target
    ret = target(*args, **kwargs)
          │       │       └ {}
          │       └ ('bisheng.worker.workflow.tasks.continue_workflow', '307295a1-e11b-4a12-aa96-3d3320128e77', {'lang': 'py', 'task': 'bisheng.w...
          └ <function fast_trace_task at 0x000001A55D27D480>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 651, in fast_trace_task
    R, I, T, Rstr = tasks[task].__trace__(
                    │     └ 'bisheng.worker.workflow.tasks.continue_workflow'
                    └ {'celery.chord': <@task: celery.chord of bisheng at 0x1a578988cd0>, 'celery.accumulate': <@task: celery.accumulate of bisheng...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 │    │       └ {}
                 │    └ ['f18a7c85fc945bc84a478fe4facb0776_async_task_id', 'ac8b23aed25947908ac042dc5b5f1594', 'f18a7c85fc945bc84a478fe4facb0776', '1']
                 └ <@task: bisheng.worker.workflow.tasks.continue_workflow of bisheng at 0x1a578988cd0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           │    │    │       └ {}
           │    │    └ ('f18a7c85fc945bc84a478fe4facb0776_async_task_id', 'ac8b23aed25947908ac042dc5b5f1594', 'f18a7c85fc945bc84a478fe4facb0776', '1')
           │    └ <staticmethod(<function continue_workflow at 0x000001A533324C10>)>
           └ <@task: bisheng.worker.workflow.tasks.continue_workflow of bisheng at 0x1a578988cd0>

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\worker\workflow\tasks.py", line 107, in continue_workflow
    _continue_workflow(unique_id, workflow_id, chat_id, user_id)
    │                  │          │            │        └ '1'
    │                  │          │            └ 'f18a7c85fc945bc84a478fe4facb0776'
    │                  │          └ 'ac8b23aed25947908ac042dc5b5f1594'
    │                  └ 'f18a7c85fc945bc84a478fe4facb0776_async_task_id'
    └ <function _continue_workflow at 0x000001A533324B80>

> File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\worker\workflow\tasks.py", line 84, in _continue_workflow
    raise Exception('workflow object not found maybe data is expired')

Exception: workflow object not found maybe data is expired
[2025-08-22 15:10:56.315204] [WARNING process-27016-31932 bisheng.worker.workflow.tasks:20] - trace=f18a7c85fc945bc84a478fe4facb0776_async_task_id workflow object not found for unique_id: f18a7c85fc945bc84a478fe4facb0776_async_task_id
[2025-08-22 15:11:13.116619] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:32] - trace=f18a7c85fc945bc84a478fe4facb0776_async_task_id === StartNode self 内部信息 ===
[2025-08-22 15:11:13.120893] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=f18a7c85fc945bc84a478fe4facb0776_async_task_id id: start_553b9
[2025-08-22 15:11:13.125969] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=f18a7c85fc945bc84a478fe4facb0776_async_task_id type: start
[2025-08-22 15:11:13.130069] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=f18a7c85fc945bc84a478fe4facb0776_async_task_id name: 开始
[2025-08-22 15:11:13.135662] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=f18a7c85fc945bc84a478fe4facb0776_async_task_id description: 工作流运行的起始节点。
[2025-08-22 15:11:13.142674] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=f18a7c85fc945bc84a478fe4facb0776_async_task_id target_edges: [EdgeBase(id='xy-edge__start_553b9right_handle-input_2775bleft_handle', source='start_553b9', sourceHandle='right_handle', sourceType='', target='input_2775b', targetHandle='left_handle', targetType='')]
[2025-08-22 15:11:13.149219] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=f18a7c85fc945bc84a478fe4facb0776_async_task_id user_id: 1
[2025-08-22 15:11:13.153749] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=f18a7c85fc945bc84a478fe4facb0776_async_task_id workflow_id: ac8b23aed25947908ac042dc5b5f1594
[2025-08-22 15:11:13.160002] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=f18a7c85fc945bc84a478fe4facb0776_async_task_id graph_state: history_memory=ConversationBufferWindowMemory(chat_memory=InMemoryChatMessageHistory(messages=[]), k=10) variables_pool={}
[2025-08-22 15:11:13.165549] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=f18a7c85fc945bc84a478fe4facb0776_async_task_id node_data: id='start_553b9' type='start' name='开始' description='工作流运行的起始节点。' group_params=[NodeGroupParams(name='开场引导', params=[NodeParams(key='guide_word', label='开场白', value='您好，请问想聊些什么呢？', type='textarea', help='', tab='', placeholder='每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。', required=False, options=None), NodeParams(key='guide_question', label='引导问题', value=['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], type='input_list', help='为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', tab='', placeholder='请输入引导问题', required=False, options=None)], description=''), NodeGroupParams(name='全局变量', params=[NodeParams(key='current_time', label='当前时间', value='', type='var', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='chat_history', label='', value=10, type='chat_history_num', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='preset_question', label='预置问题列表', value=[{'key': 'f4cbae', 'value': ''}], type='input_list', help='适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', tab='', placeholder='输入批量预置问题', required=False, options=None)], description='')] tab={} tool_key='' v=1
[2025-08-22 15:11:13.747855] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=f18a7c85fc945bc84a478fe4facb0776_async_task_id node_params: {'guide_word': '您好，请问想聊些什么呢？', 'guide_question': ['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], 'current_time': '2025-08-22 15:11:12', 'chat_history': 10, 'preset_question': [{'key': 'f4cbae', 'value': ''}]}
[2025-08-22 15:11:13.751947] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=f18a7c85fc945bc84a478fe4facb0776_async_task_id other_node_variable: {}
[2025-08-22 15:11:13.757031] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=f18a7c85fc945bc84a478fe4facb0776_async_task_id current_step: 0
[2025-08-22 15:11:13.762027] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=f18a7c85fc945bc84a478fe4facb0776_async_task_id max_steps: 50
[2025-08-22 15:11:13.766192] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=f18a7c85fc945bc84a478fe4facb0776_async_task_id callback_manager: <bisheng.worker.workflow.redis_callback.RedisCallback object at 0x000001A53A70FA90>
[2025-08-22 15:11:13.770210] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=f18a7c85fc945bc84a478fe4facb0776_async_task_id tmp_collection_name: tmp_workflow_data
[2025-08-22 15:11:13.773171] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=f18a7c85fc945bc84a478fe4facb0776_async_task_id stop_flag: False
[2025-08-22 15:11:13.777605] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=f18a7c85fc945bc84a478fe4facb0776_async_task_id exec_unique_id: 17c74b2aa1144e9db4b45bb77bdac94a
[2025-08-22 15:11:13.780604] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:35] - trace=f18a7c85fc945bc84a478fe4facb0776_async_task_id === StartNode self 信息结束 ===
[2025-08-22 15:11:52.251375] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:32] - trace=123eded972cd421bb412dde2de07de05 === StartNode self 内部信息 ===
[2025-08-22 15:11:52.254854] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=123eded972cd421bb412dde2de07de05 id: start_553b9
[2025-08-22 15:11:52.256935] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=123eded972cd421bb412dde2de07de05 type: start
[2025-08-22 15:11:52.258095] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=123eded972cd421bb412dde2de07de05 name: 开始
[2025-08-22 15:11:52.260128] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=123eded972cd421bb412dde2de07de05 description: 工作流运行的起始节点。
[2025-08-22 15:11:52.261138] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=123eded972cd421bb412dde2de07de05 target_edges: [EdgeBase(id='xy-edge__start_553b9right_handle-input_2775bleft_handle', source='start_553b9', sourceHandle='right_handle', sourceType='', target='input_2775b', targetHandle='left_handle', targetType='')]
[2025-08-22 15:11:52.265184] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=123eded972cd421bb412dde2de07de05 user_id: 1
[2025-08-22 15:11:52.268301] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=123eded972cd421bb412dde2de07de05 workflow_id: ac8b23aed25947908ac042dc5b5f1594
[2025-08-22 15:11:52.270386] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=123eded972cd421bb412dde2de07de05 graph_state: history_memory=ConversationBufferWindowMemory(chat_memory=InMemoryChatMessageHistory(messages=[]), k=10) variables_pool={}
[2025-08-22 15:11:52.272533] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=123eded972cd421bb412dde2de07de05 node_data: id='start_553b9' type='start' name='开始' description='工作流运行的起始节点。' group_params=[NodeGroupParams(name='开场引导', params=[NodeParams(key='guide_word', label='开场白', value='您好，请问想聊些什么呢？', type='textarea', help='', tab='', placeholder='每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。', required=False, options=None), NodeParams(key='guide_question', label='引导问题', value=['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], type='input_list', help='为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', tab='', placeholder='请输入引导问题', required=False, options=None)], description=''), NodeGroupParams(name='全局变量', params=[NodeParams(key='current_time', label='当前时间', value='', type='var', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='chat_history', label='', value=10, type='chat_history_num', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='preset_question', label='预置问题列表', value=[{'key': 'f4cbae', 'value': ''}], type='input_list', help='适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', tab='', placeholder='输入批量预置问题', required=False, options=None)], description='')] tab={} tool_key='' v=1
[2025-08-22 15:11:52.278885] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=123eded972cd421bb412dde2de07de05 node_params: {'guide_word': '您好，请问想聊些什么呢？', 'guide_question': ['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], 'current_time': '2025-08-22 15:11:52', 'chat_history': 10, 'preset_question': [{'key': 'f4cbae', 'value': ''}]}
[2025-08-22 15:11:52.283400] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=123eded972cd421bb412dde2de07de05 other_node_variable: {}
[2025-08-22 15:11:52.287388] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=123eded972cd421bb412dde2de07de05 current_step: 0
[2025-08-22 15:11:52.289399] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=123eded972cd421bb412dde2de07de05 max_steps: 50
[2025-08-22 15:11:52.291398] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=123eded972cd421bb412dde2de07de05 callback_manager: <bisheng.worker.workflow.redis_callback.RedisCallback object at 0x000001A53AA32590>
[2025-08-22 15:11:52.294575] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=123eded972cd421bb412dde2de07de05 tmp_collection_name: tmp_workflow_data
[2025-08-22 15:11:52.296588] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=123eded972cd421bb412dde2de07de05 stop_flag: False
[2025-08-22 15:11:52.301937] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:34] - trace=123eded972cd421bb412dde2de07de05 exec_unique_id: 29c2179ead7646b4a31da0911a524dc5
[2025-08-22 15:11:52.305804] [INFO process-27016-31932 bisheng.workflow.nodes.start.start:35] - trace=123eded972cd421bb412dde2de07de05 === StartNode self 信息结束 ===
[2025-08-22 15:12:51.272177] [WARNING process-27016-31932 bisheng.worker.workflow.tasks:116] - trace=e4b0bf531f624633a6adb184528a8d8d stop_workflow called but workflow not found in global cache
[2025-08-22 15:13:09.906266] [ERROR process-27016-31932 bisheng.worker.workflow.tasks:98] - trace=fc2672fd66cc460f81583d71fee5766d continue_workflow error
Traceback (most recent call last):

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
           │         │     └ {'__name__': '__main__', '__doc__': 'Entry-point for the :program:`celery` umbrella command.', '__package__': 'celery', '__lo...
           │         └ <code object <module> at 0x000001A55AEFB100, file "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pa...
           └ <function _run_code at 0x000001A55AF19CF0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': 'Entry-point for the :program:`celery` umbrella command.', '__package__': 'celery', '__lo...
         └ <code object <module> at 0x000001A55AEFB100, file "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pa...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\__main__.py", line 19, in <module>
    main()
    └ <function main at 0x000001A55AF1A710>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\__main__.py", line 15, in main
    sys.exit(_main())
    │   │    └ <function main at 0x000001A55D2E0550>
    │   └ <built-in function exit>
    └ <module 'sys' (built-in)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
           └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {'auto_envvar_prefix': 'CELERY'}
           │    │     └ ()
           │    └ <function Command.main at 0x000001A55C8A3C70>
           └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000001A55AC3FB50>
         │    └ <function Group.invoke at 0x000001A55C8AC670>
         └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1830, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x000001A55D2BB190>
           │               │       │       └ <function Command.invoke at 0x000001A55C8A3B50>
           │               │       └ <CeleryDaemonCommand worker>
           │               └ <click.core.Context object at 0x000001A55D2BB190>
           └ <function Group.invoke.<locals>._process_result at 0x000001A55D2E0670>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │   │      │    │           └ <click.core.Context object at 0x000001A55D2BB190>
           │   │      │    └ <function worker at 0x000001A55D2E13F0>
           │   │      └ <CeleryDaemonCommand worker>
           │   └ <function Context.invoke at 0x000001A55C8A2EF0>
           └ <click.core.Context object at 0x000001A55D2BB190>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │         └ ()
           └ <function worker at 0x000001A55D2E13F0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\decorators.py", line 34, in new_func
    return f(get_current_context(), *args, **kwargs)
           │ │                       │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │ │                       └ ()
           │ └ <function get_current_context at 0x000001A55C81F1C0>
           └ <function worker at 0x000001A55D2E1360>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
           │ │     │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │ │     └ ()
           │ └ <click.core.Context object at 0x000001A55D2BB190>
           └ <function worker at 0x000001A55D2E12D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\worker.py", line 356, in worker
    worker.start()
    │      └ <function WorkController.start at 0x000001A55D212440>
    └ <Worker: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\worker.py", line 203, in start
    self.blueprint.start(self)
    │    │         │     └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │    │         └ <function Blueprint.start at 0x000001A55D1FECB0>
    │    └ <celery.worker.worker.WorkController.Blueprint object at 0x000001A53A59DAE0>
    └ <Worker: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 116, in start
    step.start(parent)
    │    │     └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │    └ <function StartStopStep.start at 0x000001A55D1FFD90>
    └ <step: Consumer>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 365, in start
    return self.obj.start()
           │    │   └ <function Consumer.start at 0x000001A53A61C820>
           │    └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
           └ <step: Consumer>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 341, in start
    blueprint.start(self)
    │         │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │         └ <function Blueprint.start at 0x000001A55D1FECB0>
    └ <celery.worker.consumer.consumer.Consumer.Blueprint object at 0x000001A53A5E1C60>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 116, in start
    step.start(parent)
    │    │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │    └ <function Evloop.start at 0x000001A53A61D990>
    └ <step: event loop>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 772, in start
    c.loop(*c.loop_args())
    │ │     │ └ <function Consumer.loop_args at 0x000001A53A61CCA0>
    │ │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │ └ <function synloop at 0x000001A53A5F3F40>
    └ <Consumer: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\loops.py", line 143, in synloop
    _loop_cycle()
    └ <function synloop.<locals>._loop_cycle at 0x000001A53A724310>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\loops.py", line 132, in _loop_cycle
    connection.drain_events(timeout=2.0)
    │          └ <function Connection.drain_events at 0x000001A55C685750>
    └ <Connection: redis://172.20.20.128:6379/2 at 0x1a53a699e10>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\connection.py", line 341, in drain_events
    return self.transport.drain_events(self.connection, **kwargs)
           │    │                      │    │             └ {'timeout': 2.0}
           │    │                      │    └ <property object at 0x000001A55C6798A0>
           │    │                      └ <Connection: redis://172.20.20.128:6379/2 at 0x1a53a699e10>
           │    └ <property object at 0x000001A55C679990>
           └ <Connection: redis://172.20.20.128:6379/2 at 0x1a53a699e10>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 997, in drain_events
    get(self._deliver, timeout=timeout)
    │   │    │                 └ 2.0
    │   │    └ <function Transport._deliver at 0x000001A53A5E5240>
    │   └ <kombu.transport.redis.Transport object at 0x000001A53A69AC50>
    └ <bound method MultiChannelPoller.get of <kombu.transport.redis.MultiChannelPoller object at 0x000001A53A69AD40>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 598, in get
    ret = self.handle_event(fileno, event)
          │    │            │       └ 1
          │    │            └ 8172
          │    └ <function MultiChannelPoller.handle_event at 0x000001A53A5E7370>
          └ <kombu.transport.redis.MultiChannelPoller object at 0x000001A53A69AD40>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 580, in handle_event
    return self.on_readable(fileno), self
           │    │           │        └ <kombu.transport.redis.MultiChannelPoller object at 0x000001A53A69AD40>
           │    │           └ 8172
           │    └ <function MultiChannelPoller.on_readable at 0x000001A53A5E72E0>
           └ <kombu.transport.redis.MultiChannelPoller object at 0x000001A53A69AD40>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 576, in on_readable
    chan.handlers[type]()
    │    │        └ 'BRPOP'
    │    └ {'BRPOP': <bound method Channel._brpop_read of <kombu.transport.redis.Channel object at 0x000001A53A69ACE0>>, 'LISTEN': <boun...
    └ <kombu.transport.redis.Channel object at 0x000001A53A69ACE0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 986, in _brpop_read
    self.connection._deliver(loads(bytes_to_str(item)), dest)
    │    │          │        │     │            │       └ 'workflow_celery'
    │    │          │        │     │            └ b'{"body": "W1siZmMyNjcyZmQ2NmNjNDYwZjgxNTgzZDcxZmVlNTc2NmQiLCAiYWM4YjIzYWVkMjU5NDc5MDhhYzA0MmRjNWI1ZjE1OTQiLCAiIiwgIjEiXSwge...
    │    │          │        │     └ <function bytes_to_str at 0x000001A55BB0EF80>
    │    │          │        └ <function loads at 0x000001A55C693010>
    │    │          └ <function Transport._deliver at 0x000001A53A5E5240>
    │    └ <kombu.transport.redis.Transport object at 0x000001A53A69AC50>
    └ <kombu.transport.redis.Channel object at 0x000001A53A69ACE0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 1017, in _deliver
    callback(message)
    │        └ {'body': 'W1siZmMyNjcyZmQ2NmNjNDYwZjgxNTgzZDcxZmVlNTc2NmQiLCAiYWM4YjIzYWVkMjU5NDc5MDhhYzA0MmRjNWI1ZjE1OTQiLCAiIiwgIjEiXSwge30...
    └ <function Channel.basic_consume.<locals>._callback at 0x000001A53A724160>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 639, in _callback
    return callback(message)
           │        └ <Message object at 0x1a53aa2aef0 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': '410d...
           └ <bound method Consumer._receive_callback of <Consumer: [<Queue workflow_celery -> <Exchange workflow_celery(direct) bound to ...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\messaging.py", line 668, in _receive_callback
    return on_m(message) if on_m else self.receive(decoded, message)
           │    │           │         │    │       │        └ <Message object at 0x1a53aa2aef0 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': '410d...
           │    │           │         │    │       └ None
           │    │           │         │    └ <function Consumer.receive at 0x000001A55CA02170>
           │    │           │         └ <Consumer: [<Queue workflow_celery -> <Exchange workflow_celery(direct) bound to chan:1> -> workflow_celery bound to chan:1>,...
           │    │           └ <function Consumer.create_task_handler.<locals>.on_task_received at 0x000001A53A7240D0>
           │    └ <Message object at 0x1a53aa2aef0 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': '410d...
           └ <function Consumer.create_task_handler.<locals>.on_task_received at 0x000001A53A7240D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 688, in on_task_received
    strategy(
    └ <function default.<locals>.task_message_handler at 0x000001A53A6BA560>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\strategy.py", line 207, in task_message_handler
    handle(req)
    │      └ <Request: bisheng.worker.workflow.tasks.continue_workflow[5d19ed8b-3d13-4a1d-8451-0c17598ccd5e] ('fc2672fd66cc460f81583d71fee...
    └ <bound method WorkController._process_task of <Worker: celery@WIN-S45T7NVPCSH (running)>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\worker.py", line 226, in _process_task
    req.execute_using_pool(self.pool)
    │   │                  │    └ <celery.concurrency.solo.TaskPool object at 0x000001A53A5E1F00>
    │   │                  └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │   └ <function create_request_cls.<locals>.Request.execute_using_pool at 0x000001A53A6BA5F0>
    └ <Request: bisheng.worker.workflow.tasks.continue_workflow[5d19ed8b-3d13-4a1d-8451-0c17598ccd5e] ('fc2672fd66cc460f81583d71fee...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\request.py", line 754, in execute_using_pool
    result = apply_async(
             └ <bound method BasePool.apply_async of <celery.concurrency.solo.TaskPool object at 0x000001A53A5E1F00>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\concurrency\base.py", line 153, in apply_async
    return self.on_apply(target, args, kwargs,
           │    │        │       │     └ {}
           │    │        │       └ ('bisheng.worker.workflow.tasks.continue_workflow', '5d19ed8b-3d13-4a1d-8451-0c17598ccd5e', {'lang': 'py', 'task': 'bisheng.w...
           │    │        └ <function fast_trace_task at 0x000001A55D27D480>
           │    └ <function apply_target at 0x000001A55BB13130>
           └ <celery.concurrency.solo.TaskPool object at 0x000001A53A5E1F00>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\concurrency\base.py", line 30, in apply_target
    ret = target(*args, **kwargs)
          │       │       └ {}
          │       └ ('bisheng.worker.workflow.tasks.continue_workflow', '5d19ed8b-3d13-4a1d-8451-0c17598ccd5e', {'lang': 'py', 'task': 'bisheng.w...
          └ <function fast_trace_task at 0x000001A55D27D480>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 651, in fast_trace_task
    R, I, T, Rstr = tasks[task].__trace__(
                    │     └ 'bisheng.worker.workflow.tasks.continue_workflow'
                    └ {'celery.chord': <@task: celery.chord of bisheng at 0x1a578988cd0>, 'celery.accumulate': <@task: celery.accumulate of bisheng...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 │    │       └ {}
                 │    └ ['fc2672fd66cc460f81583d71fee5766d', 'ac8b23aed25947908ac042dc5b5f1594', '', '1']
                 └ <@task: bisheng.worker.workflow.tasks.continue_workflow of bisheng at 0x1a578988cd0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           │    │    │       └ {}
           │    │    └ ('fc2672fd66cc460f81583d71fee5766d', 'ac8b23aed25947908ac042dc5b5f1594', '', '1')
           │    └ <staticmethod(<function continue_workflow at 0x000001A533324C10>)>
           └ <@task: bisheng.worker.workflow.tasks.continue_workflow of bisheng at 0x1a578988cd0>

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\worker\workflow\tasks.py", line 107, in continue_workflow
    _continue_workflow(unique_id, workflow_id, chat_id, user_id)
    │                  │          │            │        └ '1'
    │                  │          │            └ ''
    │                  │          └ 'ac8b23aed25947908ac042dc5b5f1594'
    │                  └ 'fc2672fd66cc460f81583d71fee5766d'
    └ <function _continue_workflow at 0x000001A533324B80>

> File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\worker\workflow\tasks.py", line 84, in _continue_workflow
    raise Exception('workflow object not found maybe data is expired')

Exception: workflow object not found maybe data is expired
[2025-08-22 15:13:10.067247] [WARNING process-27016-31932 bisheng.worker.workflow.tasks:20] - trace=fc2672fd66cc460f81583d71fee5766d workflow object not found for unique_id: fc2672fd66cc460f81583d71fee5766d
[2025-08-22 15:15:11.492518] [INFO process-948-27812 datasets.config:58] - trace=1 PyTorch version 2.8.0 available.
[2025-08-22 15:16:22.026454] [ERROR process-948-27812 bisheng.worker.workflow.tasks:98] - trace=e3c6eca267a445cbac7acdaca25d430e continue_workflow error
Traceback (most recent call last):

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
           │         │     └ {'__name__': '__main__', '__doc__': 'Entry-point for the :program:`celery` umbrella command.', '__package__': 'celery', '__lo...
           │         └ <code object <module> at 0x00000129BAA7B100, file "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pa...
           └ <function _run_code at 0x00000129BAA99CF0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': 'Entry-point for the :program:`celery` umbrella command.', '__package__': 'celery', '__lo...
         └ <code object <module> at 0x00000129BAA7B100, file "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pa...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\__main__.py", line 19, in <module>
    main()
    └ <function main at 0x00000129BAA9A710>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\__main__.py", line 15, in main
    sys.exit(_main())
    │   │    └ <function main at 0x00000129BCDB0550>
    │   └ <built-in function exit>
    └ <module 'sys' (built-in)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
           └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {'auto_envvar_prefix': 'CELERY'}
           │    │     └ ()
           │    └ <function Command.main at 0x00000129BC3A3C70>
           └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x00000129BA82FB50>
         │    └ <function Group.invoke at 0x00000129BC3AC670>
         └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1830, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x00000129BCD8B190>
           │               │       │       └ <function Command.invoke at 0x00000129BC3A3B50>
           │               │       └ <CeleryDaemonCommand worker>
           │               └ <click.core.Context object at 0x00000129BCD8B190>
           └ <function Group.invoke.<locals>._process_result at 0x00000129BCDB0670>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │   │      │    │           └ <click.core.Context object at 0x00000129BCD8B190>
           │   │      │    └ <function worker at 0x00000129BCDB13F0>
           │   │      └ <CeleryDaemonCommand worker>
           │   └ <function Context.invoke at 0x00000129BC3A2EF0>
           └ <click.core.Context object at 0x00000129BCD8B190>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │         └ ()
           └ <function worker at 0x00000129BCDB13F0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\decorators.py", line 34, in new_func
    return f(get_current_context(), *args, **kwargs)
           │ │                       │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │ │                       └ ()
           │ └ <function get_current_context at 0x00000129BC33F1C0>
           └ <function worker at 0x00000129BCDB1360>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
           │ │     │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │ │     └ ()
           │ └ <click.core.Context object at 0x00000129BCD8B190>
           └ <function worker at 0x00000129BCDB12D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\worker.py", line 356, in worker
    worker.start()
    │      └ <function WorkController.start at 0x00000129BCCE2440>
    └ <Worker: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\worker.py", line 203, in start
    self.blueprint.start(self)
    │    │         │     └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │    │         └ <function Blueprint.start at 0x00000129BCCCECB0>
    │    └ <celery.worker.worker.WorkController.Blueprint object at 0x000001299D429D50>
    └ <Worker: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 116, in start
    step.start(parent)
    │    │     └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │    └ <function StartStopStep.start at 0x00000129BCCCFD90>
    └ <step: Consumer>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 365, in start
    return self.obj.start()
           │    │   └ <function Consumer.start at 0x000001299D4B0790>
           │    └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
           └ <step: Consumer>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 341, in start
    blueprint.start(self)
    │         │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │         └ <function Blueprint.start at 0x00000129BCCCECB0>
    └ <celery.worker.consumer.consumer.Consumer.Blueprint object at 0x000001299D46DED0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 116, in start
    step.start(parent)
    │    │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │    └ <function Evloop.start at 0x000001299D4B1900>
    └ <step: event loop>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 772, in start
    c.loop(*c.loop_args())
    │ │     │ └ <function Consumer.loop_args at 0x000001299D4B0C10>
    │ │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │ └ <function synloop at 0x000001299D47FEB0>
    └ <Consumer: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\loops.py", line 143, in synloop
    _loop_cycle()
    └ <function synloop.<locals>._loop_cycle at 0x000001299D5B4280>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\loops.py", line 132, in _loop_cycle
    connection.drain_events(timeout=2.0)
    │          └ <function Connection.drain_events at 0x00000129BC1B5750>
    └ <Connection: redis://172.20.20.128:6379/2 at 0x1299d539c30>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\connection.py", line 341, in drain_events
    return self.transport.drain_events(self.connection, **kwargs)
           │    │                      │    │             └ {'timeout': 2.0}
           │    │                      │    └ <property object at 0x00000129BC1A9760>
           │    │                      └ <Connection: redis://172.20.20.128:6379/2 at 0x1299d539c30>
           │    └ <property object at 0x00000129BC1A9850>
           └ <Connection: redis://172.20.20.128:6379/2 at 0x1299d539c30>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 997, in drain_events
    get(self._deliver, timeout=timeout)
    │   │    │                 └ 2.0
    │   │    └ <function Transport._deliver at 0x000001299D4791B0>
    │   └ <kombu.transport.redis.Transport object at 0x000001299D53B0D0>
    └ <bound method MultiChannelPoller.get of <kombu.transport.redis.MultiChannelPoller object at 0x000001299D53B1C0>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 598, in get
    ret = self.handle_event(fileno, event)
          │    │            │       └ 1
          │    │            └ 7468
          │    └ <function MultiChannelPoller.handle_event at 0x000001299D47B2E0>
          └ <kombu.transport.redis.MultiChannelPoller object at 0x000001299D53B1C0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 580, in handle_event
    return self.on_readable(fileno), self
           │    │           │        └ <kombu.transport.redis.MultiChannelPoller object at 0x000001299D53B1C0>
           │    │           └ 7468
           │    └ <function MultiChannelPoller.on_readable at 0x000001299D47B250>
           └ <kombu.transport.redis.MultiChannelPoller object at 0x000001299D53B1C0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 576, in on_readable
    chan.handlers[type]()
    │    │        └ 'BRPOP'
    │    └ {'BRPOP': <bound method Channel._brpop_read of <kombu.transport.redis.Channel object at 0x000001299D53B160>>, 'LISTEN': <boun...
    └ <kombu.transport.redis.Channel object at 0x000001299D53B160>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 986, in _brpop_read
    self.connection._deliver(loads(bytes_to_str(item)), dest)
    │    │          │        │     │            │       └ 'workflow_celery'
    │    │          │        │     │            └ b'{"body": "W1siZTNjNmVjYTI2N2E0NDVjYmFjN2FjZGFjYTI1ZDQzMGUiLCAiYWM4YjIzYWVkMjU5NDc5MDhhYzA0MmRjNWI1ZjE1OTQiLCAiIiwgIjEiXSwge...
    │    │          │        │     └ <function bytes_to_str at 0x00000129BB64EF80>
    │    │          │        └ <function loads at 0x00000129BC1C3010>
    │    │          └ <function Transport._deliver at 0x000001299D4791B0>
    │    └ <kombu.transport.redis.Transport object at 0x000001299D53B0D0>
    └ <kombu.transport.redis.Channel object at 0x000001299D53B160>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 1017, in _deliver
    callback(message)
    │        └ {'body': 'W1siZTNjNmVjYTI2N2E0NDVjYmFjN2FjZGFjYTI1ZDQzMGUiLCAiYWM4YjIzYWVkMjU5NDc5MDhhYzA0MmRjNWI1ZjE1OTQiLCAiIiwgIjEiXSwge30...
    └ <function Channel.basic_consume.<locals>._callback at 0x000001299D5B40D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 639, in _callback
    return callback(message)
           │        └ <Message object at 0x1299d5b4820 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': '54ee...
           └ <bound method Consumer._receive_callback of <Consumer: [<Queue workflow_celery -> <Exchange workflow_celery(direct) bound to ...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\messaging.py", line 668, in _receive_callback
    return on_m(message) if on_m else self.receive(decoded, message)
           │    │           │         │    │       │        └ <Message object at 0x1299d5b4820 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': '54ee...
           │    │           │         │    │       └ None
           │    │           │         │    └ <function Consumer.receive at 0x00000129BC4F2170>
           │    │           │         └ <Consumer: [<Queue workflow_celery -> <Exchange workflow_celery(direct) bound to chan:1> -> workflow_celery bound to chan:1>,...
           │    │           └ <function Consumer.create_task_handler.<locals>.on_task_received at 0x000001299D5B4040>
           │    └ <Message object at 0x1299d5b4820 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': '54ee...
           └ <function Consumer.create_task_handler.<locals>.on_task_received at 0x000001299D5B4040>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 688, in on_task_received
    strategy(
    └ <function default.<locals>.task_message_handler at 0x000001299D5524D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\strategy.py", line 207, in task_message_handler
    handle(req)
    │      └ <Request: bisheng.worker.workflow.tasks.continue_workflow[807d3c2d-1ec7-4ddc-b7fa-5bafe4f84c08] ('e3c6eca267a445cbac7acdaca25...
    └ <bound method WorkController._process_task of <Worker: celery@WIN-S45T7NVPCSH (running)>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\worker.py", line 226, in _process_task
    req.execute_using_pool(self.pool)
    │   │                  │    └ <celery.concurrency.solo.TaskPool object at 0x000001299D46E170>
    │   │                  └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │   └ <function create_request_cls.<locals>.Request.execute_using_pool at 0x000001299D552560>
    └ <Request: bisheng.worker.workflow.tasks.continue_workflow[807d3c2d-1ec7-4ddc-b7fa-5bafe4f84c08] ('e3c6eca267a445cbac7acdaca25...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\request.py", line 754, in execute_using_pool
    result = apply_async(
             └ <bound method BasePool.apply_async of <celery.concurrency.solo.TaskPool object at 0x000001299D46E170>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\concurrency\base.py", line 153, in apply_async
    return self.on_apply(target, args, kwargs,
           │    │        │       │     └ {}
           │    │        │       └ ('bisheng.worker.workflow.tasks.continue_workflow', '807d3c2d-1ec7-4ddc-b7fa-5bafe4f84c08', {'lang': 'py', 'task': 'bisheng.w...
           │    │        └ <function fast_trace_task at 0x00000129BCD4D480>
           │    └ <function apply_target at 0x00000129BB653130>
           └ <celery.concurrency.solo.TaskPool object at 0x000001299D46E170>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\concurrency\base.py", line 30, in apply_target
    ret = target(*args, **kwargs)
          │       │       └ {}
          │       └ ('bisheng.worker.workflow.tasks.continue_workflow', '807d3c2d-1ec7-4ddc-b7fa-5bafe4f84c08', {'lang': 'py', 'task': 'bisheng.w...
          └ <function fast_trace_task at 0x00000129BCD4D480>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 651, in fast_trace_task
    R, I, T, Rstr = tasks[task].__trace__(
                    │     └ 'bisheng.worker.workflow.tasks.continue_workflow'
                    └ {'celery.chord': <@task: celery.chord of bisheng at 0x129d8458d90>, 'celery.accumulate': <@task: celery.accumulate of bisheng...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 │    │       └ {}
                 │    └ ['e3c6eca267a445cbac7acdaca25d430e', 'ac8b23aed25947908ac042dc5b5f1594', '', '1']
                 └ <@task: bisheng.worker.workflow.tasks.continue_workflow of bisheng at 0x129d8458d90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           │    │    │       └ {}
           │    │    └ ('e3c6eca267a445cbac7acdaca25d430e', 'ac8b23aed25947908ac042dc5b5f1594', '', '1')
           │    └ <staticmethod(<function continue_workflow at 0x0000012995164C10>)>
           └ <@task: bisheng.worker.workflow.tasks.continue_workflow of bisheng at 0x129d8458d90>

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\worker\workflow\tasks.py", line 107, in continue_workflow
    _continue_workflow(unique_id, workflow_id, chat_id, user_id)
    │                  │          │            │        └ '1'
    │                  │          │            └ ''
    │                  │          └ 'ac8b23aed25947908ac042dc5b5f1594'
    │                  └ 'e3c6eca267a445cbac7acdaca25d430e'
    └ <function _continue_workflow at 0x0000012995164B80>

> File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\worker\workflow\tasks.py", line 84, in _continue_workflow
    raise Exception('workflow object not found maybe data is expired')

Exception: workflow object not found maybe data is expired
[2025-08-22 15:16:22.320861] [WARNING process-948-27812 bisheng.worker.workflow.tasks:20] - trace=e3c6eca267a445cbac7acdaca25d430e workflow object not found for unique_id: e3c6eca267a445cbac7acdaca25d430e
[2025-08-22 15:16:36.828836] [INFO process-948-27812 bisheng.workflow.nodes.start.start:32] - trace=ea061dcec7f24784a057a523e00f4394 === StartNode self 内部信息 ===
[2025-08-22 15:16:36.830968] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=ea061dcec7f24784a057a523e00f4394 id: start_553b9
[2025-08-22 15:16:36.833495] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=ea061dcec7f24784a057a523e00f4394 type: start
[2025-08-22 15:16:36.836496] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=ea061dcec7f24784a057a523e00f4394 name: 开始
[2025-08-22 15:16:36.839417] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=ea061dcec7f24784a057a523e00f4394 description: 工作流运行的起始节点。
[2025-08-22 15:16:36.841110] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=ea061dcec7f24784a057a523e00f4394 target_edges: [EdgeBase(id='xy-edge__start_553b9right_handle-input_2775bleft_handle', source='start_553b9', sourceHandle='right_handle', sourceType='', target='input_2775b', targetHandle='left_handle', targetType='')]
[2025-08-22 15:16:36.843924] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=ea061dcec7f24784a057a523e00f4394 user_id: 1
[2025-08-22 15:16:36.845560] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=ea061dcec7f24784a057a523e00f4394 workflow_id: ac8b23aed25947908ac042dc5b5f1594
[2025-08-22 15:16:36.848365] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=ea061dcec7f24784a057a523e00f4394 graph_state: history_memory=ConversationBufferWindowMemory(chat_memory=InMemoryChatMessageHistory(messages=[]), k=10) variables_pool={}
[2025-08-22 15:16:36.850909] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=ea061dcec7f24784a057a523e00f4394 node_data: id='start_553b9' type='start' name='开始' description='工作流运行的起始节点。' group_params=[NodeGroupParams(name='开场引导', params=[NodeParams(key='guide_word', label='开场白', value='您好，请问想聊些什么呢？', type='textarea', help='', tab='', placeholder='每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。', required=False, options=None), NodeParams(key='guide_question', label='引导问题', value=['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], type='input_list', help='为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', tab='', placeholder='请输入引导问题', required=False, options=None)], description=''), NodeGroupParams(name='全局变量', params=[NodeParams(key='current_time', label='当前时间', value='', type='var', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='chat_history', label='', value=10, type='chat_history_num', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='preset_question', label='预置问题列表', value=[{'key': 'f4cbae', 'value': ''}], type='input_list', help='适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', tab='', placeholder='输入批量预置问题', required=False, options=None)], description='')] tab={} tool_key='' v=1
[2025-08-22 15:16:36.860562] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=ea061dcec7f24784a057a523e00f4394 node_params: {'guide_word': '您好，请问想聊些什么呢？', 'guide_question': ['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], 'current_time': '2025-08-22 15:16:36', 'chat_history': 10, 'preset_question': [{'key': 'f4cbae', 'value': ''}]}
[2025-08-22 15:16:36.869665] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=ea061dcec7f24784a057a523e00f4394 other_node_variable: {}
[2025-08-22 15:16:36.871668] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=ea061dcec7f24784a057a523e00f4394 current_step: 0
[2025-08-22 15:16:36.873208] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=ea061dcec7f24784a057a523e00f4394 max_steps: 50
[2025-08-22 15:16:36.875726] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=ea061dcec7f24784a057a523e00f4394 callback_manager: <bisheng.worker.workflow.redis_callback.RedisCallback object at 0x000001299D5A3DF0>
[2025-08-22 15:16:36.877727] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=ea061dcec7f24784a057a523e00f4394 tmp_collection_name: tmp_workflow_data
[2025-08-22 15:16:36.879730] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=ea061dcec7f24784a057a523e00f4394 stop_flag: False
[2025-08-22 15:16:36.882609] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=ea061dcec7f24784a057a523e00f4394 exec_unique_id: 3bc840e6f4ad41cb804352b9f2802ff5
[2025-08-22 15:16:36.885626] [INFO process-948-27812 bisheng.workflow.nodes.start.start:35] - trace=ea061dcec7f24784a057a523e00f4394 === StartNode self 信息结束 ===
[2025-08-22 15:16:40.696329] [INFO process-948-27812 bisheng.workflow.nodes.start.start:32] - trace=60d4053a8c7d4799ad95afc0adcd2255 === StartNode self 内部信息 ===
[2025-08-22 15:16:40.698783] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=60d4053a8c7d4799ad95afc0adcd2255 id: start_553b9
[2025-08-22 15:16:40.701219] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=60d4053a8c7d4799ad95afc0adcd2255 type: start
[2025-08-22 15:16:40.703739] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=60d4053a8c7d4799ad95afc0adcd2255 name: 开始
[2025-08-22 15:16:40.706123] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=60d4053a8c7d4799ad95afc0adcd2255 description: 工作流运行的起始节点。
[2025-08-22 15:16:40.707158] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=60d4053a8c7d4799ad95afc0adcd2255 target_edges: [EdgeBase(id='xy-edge__start_553b9right_handle-input_2775bleft_handle', source='start_553b9', sourceHandle='right_handle', sourceType='', target='input_2775b', targetHandle='left_handle', targetType='')]
[2025-08-22 15:16:40.709153] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=60d4053a8c7d4799ad95afc0adcd2255 user_id: 1
[2025-08-22 15:16:40.711154] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=60d4053a8c7d4799ad95afc0adcd2255 workflow_id: ac8b23aed25947908ac042dc5b5f1594
[2025-08-22 15:16:40.713713] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=60d4053a8c7d4799ad95afc0adcd2255 graph_state: history_memory=ConversationBufferWindowMemory(chat_memory=InMemoryChatMessageHistory(messages=[]), k=10) variables_pool={}
[2025-08-22 15:16:40.717156] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=60d4053a8c7d4799ad95afc0adcd2255 node_data: id='start_553b9' type='start' name='开始' description='工作流运行的起始节点。' group_params=[NodeGroupParams(name='开场引导', params=[NodeParams(key='guide_word', label='开场白', value='您好，请问想聊些什么呢？', type='textarea', help='', tab='', placeholder='每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。', required=False, options=None), NodeParams(key='guide_question', label='引导问题', value=['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], type='input_list', help='为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', tab='', placeholder='请输入引导问题', required=False, options=None)], description=''), NodeGroupParams(name='全局变量', params=[NodeParams(key='current_time', label='当前时间', value='', type='var', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='chat_history', label='', value=10, type='chat_history_num', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='preset_question', label='预置问题列表', value=[{'key': 'f4cbae', 'value': ''}], type='input_list', help='适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', tab='', placeholder='输入批量预置问题', required=False, options=None)], description='')] tab={} tool_key='' v=1
[2025-08-22 15:16:40.722499] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=60d4053a8c7d4799ad95afc0adcd2255 node_params: {'guide_word': '您好，请问想聊些什么呢？', 'guide_question': ['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], 'current_time': '2025-08-22 15:16:40', 'chat_history': 10, 'preset_question': [{'key': 'f4cbae', 'value': ''}]}
[2025-08-22 15:16:40.726523] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=60d4053a8c7d4799ad95afc0adcd2255 other_node_variable: {}
[2025-08-22 15:16:40.727523] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=60d4053a8c7d4799ad95afc0adcd2255 current_step: 0
[2025-08-22 15:16:40.730042] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=60d4053a8c7d4799ad95afc0adcd2255 max_steps: 50
[2025-08-22 15:16:40.732650] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=60d4053a8c7d4799ad95afc0adcd2255 callback_manager: <bisheng.worker.workflow.redis_callback.RedisCallback object at 0x000001299D5A3FD0>
[2025-08-22 15:16:40.734664] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=60d4053a8c7d4799ad95afc0adcd2255 tmp_collection_name: tmp_workflow_data
[2025-08-22 15:16:40.737660] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=60d4053a8c7d4799ad95afc0adcd2255 stop_flag: False
[2025-08-22 15:16:40.740665] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=60d4053a8c7d4799ad95afc0adcd2255 exec_unique_id: 035034e34aeb4e5580268ea15ec74b72
[2025-08-22 15:16:40.743174] [INFO process-948-27812 bisheng.workflow.nodes.start.start:35] - trace=60d4053a8c7d4799ad95afc0adcd2255 === StartNode self 信息结束 ===
[2025-08-22 15:17:00.880961] [INFO process-948-27812 bisheng.workflow.nodes.start.start:32] - trace=d7104b254fd84abaab298c9192bb6a39 === StartNode self 内部信息 ===
[2025-08-22 15:17:00.883533] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=d7104b254fd84abaab298c9192bb6a39 id: start_553b9
[2025-08-22 15:17:00.886581] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=d7104b254fd84abaab298c9192bb6a39 type: start
[2025-08-22 15:17:00.888540] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=d7104b254fd84abaab298c9192bb6a39 name: 开始
[2025-08-22 15:17:00.890564] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=d7104b254fd84abaab298c9192bb6a39 description: 工作流运行的起始节点。
[2025-08-22 15:17:00.893051] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=d7104b254fd84abaab298c9192bb6a39 target_edges: [EdgeBase(id='xy-edge__start_553b9right_handle-input_2775bleft_handle', source='start_553b9', sourceHandle='right_handle', sourceType='', target='input_2775b', targetHandle='left_handle', targetType='')]
[2025-08-22 15:17:00.895573] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=d7104b254fd84abaab298c9192bb6a39 user_id: 1
[2025-08-22 15:17:00.898597] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=d7104b254fd84abaab298c9192bb6a39 workflow_id: ac8b23aed25947908ac042dc5b5f1594
[2025-08-22 15:17:00.900573] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=d7104b254fd84abaab298c9192bb6a39 graph_state: history_memory=ConversationBufferWindowMemory(chat_memory=InMemoryChatMessageHistory(messages=[]), k=10) variables_pool={}
[2025-08-22 15:17:00.903953] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=d7104b254fd84abaab298c9192bb6a39 node_data: id='start_553b9' type='start' name='开始' description='工作流运行的起始节点。' group_params=[NodeGroupParams(name='开场引导', params=[NodeParams(key='guide_word', label='开场白', value='您好，请问想聊些什么呢？', type='textarea', help='', tab='', placeholder='每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。', required=False, options=None), NodeParams(key='guide_question', label='引导问题', value=['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], type='input_list', help='为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', tab='', placeholder='请输入引导问题', required=False, options=None)], description=''), NodeGroupParams(name='全局变量', params=[NodeParams(key='current_time', label='当前时间', value='', type='var', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='chat_history', label='', value=10, type='chat_history_num', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='preset_question', label='预置问题列表', value=[{'key': 'f4cbae', 'value': ''}], type='input_list', help='适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', tab='', placeholder='输入批量预置问题', required=False, options=None)], description='')] tab={} tool_key='' v=1
[2025-08-22 15:17:00.910940] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=d7104b254fd84abaab298c9192bb6a39 node_params: {'guide_word': '您好，请问想聊些什么呢？', 'guide_question': ['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], 'current_time': '2025-08-22 15:17:00', 'chat_history': 10, 'preset_question': [{'key': 'f4cbae', 'value': ''}]}
[2025-08-22 15:17:00.925579] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=d7104b254fd84abaab298c9192bb6a39 other_node_variable: {}
[2025-08-22 15:17:00.927577] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=d7104b254fd84abaab298c9192bb6a39 current_step: 0
[2025-08-22 15:17:00.930720] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=d7104b254fd84abaab298c9192bb6a39 max_steps: 50
[2025-08-22 15:17:00.933376] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=d7104b254fd84abaab298c9192bb6a39 callback_manager: <bisheng.worker.workflow.redis_callback.RedisCallback object at 0x000001299D90B550>
[2025-08-22 15:17:00.936658] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=d7104b254fd84abaab298c9192bb6a39 tmp_collection_name: tmp_workflow_data
[2025-08-22 15:17:00.938668] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=d7104b254fd84abaab298c9192bb6a39 stop_flag: False
[2025-08-22 15:17:00.940670] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=d7104b254fd84abaab298c9192bb6a39 exec_unique_id: cef5f890a2ee43d49a88f79fb20d59c2
[2025-08-22 15:17:00.944193] [INFO process-948-27812 bisheng.workflow.nodes.start.start:35] - trace=d7104b254fd84abaab298c9192bb6a39 === StartNode self 信息结束 ===
[2025-08-22 15:17:04.477587] [INFO process-948-27812 bisheng.workflow.callback.llm_callback:35] - trace=d7104b254fd84abaab298c9192bb6a39 on_llm_new_token True outkey=output
[2025-08-22 15:17:31.908017] [INFO process-948-27812 bisheng.workflow.nodes.start.start:32] - trace=0bab1cd628644567b8e407637571965b === StartNode self 内部信息 ===
[2025-08-22 15:17:31.910457] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=0bab1cd628644567b8e407637571965b id: start_553b9
[2025-08-22 15:17:31.914581] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=0bab1cd628644567b8e407637571965b type: start
[2025-08-22 15:17:31.919032] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=0bab1cd628644567b8e407637571965b name: 开始
[2025-08-22 15:17:31.921376] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=0bab1cd628644567b8e407637571965b description: 工作流运行的起始节点。
[2025-08-22 15:17:31.925822] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=0bab1cd628644567b8e407637571965b target_edges: [EdgeBase(id='xy-edge__start_553b9right_handle-input_2775bleft_handle', source='start_553b9', sourceHandle='right_handle', sourceType='', target='input_2775b', targetHandle='left_handle', targetType='')]
[2025-08-22 15:17:31.928252] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=0bab1cd628644567b8e407637571965b user_id: 1
[2025-08-22 15:17:31.932604] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=0bab1cd628644567b8e407637571965b workflow_id: ac8b23aed25947908ac042dc5b5f1594
[2025-08-22 15:17:31.936767] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=0bab1cd628644567b8e407637571965b graph_state: history_memory=ConversationBufferWindowMemory(chat_memory=InMemoryChatMessageHistory(messages=[]), k=10) variables_pool={}
[2025-08-22 15:17:31.941324] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=0bab1cd628644567b8e407637571965b node_data: id='start_553b9' type='start' name='开始' description='工作流运行的起始节点。' group_params=[NodeGroupParams(name='开场引导', params=[NodeParams(key='guide_word', label='开场白', value='您好，请问想聊些什么呢？', type='textarea', help='', tab='', placeholder='每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。', required=False, options=None), NodeParams(key='guide_question', label='引导问题', value=['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], type='input_list', help='为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', tab='', placeholder='请输入引导问题', required=False, options=None)], description=''), NodeGroupParams(name='全局变量', params=[NodeParams(key='current_time', label='当前时间', value='', type='var', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='chat_history', label='', value=10, type='chat_history_num', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='preset_question', label='预置问题列表', value=[{'key': 'f4cbae', 'value': ''}], type='input_list', help='适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', tab='', placeholder='输入批量预置问题', required=False, options=None)], description='')] tab={} tool_key='' v=1
[2025-08-22 15:17:31.953546] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=0bab1cd628644567b8e407637571965b node_params: {'guide_word': '您好，请问想聊些什么呢？', 'guide_question': ['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], 'current_time': '2025-08-22 15:17:31', 'chat_history': 10, 'preset_question': [{'key': 'f4cbae', 'value': ''}]}
[2025-08-22 15:17:31.957419] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=0bab1cd628644567b8e407637571965b other_node_variable: {}
[2025-08-22 15:17:31.959428] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=0bab1cd628644567b8e407637571965b current_step: 0
[2025-08-22 15:17:31.962429] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=0bab1cd628644567b8e407637571965b max_steps: 50
[2025-08-22 15:17:31.967696] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=0bab1cd628644567b8e407637571965b callback_manager: <bisheng.worker.workflow.redis_callback.RedisCallback object at 0x000001299E927F70>
[2025-08-22 15:17:31.972497] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=0bab1cd628644567b8e407637571965b tmp_collection_name: tmp_workflow_data
[2025-08-22 15:17:31.975914] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=0bab1cd628644567b8e407637571965b stop_flag: False
[2025-08-22 15:17:31.977910] [INFO process-948-27812 bisheng.workflow.nodes.start.start:34] - trace=0bab1cd628644567b8e407637571965b exec_unique_id: 294e4fb54a0c4ae6ac094ca2b6e6f358
[2025-08-22 15:17:31.982569] [INFO process-948-27812 bisheng.workflow.nodes.start.start:35] - trace=0bab1cd628644567b8e407637571965b === StartNode self 信息结束 ===
[2025-08-22 15:17:35.999239] [INFO process-948-27812 bisheng.workflow.callback.llm_callback:35] - trace=0bab1cd628644567b8e407637571965b on_llm_new_token True outkey=output
[2025-08-22 15:17:51.159674] [INFO process-948-27812 bisheng.worker.workflow.tasks:122] - trace=d7104b254fd84abaab298c9192bb6a39 workflow stop by user 1
[2025-08-22 15:24:23.546942] [INFO process-23824-31892 datasets.config:58] - trace=1 PyTorch version 2.8.0 available.
[2025-08-22 15:25:41.920690] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:32] - trace=88e9dfd5b1624e3d902ec71168a735c8 === StartNode self 内部信息 ===
[2025-08-22 15:25:41.924021] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=88e9dfd5b1624e3d902ec71168a735c8 id: start_553b9
[2025-08-22 15:25:41.928086] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=88e9dfd5b1624e3d902ec71168a735c8 type: start
[2025-08-22 15:25:41.931889] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=88e9dfd5b1624e3d902ec71168a735c8 name: 开始
[2025-08-22 15:25:41.936946] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=88e9dfd5b1624e3d902ec71168a735c8 description: 工作流运行的起始节点。
[2025-08-22 15:25:41.939998] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=88e9dfd5b1624e3d902ec71168a735c8 target_edges: [EdgeBase(id='xy-edge__start_553b9right_handle-input_2775bleft_handle', source='start_553b9', sourceHandle='right_handle', sourceType='', target='input_2775b', targetHandle='left_handle', targetType='')]
[2025-08-22 15:25:41.944332] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=88e9dfd5b1624e3d902ec71168a735c8 user_id: 1
[2025-08-22 15:25:41.948913] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=88e9dfd5b1624e3d902ec71168a735c8 workflow_id: ac8b23aed25947908ac042dc5b5f1594
[2025-08-22 15:25:41.953252] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=88e9dfd5b1624e3d902ec71168a735c8 graph_state: history_memory=ConversationBufferWindowMemory(chat_memory=InMemoryChatMessageHistory(messages=[]), k=10) variables_pool={}
[2025-08-22 15:25:41.958233] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=88e9dfd5b1624e3d902ec71168a735c8 node_data: id='start_553b9' type='start' name='开始' description='工作流运行的起始节点。' group_params=[NodeGroupParams(name='开场引导', params=[NodeParams(key='guide_word', label='开场白', value='您好，请问想聊些什么呢？', type='textarea', help='', tab='', placeholder='每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。', required=False, options=None), NodeParams(key='guide_question', label='引导问题', value=['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], type='input_list', help='为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', tab='', placeholder='请输入引导问题', required=False, options=None)], description=''), NodeGroupParams(name='全局变量', params=[NodeParams(key='current_time', label='当前时间', value='', type='var', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='chat_history', label='', value=10, type='chat_history_num', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='preset_question', label='预置问题列表', value=[{'key': 'f4cbae', 'value': ''}], type='input_list', help='适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', tab='', placeholder='输入批量预置问题', required=False, options=None)], description='')] tab={} tool_key='' v=1
[2025-08-22 15:25:41.973228] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=88e9dfd5b1624e3d902ec71168a735c8 node_params: {'guide_word': '您好，请问想聊些什么呢？', 'guide_question': ['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], 'current_time': '2025-08-22 15:25:41', 'chat_history': 10, 'preset_question': [{'key': 'f4cbae', 'value': ''}]}
[2025-08-22 15:25:41.978011] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=88e9dfd5b1624e3d902ec71168a735c8 other_node_variable: {}
[2025-08-22 15:25:41.982874] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=88e9dfd5b1624e3d902ec71168a735c8 current_step: 0
[2025-08-22 15:25:41.986395] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=88e9dfd5b1624e3d902ec71168a735c8 max_steps: 50
[2025-08-22 15:25:41.989414] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=88e9dfd5b1624e3d902ec71168a735c8 callback_manager: <bisheng.worker.workflow.redis_callback.RedisCallback object at 0x0000022F266DE9B0>
[2025-08-22 15:25:41.995539] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=88e9dfd5b1624e3d902ec71168a735c8 tmp_collection_name: tmp_workflow_data
[2025-08-22 15:25:42.000075] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=88e9dfd5b1624e3d902ec71168a735c8 stop_flag: False
[2025-08-22 15:25:42.003646] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=88e9dfd5b1624e3d902ec71168a735c8 exec_unique_id: 9f9898355dc34f318c4891ad1176eade
[2025-08-22 15:25:42.006603] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:35] - trace=88e9dfd5b1624e3d902ec71168a735c8 === StartNode self 信息结束 ===
[2025-08-22 15:25:46.091601] [ERROR process-23824-31892 bisheng.worker.workflow.tasks:98] - trace=7b65f30ce7df43a6a3b031d0287422cd continue_workflow error
Traceback (most recent call last):

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
           │         │     └ {'__name__': '__main__', '__doc__': 'Entry-point for the :program:`celery` umbrella command.', '__package__': 'celery', '__lo...
           │         └ <code object <module> at 0x0000022F46FBB100, file "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pa...
           └ <function _run_code at 0x0000022F46FD9CF0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': 'Entry-point for the :program:`celery` umbrella command.', '__package__': 'celery', '__lo...
         └ <code object <module> at 0x0000022F46FBB100, file "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pa...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\__main__.py", line 19, in <module>
    main()
    └ <function main at 0x0000022F46FDA710>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\__main__.py", line 15, in main
    sys.exit(_main())
    │   │    └ <function main at 0x0000022F4935C550>
    │   └ <built-in function exit>
    └ <module 'sys' (built-in)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
           └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {'auto_envvar_prefix': 'CELERY'}
           │    │     └ ()
           │    └ <function Command.main at 0x0000022F48923C70>
           └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000022F46CFFB50>
         │    └ <function Group.invoke at 0x0000022F4892C670>
         └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1830, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000022F4933B190>
           │               │       │       └ <function Command.invoke at 0x0000022F48923B50>
           │               │       └ <CeleryDaemonCommand worker>
           │               └ <click.core.Context object at 0x0000022F4933B190>
           └ <function Group.invoke.<locals>._process_result at 0x0000022F4935C670>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │   │      │    │           └ <click.core.Context object at 0x0000022F4933B190>
           │   │      │    └ <function worker at 0x0000022F4935D3F0>
           │   │      └ <CeleryDaemonCommand worker>
           │   └ <function Context.invoke at 0x0000022F48922EF0>
           └ <click.core.Context object at 0x0000022F4933B190>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │         └ ()
           └ <function worker at 0x0000022F4935D3F0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\decorators.py", line 34, in new_func
    return f(get_current_context(), *args, **kwargs)
           │ │                       │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │ │                       └ ()
           │ └ <function get_current_context at 0x0000022F4889F1C0>
           └ <function worker at 0x0000022F4935D360>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
           │ │     │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │ │     └ ()
           │ └ <click.core.Context object at 0x0000022F4933B190>
           └ <function worker at 0x0000022F4935D2D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\worker.py", line 356, in worker
    worker.start()
    │      └ <function WorkController.start at 0x0000022F49292440>
    └ <Worker: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\worker.py", line 203, in start
    self.blueprint.start(self)
    │    │         │     └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │    │         └ <function Blueprint.start at 0x0000022F4927ECB0>
    │    └ <celery.worker.worker.WorkController.Blueprint object at 0x0000022F265886D0>
    └ <Worker: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 116, in start
    step.start(parent)
    │    │     └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │    └ <function StartStopStep.start at 0x0000022F4927FD90>
    └ <step: Consumer>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 365, in start
    return self.obj.start()
           │    │   └ <function Consumer.start at 0x0000022F265E8820>
           │    └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
           └ <step: Consumer>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 341, in start
    blueprint.start(self)
    │         │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │         └ <function Blueprint.start at 0x0000022F4927ECB0>
    └ <celery.worker.consumer.consumer.Consumer.Blueprint object at 0x0000022F265C9180>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 116, in start
    step.start(parent)
    │    │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │    └ <function Evloop.start at 0x0000022F265E9990>
    └ <step: event loop>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 772, in start
    c.loop(*c.loop_args())
    │ │     │ └ <function Consumer.loop_args at 0x0000022F265E8CA0>
    │ │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │ └ <function synloop at 0x0000022F265BFF40>
    └ <Consumer: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\loops.py", line 143, in synloop
    _loop_cycle()
    └ <function synloop.<locals>._loop_cycle at 0x0000022F266F8040>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\loops.py", line 132, in _loop_cycle
    connection.drain_events(timeout=2.0)
    │          └ <function Connection.drain_events at 0x0000022F48705750>
    └ <Connection: redis://172.20.20.128:6379/2 at 0x22f2665b6d0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\connection.py", line 341, in drain_events
    return self.transport.drain_events(self.connection, **kwargs)
           │    │                      │    │             └ {'timeout': 2.0}
           │    │                      │    └ <property object at 0x0000022F486F9800>
           │    │                      └ <Connection: redis://172.20.20.128:6379/2 at 0x22f2665b6d0>
           │    └ <property object at 0x0000022F486F98F0>
           └ <Connection: redis://172.20.20.128:6379/2 at 0x22f2665b6d0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 997, in drain_events
    get(self._deliver, timeout=timeout)
    │   │    │                 └ 2.0
    │   │    └ <function Transport._deliver at 0x0000022F265B91B0>
    │   └ <kombu.transport.redis.Transport object at 0x0000022F2665BEE0>
    └ <bound method MultiChannelPoller.get of <kombu.transport.redis.MultiChannelPoller object at 0x0000022F2665A080>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 598, in get
    ret = self.handle_event(fileno, event)
          │    │            │       └ 1
          │    │            └ 7172
          │    └ <function MultiChannelPoller.handle_event at 0x0000022F265BB2E0>
          └ <kombu.transport.redis.MultiChannelPoller object at 0x0000022F2665A080>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 580, in handle_event
    return self.on_readable(fileno), self
           │    │           │        └ <kombu.transport.redis.MultiChannelPoller object at 0x0000022F2665A080>
           │    │           └ 7172
           │    └ <function MultiChannelPoller.on_readable at 0x0000022F265BB250>
           └ <kombu.transport.redis.MultiChannelPoller object at 0x0000022F2665A080>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 576, in on_readable
    chan.handlers[type]()
    │    │        └ 'BRPOP'
    │    └ {'BRPOP': <bound method Channel._brpop_read of <kombu.transport.redis.Channel object at 0x0000022F26659FC0>>, 'LISTEN': <boun...
    └ <kombu.transport.redis.Channel object at 0x0000022F26659FC0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 986, in _brpop_read
    self.connection._deliver(loads(bytes_to_str(item)), dest)
    │    │          │        │     │            │       └ 'workflow_celery'
    │    │          │        │     │            └ b'{"body": "W1siN2I2NWYzMGNlN2RmNDNhNmEzYjAzMWQwMjg3NDIyY2QiLCAiYWM4YjIzYWVkMjU5NDc5MDhhYzA0MmRjNWI1ZjE1OTQiLCAiIiwgIjEiXSwge...
    │    │          │        │     └ <function bytes_to_str at 0x0000022F47B8EF80>
    │    │          │        └ <function loads at 0x0000022F48713010>
    │    │          └ <function Transport._deliver at 0x0000022F265B91B0>
    │    └ <kombu.transport.redis.Transport object at 0x0000022F2665BEE0>
    └ <kombu.transport.redis.Channel object at 0x0000022F26659FC0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 1017, in _deliver
    callback(message)
    │        └ {'body': 'W1siN2I2NWYzMGNlN2RmNDNhNmEzYjAzMWQwMjg3NDIyY2QiLCAiYWM4YjIzYWVkMjU5NDc5MDhhYzA0MmRjNWI1ZjE1OTQiLCAiIiwgIjEiXSwge30...
    └ <function Channel.basic_consume.<locals>._callback at 0x0000022F266781F0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 639, in _callback
    return callback(message)
           │        └ <Message object at 0x22f2688a440 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': 'd9b3...
           └ <bound method Consumer._receive_callback of <Consumer: [<Queue workflow_celery -> <Exchange workflow_celery(direct) bound to ...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\messaging.py", line 668, in _receive_callback
    return on_m(message) if on_m else self.receive(decoded, message)
           │    │           │         │    │       │        └ <Message object at 0x22f2688a440 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': 'd9b3...
           │    │           │         │    │       └ None
           │    │           │         │    └ <function Consumer.receive at 0x0000022F48A82170>
           │    │           │         └ <Consumer: [<Queue workflow_celery -> <Exchange workflow_celery(direct) bound to chan:1> -> workflow_celery bound to chan:1>,...
           │    │           └ <function Consumer.create_task_handler.<locals>.on_task_received at 0x0000022F26572950>
           │    └ <Message object at 0x22f2688a440 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': 'd9b3...
           └ <function Consumer.create_task_handler.<locals>.on_task_received at 0x0000022F26572950>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 688, in on_task_received
    strategy(
    └ <function default.<locals>.task_message_handler at 0x0000022F2667A4D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\strategy.py", line 207, in task_message_handler
    handle(req)
    │      └ <Request: bisheng.worker.workflow.tasks.continue_workflow[c35b804e-a7d8-4e0d-a4b0-f1c4452b4efd] ('7b65f30ce7df43a6a3b031d0287...
    └ <bound method WorkController._process_task of <Worker: celery@WIN-S45T7NVPCSH (running)>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\worker.py", line 226, in _process_task
    req.execute_using_pool(self.pool)
    │   │                  │    └ <celery.concurrency.solo.TaskPool object at 0x0000022F265C8AF0>
    │   │                  └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │   └ <function create_request_cls.<locals>.Request.execute_using_pool at 0x0000022F2667A560>
    └ <Request: bisheng.worker.workflow.tasks.continue_workflow[c35b804e-a7d8-4e0d-a4b0-f1c4452b4efd] ('7b65f30ce7df43a6a3b031d0287...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\request.py", line 754, in execute_using_pool
    result = apply_async(
             └ <bound method BasePool.apply_async of <celery.concurrency.solo.TaskPool object at 0x0000022F265C8AF0>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\concurrency\base.py", line 153, in apply_async
    return self.on_apply(target, args, kwargs,
           │    │        │       │     └ {}
           │    │        │       └ ('bisheng.worker.workflow.tasks.continue_workflow', 'c35b804e-a7d8-4e0d-a4b0-f1c4452b4efd', {'lang': 'py', 'task': 'bisheng.w...
           │    │        └ <function fast_trace_task at 0x0000022F492FD480>
           │    └ <function apply_target at 0x0000022F47B93130>
           └ <celery.concurrency.solo.TaskPool object at 0x0000022F265C8AF0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\concurrency\base.py", line 30, in apply_target
    ret = target(*args, **kwargs)
          │       │       └ {}
          │       └ ('bisheng.worker.workflow.tasks.continue_workflow', 'c35b804e-a7d8-4e0d-a4b0-f1c4452b4efd', {'lang': 'py', 'task': 'bisheng.w...
          └ <function fast_trace_task at 0x0000022F492FD480>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 651, in fast_trace_task
    R, I, T, Rstr = tasks[task].__trace__(
                    │     └ 'bisheng.worker.workflow.tasks.continue_workflow'
                    └ {'celery.chord': <@task: celery.chord of bisheng at 0x22f64a08ca0>, 'celery.accumulate': <@task: celery.accumulate of bisheng...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 │    │       └ {}
                 │    └ ['7b65f30ce7df43a6a3b031d0287422cd', 'ac8b23aed25947908ac042dc5b5f1594', '', '1']
                 └ <@task: bisheng.worker.workflow.tasks.continue_workflow of bisheng at 0x22f64a08ca0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           │    │    │       └ {}
           │    │    └ ('7b65f30ce7df43a6a3b031d0287422cd', 'ac8b23aed25947908ac042dc5b5f1594', '', '1')
           │    └ <staticmethod(<function continue_workflow at 0x0000022F1F310C10>)>
           └ <@task: bisheng.worker.workflow.tasks.continue_workflow of bisheng at 0x22f64a08ca0>

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\worker\workflow\tasks.py", line 107, in continue_workflow
    _continue_workflow(unique_id, workflow_id, chat_id, user_id)
    │                  │          │            │        └ '1'
    │                  │          │            └ ''
    │                  │          └ 'ac8b23aed25947908ac042dc5b5f1594'
    │                  └ '7b65f30ce7df43a6a3b031d0287422cd'
    └ <function _continue_workflow at 0x0000022F1F310B80>

> File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\worker\workflow\tasks.py", line 84, in _continue_workflow
    raise Exception('workflow object not found maybe data is expired')

Exception: workflow object not found maybe data is expired
[2025-08-22 15:25:46.231752] [WARNING process-23824-31892 bisheng.worker.workflow.tasks:20] - trace=7b65f30ce7df43a6a3b031d0287422cd workflow object not found for unique_id: 7b65f30ce7df43a6a3b031d0287422cd
[2025-08-22 15:25:46.763374] [INFO process-23824-31892 bisheng.workflow.callback.llm_callback:35] - trace=88e9dfd5b1624e3d902ec71168a735c8 on_llm_new_token True outkey=output
[2025-08-22 15:26:08.953933] [ERROR process-23824-31892 bisheng.worker.workflow.tasks:98] - trace=a97f4ea3a674418099a5a2a585b42b94 continue_workflow error
Traceback (most recent call last):

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
           │         │     └ {'__name__': '__main__', '__doc__': 'Entry-point for the :program:`celery` umbrella command.', '__package__': 'celery', '__lo...
           │         └ <code object <module> at 0x0000022F46FBB100, file "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pa...
           └ <function _run_code at 0x0000022F46FD9CF0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': 'Entry-point for the :program:`celery` umbrella command.', '__package__': 'celery', '__lo...
         └ <code object <module> at 0x0000022F46FBB100, file "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pa...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\__main__.py", line 19, in <module>
    main()
    └ <function main at 0x0000022F46FDA710>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\__main__.py", line 15, in main
    sys.exit(_main())
    │   │    └ <function main at 0x0000022F4935C550>
    │   └ <built-in function exit>
    └ <module 'sys' (built-in)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
           └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {'auto_envvar_prefix': 'CELERY'}
           │    │     └ ()
           │    └ <function Command.main at 0x0000022F48923C70>
           └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000022F46CFFB50>
         │    └ <function Group.invoke at 0x0000022F4892C670>
         └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1830, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000022F4933B190>
           │               │       │       └ <function Command.invoke at 0x0000022F48923B50>
           │               │       └ <CeleryDaemonCommand worker>
           │               └ <click.core.Context object at 0x0000022F4933B190>
           └ <function Group.invoke.<locals>._process_result at 0x0000022F4935C670>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │   │      │    │           └ <click.core.Context object at 0x0000022F4933B190>
           │   │      │    └ <function worker at 0x0000022F4935D3F0>
           │   │      └ <CeleryDaemonCommand worker>
           │   └ <function Context.invoke at 0x0000022F48922EF0>
           └ <click.core.Context object at 0x0000022F4933B190>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │         └ ()
           └ <function worker at 0x0000022F4935D3F0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\decorators.py", line 34, in new_func
    return f(get_current_context(), *args, **kwargs)
           │ │                       │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │ │                       └ ()
           │ └ <function get_current_context at 0x0000022F4889F1C0>
           └ <function worker at 0x0000022F4935D360>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
           │ │     │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │ │     └ ()
           │ └ <click.core.Context object at 0x0000022F4933B190>
           └ <function worker at 0x0000022F4935D2D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\worker.py", line 356, in worker
    worker.start()
    │      └ <function WorkController.start at 0x0000022F49292440>
    └ <Worker: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\worker.py", line 203, in start
    self.blueprint.start(self)
    │    │         │     └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │    │         └ <function Blueprint.start at 0x0000022F4927ECB0>
    │    └ <celery.worker.worker.WorkController.Blueprint object at 0x0000022F265886D0>
    └ <Worker: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 116, in start
    step.start(parent)
    │    │     └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │    └ <function StartStopStep.start at 0x0000022F4927FD90>
    └ <step: Consumer>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 365, in start
    return self.obj.start()
           │    │   └ <function Consumer.start at 0x0000022F265E8820>
           │    └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
           └ <step: Consumer>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 341, in start
    blueprint.start(self)
    │         │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │         └ <function Blueprint.start at 0x0000022F4927ECB0>
    └ <celery.worker.consumer.consumer.Consumer.Blueprint object at 0x0000022F265C9180>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 116, in start
    step.start(parent)
    │    │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │    └ <function Evloop.start at 0x0000022F265E9990>
    └ <step: event loop>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 772, in start
    c.loop(*c.loop_args())
    │ │     │ └ <function Consumer.loop_args at 0x0000022F265E8CA0>
    │ │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │ └ <function synloop at 0x0000022F265BFF40>
    └ <Consumer: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\loops.py", line 143, in synloop
    _loop_cycle()
    └ <function synloop.<locals>._loop_cycle at 0x0000022F266F8040>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\loops.py", line 132, in _loop_cycle
    connection.drain_events(timeout=2.0)
    │          └ <function Connection.drain_events at 0x0000022F48705750>
    └ <Connection: redis://172.20.20.128:6379/2 at 0x22f2665b6d0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\connection.py", line 341, in drain_events
    return self.transport.drain_events(self.connection, **kwargs)
           │    │                      │    │             └ {'timeout': 2.0}
           │    │                      │    └ <property object at 0x0000022F486F9800>
           │    │                      └ <Connection: redis://172.20.20.128:6379/2 at 0x22f2665b6d0>
           │    └ <property object at 0x0000022F486F98F0>
           └ <Connection: redis://172.20.20.128:6379/2 at 0x22f2665b6d0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 997, in drain_events
    get(self._deliver, timeout=timeout)
    │   │    │                 └ 2.0
    │   │    └ <function Transport._deliver at 0x0000022F265B91B0>
    │   └ <kombu.transport.redis.Transport object at 0x0000022F2665BEE0>
    └ <bound method MultiChannelPoller.get of <kombu.transport.redis.MultiChannelPoller object at 0x0000022F2665A080>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 598, in get
    ret = self.handle_event(fileno, event)
          │    │            │       └ 1
          │    │            └ 7172
          │    └ <function MultiChannelPoller.handle_event at 0x0000022F265BB2E0>
          └ <kombu.transport.redis.MultiChannelPoller object at 0x0000022F2665A080>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 580, in handle_event
    return self.on_readable(fileno), self
           │    │           │        └ <kombu.transport.redis.MultiChannelPoller object at 0x0000022F2665A080>
           │    │           └ 7172
           │    └ <function MultiChannelPoller.on_readable at 0x0000022F265BB250>
           └ <kombu.transport.redis.MultiChannelPoller object at 0x0000022F2665A080>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 576, in on_readable
    chan.handlers[type]()
    │    │        └ 'BRPOP'
    │    └ {'BRPOP': <bound method Channel._brpop_read of <kombu.transport.redis.Channel object at 0x0000022F26659FC0>>, 'LISTEN': <boun...
    └ <kombu.transport.redis.Channel object at 0x0000022F26659FC0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 986, in _brpop_read
    self.connection._deliver(loads(bytes_to_str(item)), dest)
    │    │          │        │     │            │       └ 'workflow_celery'
    │    │          │        │     │            └ b'{"body": "W1siYTk3ZjRlYTNhNjc0NDE4MDk5YTVhMmE1ODViNDJiOTQiLCAiYWM4YjIzYWVkMjU5NDc5MDhhYzA0MmRjNWI1ZjE1OTQiLCAiIiwgIjEiXSwge...
    │    │          │        │     └ <function bytes_to_str at 0x0000022F47B8EF80>
    │    │          │        └ <function loads at 0x0000022F48713010>
    │    │          └ <function Transport._deliver at 0x0000022F265B91B0>
    │    └ <kombu.transport.redis.Transport object at 0x0000022F2665BEE0>
    └ <kombu.transport.redis.Channel object at 0x0000022F26659FC0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 1017, in _deliver
    callback(message)
    │        └ {'body': 'W1siYTk3ZjRlYTNhNjc0NDE4MDk5YTVhMmE1ODViNDJiOTQiLCAiYWM4YjIzYWVkMjU5NDc5MDhhYzA0MmRjNWI1ZjE1OTQiLCAiIiwgIjEiXSwge30...
    └ <function Channel.basic_consume.<locals>._callback at 0x0000022F266781F0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 639, in _callback
    return callback(message)
           │        └ <Message object at 0x22f2696f9a0 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': '5a62...
           └ <bound method Consumer._receive_callback of <Consumer: [<Queue workflow_celery -> <Exchange workflow_celery(direct) bound to ...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\messaging.py", line 668, in _receive_callback
    return on_m(message) if on_m else self.receive(decoded, message)
           │    │           │         │    │       │        └ <Message object at 0x22f2696f9a0 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': '5a62...
           │    │           │         │    │       └ None
           │    │           │         │    └ <function Consumer.receive at 0x0000022F48A82170>
           │    │           │         └ <Consumer: [<Queue workflow_celery -> <Exchange workflow_celery(direct) bound to chan:1> -> workflow_celery bound to chan:1>,...
           │    │           └ <function Consumer.create_task_handler.<locals>.on_task_received at 0x0000022F26572950>
           │    └ <Message object at 0x22f2696f9a0 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': '5a62...
           └ <function Consumer.create_task_handler.<locals>.on_task_received at 0x0000022F26572950>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 688, in on_task_received
    strategy(
    └ <function default.<locals>.task_message_handler at 0x0000022F2667A4D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\strategy.py", line 207, in task_message_handler
    handle(req)
    │      └ <Request: bisheng.worker.workflow.tasks.continue_workflow[61aea0a3-0913-40fc-a671-068d288155d5] ('a97f4ea3a674418099a5a2a585b...
    └ <bound method WorkController._process_task of <Worker: celery@WIN-S45T7NVPCSH (running)>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\worker.py", line 226, in _process_task
    req.execute_using_pool(self.pool)
    │   │                  │    └ <celery.concurrency.solo.TaskPool object at 0x0000022F265C8AF0>
    │   │                  └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │   └ <function create_request_cls.<locals>.Request.execute_using_pool at 0x0000022F2667A560>
    └ <Request: bisheng.worker.workflow.tasks.continue_workflow[61aea0a3-0913-40fc-a671-068d288155d5] ('a97f4ea3a674418099a5a2a585b...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\request.py", line 754, in execute_using_pool
    result = apply_async(
             └ <bound method BasePool.apply_async of <celery.concurrency.solo.TaskPool object at 0x0000022F265C8AF0>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\concurrency\base.py", line 153, in apply_async
    return self.on_apply(target, args, kwargs,
           │    │        │       │     └ {}
           │    │        │       └ ('bisheng.worker.workflow.tasks.continue_workflow', '61aea0a3-0913-40fc-a671-068d288155d5', {'lang': 'py', 'task': 'bisheng.w...
           │    │        └ <function fast_trace_task at 0x0000022F492FD480>
           │    └ <function apply_target at 0x0000022F47B93130>
           └ <celery.concurrency.solo.TaskPool object at 0x0000022F265C8AF0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\concurrency\base.py", line 30, in apply_target
    ret = target(*args, **kwargs)
          │       │       └ {}
          │       └ ('bisheng.worker.workflow.tasks.continue_workflow', '61aea0a3-0913-40fc-a671-068d288155d5', {'lang': 'py', 'task': 'bisheng.w...
          └ <function fast_trace_task at 0x0000022F492FD480>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 651, in fast_trace_task
    R, I, T, Rstr = tasks[task].__trace__(
                    │     └ 'bisheng.worker.workflow.tasks.continue_workflow'
                    └ {'celery.chord': <@task: celery.chord of bisheng at 0x22f64a08ca0>, 'celery.accumulate': <@task: celery.accumulate of bisheng...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 │    │       └ {}
                 │    └ ['a97f4ea3a674418099a5a2a585b42b94', 'ac8b23aed25947908ac042dc5b5f1594', '', '1']
                 └ <@task: bisheng.worker.workflow.tasks.continue_workflow of bisheng at 0x22f64a08ca0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           │    │    │       └ {}
           │    │    └ ('a97f4ea3a674418099a5a2a585b42b94', 'ac8b23aed25947908ac042dc5b5f1594', '', '1')
           │    └ <staticmethod(<function continue_workflow at 0x0000022F1F310C10>)>
           └ <@task: bisheng.worker.workflow.tasks.continue_workflow of bisheng at 0x22f64a08ca0>

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\worker\workflow\tasks.py", line 107, in continue_workflow
    _continue_workflow(unique_id, workflow_id, chat_id, user_id)
    │                  │          │            │        └ '1'
    │                  │          │            └ ''
    │                  │          └ 'ac8b23aed25947908ac042dc5b5f1594'
    │                  └ 'a97f4ea3a674418099a5a2a585b42b94'
    └ <function _continue_workflow at 0x0000022F1F310B80>

> File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\worker\workflow\tasks.py", line 84, in _continue_workflow
    raise Exception('workflow object not found maybe data is expired')

Exception: workflow object not found maybe data is expired
[2025-08-22 15:26:09.060705] [WARNING process-23824-31892 bisheng.worker.workflow.tasks:20] - trace=a97f4ea3a674418099a5a2a585b42b94 workflow object not found for unique_id: a97f4ea3a674418099a5a2a585b42b94
[2025-08-22 15:27:28.412650] [ERROR process-23824-31892 bisheng.worker.workflow.tasks:98] - trace=a98aa48b5a0e483fa3e6a55be58c0f3a continue_workflow error
Traceback (most recent call last):

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
           │         │     └ {'__name__': '__main__', '__doc__': 'Entry-point for the :program:`celery` umbrella command.', '__package__': 'celery', '__lo...
           │         └ <code object <module> at 0x0000022F46FBB100, file "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pa...
           └ <function _run_code at 0x0000022F46FD9CF0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': 'Entry-point for the :program:`celery` umbrella command.', '__package__': 'celery', '__lo...
         └ <code object <module> at 0x0000022F46FBB100, file "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pa...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\__main__.py", line 19, in <module>
    main()
    └ <function main at 0x0000022F46FDA710>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\__main__.py", line 15, in main
    sys.exit(_main())
    │   │    └ <function main at 0x0000022F4935C550>
    │   └ <built-in function exit>
    └ <module 'sys' (built-in)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
           └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {'auto_envvar_prefix': 'CELERY'}
           │    │     └ ()
           │    └ <function Command.main at 0x0000022F48923C70>
           └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000022F46CFFB50>
         │    └ <function Group.invoke at 0x0000022F4892C670>
         └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1830, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000022F4933B190>
           │               │       │       └ <function Command.invoke at 0x0000022F48923B50>
           │               │       └ <CeleryDaemonCommand worker>
           │               └ <click.core.Context object at 0x0000022F4933B190>
           └ <function Group.invoke.<locals>._process_result at 0x0000022F4935C670>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │   │      │    │           └ <click.core.Context object at 0x0000022F4933B190>
           │   │      │    └ <function worker at 0x0000022F4935D3F0>
           │   │      └ <CeleryDaemonCommand worker>
           │   └ <function Context.invoke at 0x0000022F48922EF0>
           └ <click.core.Context object at 0x0000022F4933B190>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │         └ ()
           └ <function worker at 0x0000022F4935D3F0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\decorators.py", line 34, in new_func
    return f(get_current_context(), *args, **kwargs)
           │ │                       │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │ │                       └ ()
           │ └ <function get_current_context at 0x0000022F4889F1C0>
           └ <function worker at 0x0000022F4935D360>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
           │ │     │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │ │     └ ()
           │ └ <click.core.Context object at 0x0000022F4933B190>
           └ <function worker at 0x0000022F4935D2D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\worker.py", line 356, in worker
    worker.start()
    │      └ <function WorkController.start at 0x0000022F49292440>
    └ <Worker: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\worker.py", line 203, in start
    self.blueprint.start(self)
    │    │         │     └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │    │         └ <function Blueprint.start at 0x0000022F4927ECB0>
    │    └ <celery.worker.worker.WorkController.Blueprint object at 0x0000022F265886D0>
    └ <Worker: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 116, in start
    step.start(parent)
    │    │     └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │    └ <function StartStopStep.start at 0x0000022F4927FD90>
    └ <step: Consumer>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 365, in start
    return self.obj.start()
           │    │   └ <function Consumer.start at 0x0000022F265E8820>
           │    └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
           └ <step: Consumer>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 341, in start
    blueprint.start(self)
    │         │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │         └ <function Blueprint.start at 0x0000022F4927ECB0>
    └ <celery.worker.consumer.consumer.Consumer.Blueprint object at 0x0000022F265C9180>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 116, in start
    step.start(parent)
    │    │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │    └ <function Evloop.start at 0x0000022F265E9990>
    └ <step: event loop>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 772, in start
    c.loop(*c.loop_args())
    │ │     │ └ <function Consumer.loop_args at 0x0000022F265E8CA0>
    │ │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │ └ <function synloop at 0x0000022F265BFF40>
    └ <Consumer: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\loops.py", line 143, in synloop
    _loop_cycle()
    └ <function synloop.<locals>._loop_cycle at 0x0000022F266F8040>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\loops.py", line 132, in _loop_cycle
    connection.drain_events(timeout=2.0)
    │          └ <function Connection.drain_events at 0x0000022F48705750>
    └ <Connection: redis://172.20.20.128:6379/2 at 0x22f2665b6d0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\connection.py", line 341, in drain_events
    return self.transport.drain_events(self.connection, **kwargs)
           │    │                      │    │             └ {'timeout': 2.0}
           │    │                      │    └ <property object at 0x0000022F486F9800>
           │    │                      └ <Connection: redis://172.20.20.128:6379/2 at 0x22f2665b6d0>
           │    └ <property object at 0x0000022F486F98F0>
           └ <Connection: redis://172.20.20.128:6379/2 at 0x22f2665b6d0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 997, in drain_events
    get(self._deliver, timeout=timeout)
    │   │    │                 └ 2.0
    │   │    └ <function Transport._deliver at 0x0000022F265B91B0>
    │   └ <kombu.transport.redis.Transport object at 0x0000022F2665BEE0>
    └ <bound method MultiChannelPoller.get of <kombu.transport.redis.MultiChannelPoller object at 0x0000022F2665A080>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 598, in get
    ret = self.handle_event(fileno, event)
          │    │            │       └ 1
          │    │            └ 7172
          │    └ <function MultiChannelPoller.handle_event at 0x0000022F265BB2E0>
          └ <kombu.transport.redis.MultiChannelPoller object at 0x0000022F2665A080>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 580, in handle_event
    return self.on_readable(fileno), self
           │    │           │        └ <kombu.transport.redis.MultiChannelPoller object at 0x0000022F2665A080>
           │    │           └ 7172
           │    └ <function MultiChannelPoller.on_readable at 0x0000022F265BB250>
           └ <kombu.transport.redis.MultiChannelPoller object at 0x0000022F2665A080>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 576, in on_readable
    chan.handlers[type]()
    │    │        └ 'BRPOP'
    │    └ {'BRPOP': <bound method Channel._brpop_read of <kombu.transport.redis.Channel object at 0x0000022F26659FC0>>, 'LISTEN': <boun...
    └ <kombu.transport.redis.Channel object at 0x0000022F26659FC0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 986, in _brpop_read
    self.connection._deliver(loads(bytes_to_str(item)), dest)
    │    │          │        │     │            │       └ 'workflow_celery'
    │    │          │        │     │            └ b'{"body": "W1siYTk4YWE0OGI1YTBlNDgzZmEzZTZhNTViZTU4YzBmM2EiLCAiYWM4YjIzYWVkMjU5NDc5MDhhYzA0MmRjNWI1ZjE1OTQiLCAiIiwgIjEiXSwge...
    │    │          │        │     └ <function bytes_to_str at 0x0000022F47B8EF80>
    │    │          │        └ <function loads at 0x0000022F48713010>
    │    │          └ <function Transport._deliver at 0x0000022F265B91B0>
    │    └ <kombu.transport.redis.Transport object at 0x0000022F2665BEE0>
    └ <kombu.transport.redis.Channel object at 0x0000022F26659FC0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 1017, in _deliver
    callback(message)
    │        └ {'body': 'W1siYTk4YWE0OGI1YTBlNDgzZmEzZTZhNTViZTU4YzBmM2EiLCAiYWM4YjIzYWVkMjU5NDc5MDhhYzA0MmRjNWI1ZjE1OTQiLCAiIiwgIjEiXSwge30...
    └ <function Channel.basic_consume.<locals>._callback at 0x0000022F266781F0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 639, in _callback
    return callback(message)
           │        └ <Message object at 0x22f2688b010 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': '423b...
           └ <bound method Consumer._receive_callback of <Consumer: [<Queue workflow_celery -> <Exchange workflow_celery(direct) bound to ...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\messaging.py", line 668, in _receive_callback
    return on_m(message) if on_m else self.receive(decoded, message)
           │    │           │         │    │       │        └ <Message object at 0x22f2688b010 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': '423b...
           │    │           │         │    │       └ None
           │    │           │         │    └ <function Consumer.receive at 0x0000022F48A82170>
           │    │           │         └ <Consumer: [<Queue workflow_celery -> <Exchange workflow_celery(direct) bound to chan:1> -> workflow_celery bound to chan:1>,...
           │    │           └ <function Consumer.create_task_handler.<locals>.on_task_received at 0x0000022F26572950>
           │    └ <Message object at 0x22f2688b010 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': '423b...
           └ <function Consumer.create_task_handler.<locals>.on_task_received at 0x0000022F26572950>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 688, in on_task_received
    strategy(
    └ <function default.<locals>.task_message_handler at 0x0000022F2667A4D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\strategy.py", line 207, in task_message_handler
    handle(req)
    │      └ <Request: bisheng.worker.workflow.tasks.continue_workflow[9d5fbfa2-ea68-4845-8d98-e3870af6d04b] ('a98aa48b5a0e483fa3e6a55be58...
    └ <bound method WorkController._process_task of <Worker: celery@WIN-S45T7NVPCSH (running)>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\worker.py", line 226, in _process_task
    req.execute_using_pool(self.pool)
    │   │                  │    └ <celery.concurrency.solo.TaskPool object at 0x0000022F265C8AF0>
    │   │                  └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │   └ <function create_request_cls.<locals>.Request.execute_using_pool at 0x0000022F2667A560>
    └ <Request: bisheng.worker.workflow.tasks.continue_workflow[9d5fbfa2-ea68-4845-8d98-e3870af6d04b] ('a98aa48b5a0e483fa3e6a55be58...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\request.py", line 754, in execute_using_pool
    result = apply_async(
             └ <bound method BasePool.apply_async of <celery.concurrency.solo.TaskPool object at 0x0000022F265C8AF0>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\concurrency\base.py", line 153, in apply_async
    return self.on_apply(target, args, kwargs,
           │    │        │       │     └ {}
           │    │        │       └ ('bisheng.worker.workflow.tasks.continue_workflow', '9d5fbfa2-ea68-4845-8d98-e3870af6d04b', {'lang': 'py', 'task': 'bisheng.w...
           │    │        └ <function fast_trace_task at 0x0000022F492FD480>
           │    └ <function apply_target at 0x0000022F47B93130>
           └ <celery.concurrency.solo.TaskPool object at 0x0000022F265C8AF0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\concurrency\base.py", line 30, in apply_target
    ret = target(*args, **kwargs)
          │       │       └ {}
          │       └ ('bisheng.worker.workflow.tasks.continue_workflow', '9d5fbfa2-ea68-4845-8d98-e3870af6d04b', {'lang': 'py', 'task': 'bisheng.w...
          └ <function fast_trace_task at 0x0000022F492FD480>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 651, in fast_trace_task
    R, I, T, Rstr = tasks[task].__trace__(
                    │     └ 'bisheng.worker.workflow.tasks.continue_workflow'
                    └ {'celery.chord': <@task: celery.chord of bisheng at 0x22f64a08ca0>, 'celery.accumulate': <@task: celery.accumulate of bisheng...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 │    │       └ {}
                 │    └ ['a98aa48b5a0e483fa3e6a55be58c0f3a', 'ac8b23aed25947908ac042dc5b5f1594', '', '1']
                 └ <@task: bisheng.worker.workflow.tasks.continue_workflow of bisheng at 0x22f64a08ca0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           │    │    │       └ {}
           │    │    └ ('a98aa48b5a0e483fa3e6a55be58c0f3a', 'ac8b23aed25947908ac042dc5b5f1594', '', '1')
           │    └ <staticmethod(<function continue_workflow at 0x0000022F1F310C10>)>
           └ <@task: bisheng.worker.workflow.tasks.continue_workflow of bisheng at 0x22f64a08ca0>

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\worker\workflow\tasks.py", line 107, in continue_workflow
    _continue_workflow(unique_id, workflow_id, chat_id, user_id)
    │                  │          │            │        └ '1'
    │                  │          │            └ ''
    │                  │          └ 'ac8b23aed25947908ac042dc5b5f1594'
    │                  └ 'a98aa48b5a0e483fa3e6a55be58c0f3a'
    └ <function _continue_workflow at 0x0000022F1F310B80>

> File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\worker\workflow\tasks.py", line 84, in _continue_workflow
    raise Exception('workflow object not found maybe data is expired')

Exception: workflow object not found maybe data is expired
[2025-08-22 15:27:29.968658] [WARNING process-23824-31892 bisheng.worker.workflow.tasks:20] - trace=a98aa48b5a0e483fa3e6a55be58c0f3a workflow object not found for unique_id: a98aa48b5a0e483fa3e6a55be58c0f3a
[2025-08-22 15:27:48.852828] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:32] - trace=0f5c26b7aa5f4c38831f25c7385a0f52 === StartNode self 内部信息 ===
[2025-08-22 15:27:48.856812] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=0f5c26b7aa5f4c38831f25c7385a0f52 id: start_553b9
[2025-08-22 15:27:48.859865] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=0f5c26b7aa5f4c38831f25c7385a0f52 type: start
[2025-08-22 15:27:48.863483] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=0f5c26b7aa5f4c38831f25c7385a0f52 name: 开始
[2025-08-22 15:27:48.866481] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=0f5c26b7aa5f4c38831f25c7385a0f52 description: 工作流运行的起始节点。
[2025-08-22 15:27:48.868497] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=0f5c26b7aa5f4c38831f25c7385a0f52 target_edges: [EdgeBase(id='xy-edge__start_553b9right_handle-input_2775bleft_handle', source='start_553b9', sourceHandle='right_handle', sourceType='', target='input_2775b', targetHandle='left_handle', targetType='')]
[2025-08-22 15:27:48.872381] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=0f5c26b7aa5f4c38831f25c7385a0f52 user_id: 1
[2025-08-22 15:27:48.875398] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=0f5c26b7aa5f4c38831f25c7385a0f52 workflow_id: ac8b23aed25947908ac042dc5b5f1594
[2025-08-22 15:27:48.878615] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=0f5c26b7aa5f4c38831f25c7385a0f52 graph_state: history_memory=ConversationBufferWindowMemory(chat_memory=InMemoryChatMessageHistory(messages=[]), k=10) variables_pool={}
[2025-08-22 15:27:48.881132] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=0f5c26b7aa5f4c38831f25c7385a0f52 node_data: id='start_553b9' type='start' name='开始' description='工作流运行的起始节点。' group_params=[NodeGroupParams(name='开场引导', params=[NodeParams(key='guide_word', label='开场白', value='您好，请问想聊些什么呢？', type='textarea', help='', tab='', placeholder='每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。', required=False, options=None), NodeParams(key='guide_question', label='引导问题', value=['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], type='input_list', help='为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', tab='', placeholder='请输入引导问题', required=False, options=None)], description=''), NodeGroupParams(name='全局变量', params=[NodeParams(key='current_time', label='当前时间', value='', type='var', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='chat_history', label='', value=10, type='chat_history_num', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='preset_question', label='预置问题列表', value=[{'key': 'f4cbae', 'value': ''}], type='input_list', help='适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', tab='', placeholder='输入批量预置问题', required=False, options=None)], description='')] tab={} tool_key='' v=1
[2025-08-22 15:27:48.891493] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=0f5c26b7aa5f4c38831f25c7385a0f52 node_params: {'guide_word': '您好，请问想聊些什么呢？', 'guide_question': ['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], 'current_time': '2025-08-22 15:27:48', 'chat_history': 10, 'preset_question': [{'key': 'f4cbae', 'value': ''}]}
[2025-08-22 15:27:48.895458] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=0f5c26b7aa5f4c38831f25c7385a0f52 other_node_variable: {}
[2025-08-22 15:27:48.899460] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=0f5c26b7aa5f4c38831f25c7385a0f52 current_step: 0
[2025-08-22 15:27:48.902530] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=0f5c26b7aa5f4c38831f25c7385a0f52 max_steps: 50
[2025-08-22 15:27:48.905549] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=0f5c26b7aa5f4c38831f25c7385a0f52 callback_manager: <bisheng.worker.workflow.redis_callback.RedisCallback object at 0x0000022F2690E2F0>
[2025-08-22 15:27:48.908545] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=0f5c26b7aa5f4c38831f25c7385a0f52 tmp_collection_name: tmp_workflow_data
[2025-08-22 15:27:48.910539] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=0f5c26b7aa5f4c38831f25c7385a0f52 stop_flag: False
[2025-08-22 15:27:48.914677] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=0f5c26b7aa5f4c38831f25c7385a0f52 exec_unique_id: 81de5e30f9a34ad9ba7422bc79ce6e4d
[2025-08-22 15:27:48.917675] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:35] - trace=0f5c26b7aa5f4c38831f25c7385a0f52 === StartNode self 信息结束 ===
[2025-08-22 15:27:51.040854] [INFO process-23824-31892 bisheng.workflow.callback.llm_callback:35] - trace=0f5c26b7aa5f4c38831f25c7385a0f52 on_llm_new_token True outkey=output
[2025-08-22 15:34:40.346266] [INFO process-23824-31892 bisheng.worker.workflow.tasks:122] - trace=0f5c26b7aa5f4c38831f25c7385a0f52 workflow stop by user 1
[2025-08-22 15:35:50.641533] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:32] - trace=4f6b88bb265449518824769cdbfe1678 === StartNode self 内部信息 ===
[2025-08-22 15:35:50.645284] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=4f6b88bb265449518824769cdbfe1678 id: start_553b9
[2025-08-22 15:35:50.649029] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=4f6b88bb265449518824769cdbfe1678 type: start
[2025-08-22 15:35:50.652546] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=4f6b88bb265449518824769cdbfe1678 name: 开始
[2025-08-22 15:35:50.654559] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=4f6b88bb265449518824769cdbfe1678 description: 工作流运行的起始节点。
[2025-08-22 15:35:50.657592] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=4f6b88bb265449518824769cdbfe1678 target_edges: [EdgeBase(id='xy-edge__start_553b9right_handle-input_2775bleft_handle', source='start_553b9', sourceHandle='right_handle', sourceType='', target='input_2775b', targetHandle='left_handle', targetType='')]
[2025-08-22 15:35:50.662628] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=4f6b88bb265449518824769cdbfe1678 user_id: 1
[2025-08-22 15:35:50.666624] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=4f6b88bb265449518824769cdbfe1678 workflow_id: ac8b23aed25947908ac042dc5b5f1594
[2025-08-22 15:35:50.669623] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=4f6b88bb265449518824769cdbfe1678 graph_state: history_memory=ConversationBufferWindowMemory(chat_memory=InMemoryChatMessageHistory(messages=[]), k=10) variables_pool={}
[2025-08-22 15:35:50.671843] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=4f6b88bb265449518824769cdbfe1678 node_data: id='start_553b9' type='start' name='开始' description='工作流运行的起始节点。' group_params=[NodeGroupParams(name='开场引导', params=[NodeParams(key='guide_word', label='开场白', value='您好，请问想聊些什么呢？', type='textarea', help='', tab='', placeholder='每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。', required=False, options=None), NodeParams(key='guide_question', label='引导问题', value=['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], type='input_list', help='为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', tab='', placeholder='请输入引导问题', required=False, options=None)], description=''), NodeGroupParams(name='全局变量', params=[NodeParams(key='current_time', label='当前时间', value='', type='var', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='chat_history', label='', value=10, type='chat_history_num', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='preset_question', label='预置问题列表', value=[{'key': 'f4cbae', 'value': ''}], type='input_list', help='适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', tab='', placeholder='输入批量预置问题', required=False, options=None)], description='')] tab={} tool_key='' v=1
[2025-08-22 15:35:50.680476] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=4f6b88bb265449518824769cdbfe1678 node_params: {'guide_word': '您好，请问想聊些什么呢？', 'guide_question': ['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], 'current_time': '2025-08-22 15:35:50', 'chat_history': 10, 'preset_question': [{'key': 'f4cbae', 'value': ''}]}
[2025-08-22 15:35:50.685546] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=4f6b88bb265449518824769cdbfe1678 other_node_variable: {}
[2025-08-22 15:35:50.688756] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=4f6b88bb265449518824769cdbfe1678 current_step: 0
[2025-08-22 15:35:50.690338] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=4f6b88bb265449518824769cdbfe1678 max_steps: 50
[2025-08-22 15:35:50.693517] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=4f6b88bb265449518824769cdbfe1678 callback_manager: <bisheng.worker.workflow.redis_callback.RedisCallback object at 0x0000022F26C71AB0>
[2025-08-22 15:35:50.697763] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=4f6b88bb265449518824769cdbfe1678 tmp_collection_name: tmp_workflow_data
[2025-08-22 15:35:50.700469] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=4f6b88bb265449518824769cdbfe1678 stop_flag: False
[2025-08-22 15:35:50.703576] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:34] - trace=4f6b88bb265449518824769cdbfe1678 exec_unique_id: a55ec7aaaad44285914e03c2d03e7db7
[2025-08-22 15:35:50.705602] [INFO process-23824-31892 bisheng.workflow.nodes.start.start:35] - trace=4f6b88bb265449518824769cdbfe1678 === StartNode self 信息结束 ===
[2025-08-22 15:35:53.333307] [INFO process-23824-31892 bisheng.workflow.callback.llm_callback:35] - trace=4f6b88bb265449518824769cdbfe1678 on_llm_new_token True outkey=output
[2025-08-22 15:36:02.360058] [INFO process-23824-31892 bisheng.workflow.callback.llm_callback:35] - trace=4f6b88bb265449518824769cdbfe1678 on_llm_new_token True outkey=output
[2025-08-22 15:36:31.463355] [ERROR process-23824-31892 bisheng.worker.workflow.tasks:98] - trace=4527f34d6f5e413ba152ea31b1fdf40e continue_workflow error
Traceback (most recent call last):

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
           │         │     └ {'__name__': '__main__', '__doc__': 'Entry-point for the :program:`celery` umbrella command.', '__package__': 'celery', '__lo...
           │         └ <code object <module> at 0x0000022F46FBB100, file "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pa...
           └ <function _run_code at 0x0000022F46FD9CF0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': 'Entry-point for the :program:`celery` umbrella command.', '__package__': 'celery', '__lo...
         └ <code object <module> at 0x0000022F46FBB100, file "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pa...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\__main__.py", line 19, in <module>
    main()
    └ <function main at 0x0000022F46FDA710>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\__main__.py", line 15, in main
    sys.exit(_main())
    │   │    └ <function main at 0x0000022F4935C550>
    │   └ <built-in function exit>
    └ <module 'sys' (built-in)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
           └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {'auto_envvar_prefix': 'CELERY'}
           │    │     └ ()
           │    └ <function Command.main at 0x0000022F48923C70>
           └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000022F46CFFB50>
         │    └ <function Group.invoke at 0x0000022F4892C670>
         └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1830, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000022F4933B190>
           │               │       │       └ <function Command.invoke at 0x0000022F48923B50>
           │               │       └ <CeleryDaemonCommand worker>
           │               └ <click.core.Context object at 0x0000022F4933B190>
           └ <function Group.invoke.<locals>._process_result at 0x0000022F4935C670>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │   │      │    │           └ <click.core.Context object at 0x0000022F4933B190>
           │   │      │    └ <function worker at 0x0000022F4935D3F0>
           │   │      └ <CeleryDaemonCommand worker>
           │   └ <function Context.invoke at 0x0000022F48922EF0>
           └ <click.core.Context object at 0x0000022F4933B190>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │         └ ()
           └ <function worker at 0x0000022F4935D3F0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\decorators.py", line 34, in new_func
    return f(get_current_context(), *args, **kwargs)
           │ │                       │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │ │                       └ ()
           │ └ <function get_current_context at 0x0000022F4889F1C0>
           └ <function worker at 0x0000022F4935D360>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
           │ │     │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │ │     └ ()
           │ └ <click.core.Context object at 0x0000022F4933B190>
           └ <function worker at 0x0000022F4935D2D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\worker.py", line 356, in worker
    worker.start()
    │      └ <function WorkController.start at 0x0000022F49292440>
    └ <Worker: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\worker.py", line 203, in start
    self.blueprint.start(self)
    │    │         │     └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │    │         └ <function Blueprint.start at 0x0000022F4927ECB0>
    │    └ <celery.worker.worker.WorkController.Blueprint object at 0x0000022F265886D0>
    └ <Worker: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 116, in start
    step.start(parent)
    │    │     └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │    └ <function StartStopStep.start at 0x0000022F4927FD90>
    └ <step: Consumer>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 365, in start
    return self.obj.start()
           │    │   └ <function Consumer.start at 0x0000022F265E8820>
           │    └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
           └ <step: Consumer>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 341, in start
    blueprint.start(self)
    │         │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │         └ <function Blueprint.start at 0x0000022F4927ECB0>
    └ <celery.worker.consumer.consumer.Consumer.Blueprint object at 0x0000022F265C9180>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 116, in start
    step.start(parent)
    │    │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │    └ <function Evloop.start at 0x0000022F265E9990>
    └ <step: event loop>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 772, in start
    c.loop(*c.loop_args())
    │ │     │ └ <function Consumer.loop_args at 0x0000022F265E8CA0>
    │ │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │ └ <function synloop at 0x0000022F265BFF40>
    └ <Consumer: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\loops.py", line 143, in synloop
    _loop_cycle()
    └ <function synloop.<locals>._loop_cycle at 0x0000022F26C6D120>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\loops.py", line 132, in _loop_cycle
    connection.drain_events(timeout=2.0)
    │          └ <function Connection.drain_events at 0x0000022F48705750>
    └ <Connection: redis://172.20.20.128:6379/2 at 0x22f266906a0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\connection.py", line 341, in drain_events
    return self.transport.drain_events(self.connection, **kwargs)
           │    │                      │    │             └ {'timeout': 2.0}
           │    │                      │    └ <property object at 0x0000022F486F9800>
           │    │                      └ <Connection: redis://172.20.20.128:6379/2 at 0x22f266906a0>
           │    └ <property object at 0x0000022F486F98F0>
           └ <Connection: redis://172.20.20.128:6379/2 at 0x22f266906a0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 997, in drain_events
    get(self._deliver, timeout=timeout)
    │   │    │                 └ 2.0
    │   │    └ <function Transport._deliver at 0x0000022F265B91B0>
    │   └ <kombu.transport.redis.Transport object at 0x0000022F266D7CA0>
    └ <bound method MultiChannelPoller.get of <kombu.transport.redis.MultiChannelPoller object at 0x0000022F266DD8D0>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 598, in get
    ret = self.handle_event(fileno, event)
          │    │            │       └ 1
          │    │            └ 8204
          │    └ <function MultiChannelPoller.handle_event at 0x0000022F265BB2E0>
          └ <kombu.transport.redis.MultiChannelPoller object at 0x0000022F266DD8D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 580, in handle_event
    return self.on_readable(fileno), self
           │    │           │        └ <kombu.transport.redis.MultiChannelPoller object at 0x0000022F266DD8D0>
           │    │           └ 8204
           │    └ <function MultiChannelPoller.on_readable at 0x0000022F265BB250>
           └ <kombu.transport.redis.MultiChannelPoller object at 0x0000022F266DD8D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 576, in on_readable
    chan.handlers[type]()
    │    │        └ 'BRPOP'
    │    └ {'BRPOP': <bound method Channel._brpop_read of <kombu.transport.redis.Channel object at 0x0000022F266DD750>>, 'LISTEN': <boun...
    └ <kombu.transport.redis.Channel object at 0x0000022F266DD750>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 986, in _brpop_read
    self.connection._deliver(loads(bytes_to_str(item)), dest)
    │    │          │        │     │            │       └ 'workflow_celery'
    │    │          │        │     │            └ b'{"body": "W1siNDUyN2YzNGQ2ZjVlNDEzYmExNTJlYTMxYjFmZGY0MGUiLCAiYWM4YjIzYWVkMjU5NDc5MDhhYzA0MmRjNWI1ZjE1OTQiLCAiIiwgIjEiXSwge...
    │    │          │        │     └ <function bytes_to_str at 0x0000022F47B8EF80>
    │    │          │        └ <function loads at 0x0000022F48713010>
    │    │          └ <function Transport._deliver at 0x0000022F265B91B0>
    │    └ <kombu.transport.redis.Transport object at 0x0000022F266D7CA0>
    └ <kombu.transport.redis.Channel object at 0x0000022F266DD750>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 1017, in _deliver
    callback(message)
    │        └ {'body': 'W1siNDUyN2YzNGQ2ZjVlNDEzYmExNTJlYTMxYjFmZGY0MGUiLCAiYWM4YjIzYWVkMjU5NDc5MDhhYzA0MmRjNWI1ZjE1OTQiLCAiIiwgIjEiXSwge30...
    └ <function Channel.basic_consume.<locals>._callback at 0x0000022F26C6CF70>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 639, in _callback
    return callback(message)
           │        └ <Message object at 0x22f2696ef80 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': 'ca23...
           └ <bound method Consumer._receive_callback of <Consumer: [<Queue workflow_celery -> <Exchange workflow_celery(direct) bound to ...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\messaging.py", line 668, in _receive_callback
    return on_m(message) if on_m else self.receive(decoded, message)
           │    │           │         │    │       │        └ <Message object at 0x22f2696ef80 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': 'ca23...
           │    │           │         │    │       └ None
           │    │           │         │    └ <function Consumer.receive at 0x0000022F48A82170>
           │    │           │         └ <Consumer: [<Queue workflow_celery -> <Exchange workflow_celery(direct) bound to chan:1> -> workflow_celery bound to chan:1>,...
           │    │           └ <function Consumer.create_task_handler.<locals>.on_task_received at 0x0000022F26C6CEE0>
           │    └ <Message object at 0x22f2696ef80 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': 'ca23...
           └ <function Consumer.create_task_handler.<locals>.on_task_received at 0x0000022F26C6CEE0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 688, in on_task_received
    strategy(
    └ <function default.<locals>.task_message_handler at 0x0000022F2667A440>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\strategy.py", line 207, in task_message_handler
    handle(req)
    │      └ <Request: bisheng.worker.workflow.tasks.continue_workflow[713553ec-08e3-4922-8d1c-19fa6cc18bcb] ('4527f34d6f5e413ba152ea31b1f...
    └ <bound method WorkController._process_task of <Worker: celery@WIN-S45T7NVPCSH (running)>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\worker.py", line 226, in _process_task
    req.execute_using_pool(self.pool)
    │   │                  │    └ <celery.concurrency.solo.TaskPool object at 0x0000022F265C8AF0>
    │   │                  └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │   └ <function create_request_cls.<locals>.Request.execute_using_pool at 0x0000022F2667A3B0>
    └ <Request: bisheng.worker.workflow.tasks.continue_workflow[713553ec-08e3-4922-8d1c-19fa6cc18bcb] ('4527f34d6f5e413ba152ea31b1f...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\request.py", line 754, in execute_using_pool
    result = apply_async(
             └ <bound method BasePool.apply_async of <celery.concurrency.solo.TaskPool object at 0x0000022F265C8AF0>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\concurrency\base.py", line 153, in apply_async
    return self.on_apply(target, args, kwargs,
           │    │        │       │     └ {}
           │    │        │       └ ('bisheng.worker.workflow.tasks.continue_workflow', '713553ec-08e3-4922-8d1c-19fa6cc18bcb', {'lang': 'py', 'task': 'bisheng.w...
           │    │        └ <function fast_trace_task at 0x0000022F492FD480>
           │    └ <function apply_target at 0x0000022F47B93130>
           └ <celery.concurrency.solo.TaskPool object at 0x0000022F265C8AF0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\concurrency\base.py", line 30, in apply_target
    ret = target(*args, **kwargs)
          │       │       └ {}
          │       └ ('bisheng.worker.workflow.tasks.continue_workflow', '713553ec-08e3-4922-8d1c-19fa6cc18bcb', {'lang': 'py', 'task': 'bisheng.w...
          └ <function fast_trace_task at 0x0000022F492FD480>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 651, in fast_trace_task
    R, I, T, Rstr = tasks[task].__trace__(
                    │     └ 'bisheng.worker.workflow.tasks.continue_workflow'
                    └ {'celery.chord': <@task: celery.chord of bisheng at 0x22f64a08ca0>, 'celery.accumulate': <@task: celery.accumulate of bisheng...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 │    │       └ {}
                 │    └ ['4527f34d6f5e413ba152ea31b1fdf40e', 'ac8b23aed25947908ac042dc5b5f1594', '', '1']
                 └ <@task: bisheng.worker.workflow.tasks.continue_workflow of bisheng at 0x22f64a08ca0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           │    │    │       └ {}
           │    │    └ ('4527f34d6f5e413ba152ea31b1fdf40e', 'ac8b23aed25947908ac042dc5b5f1594', '', '1')
           │    └ <staticmethod(<function continue_workflow at 0x0000022F1F310C10>)>
           └ <@task: bisheng.worker.workflow.tasks.continue_workflow of bisheng at 0x22f64a08ca0>

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\worker\workflow\tasks.py", line 107, in continue_workflow
    _continue_workflow(unique_id, workflow_id, chat_id, user_id)
    │                  │          │            │        └ '1'
    │                  │          │            └ ''
    │                  │          └ 'ac8b23aed25947908ac042dc5b5f1594'
    │                  └ '4527f34d6f5e413ba152ea31b1fdf40e'
    └ <function _continue_workflow at 0x0000022F1F310B80>

> File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\worker\workflow\tasks.py", line 84, in _continue_workflow
    raise Exception('workflow object not found maybe data is expired')

Exception: workflow object not found maybe data is expired
[2025-08-22 15:36:31.591019] [WARNING process-23824-31892 bisheng.worker.workflow.tasks:20] - trace=4527f34d6f5e413ba152ea31b1fdf40e workflow object not found for unique_id: 4527f34d6f5e413ba152ea31b1fdf40e
[2025-08-22 15:38:32.551372] [ERROR process-23824-31892 bisheng.worker.workflow.tasks:98] - trace=95927f4a08eb428a87a23a3b0738eb5d continue_workflow error
Traceback (most recent call last):

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
           │         │     └ {'__name__': '__main__', '__doc__': 'Entry-point for the :program:`celery` umbrella command.', '__package__': 'celery', '__lo...
           │         └ <code object <module> at 0x0000022F46FBB100, file "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pa...
           └ <function _run_code at 0x0000022F46FD9CF0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': 'Entry-point for the :program:`celery` umbrella command.', '__package__': 'celery', '__lo...
         └ <code object <module> at 0x0000022F46FBB100, file "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pa...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\__main__.py", line 19, in <module>
    main()
    └ <function main at 0x0000022F46FDA710>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\__main__.py", line 15, in main
    sys.exit(_main())
    │   │    └ <function main at 0x0000022F4935C550>
    │   └ <built-in function exit>
    └ <module 'sys' (built-in)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
           └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {'auto_envvar_prefix': 'CELERY'}
           │    │     └ ()
           │    └ <function Command.main at 0x0000022F48923C70>
           └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000022F46CFFB50>
         │    └ <function Group.invoke at 0x0000022F4892C670>
         └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1830, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000022F4933B190>
           │               │       │       └ <function Command.invoke at 0x0000022F48923B50>
           │               │       └ <CeleryDaemonCommand worker>
           │               └ <click.core.Context object at 0x0000022F4933B190>
           └ <function Group.invoke.<locals>._process_result at 0x0000022F4935C670>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │   │      │    │           └ <click.core.Context object at 0x0000022F4933B190>
           │   │      │    └ <function worker at 0x0000022F4935D3F0>
           │   │      └ <CeleryDaemonCommand worker>
           │   └ <function Context.invoke at 0x0000022F48922EF0>
           └ <click.core.Context object at 0x0000022F4933B190>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │         └ ()
           └ <function worker at 0x0000022F4935D3F0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\decorators.py", line 34, in new_func
    return f(get_current_context(), *args, **kwargs)
           │ │                       │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │ │                       └ ()
           │ └ <function get_current_context at 0x0000022F4889F1C0>
           └ <function worker at 0x0000022F4935D360>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
           │ │     │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery', 'knowledge_celery', 'celer...
           │ │     └ ()
           │ └ <click.core.Context object at 0x0000022F4933B190>
           └ <function worker at 0x0000022F4935D2D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\worker.py", line 356, in worker
    worker.start()
    │      └ <function WorkController.start at 0x0000022F49292440>
    └ <Worker: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\worker.py", line 203, in start
    self.blueprint.start(self)
    │    │         │     └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │    │         └ <function Blueprint.start at 0x0000022F4927ECB0>
    │    └ <celery.worker.worker.WorkController.Blueprint object at 0x0000022F265886D0>
    └ <Worker: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 116, in start
    step.start(parent)
    │    │     └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │    └ <function StartStopStep.start at 0x0000022F4927FD90>
    └ <step: Consumer>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 365, in start
    return self.obj.start()
           │    │   └ <function Consumer.start at 0x0000022F265E8820>
           │    └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
           └ <step: Consumer>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 341, in start
    blueprint.start(self)
    │         │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │         └ <function Blueprint.start at 0x0000022F4927ECB0>
    └ <celery.worker.consumer.consumer.Consumer.Blueprint object at 0x0000022F265C9180>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 116, in start
    step.start(parent)
    │    │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │    └ <function Evloop.start at 0x0000022F265E9990>
    └ <step: event loop>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 772, in start
    c.loop(*c.loop_args())
    │ │     │ └ <function Consumer.loop_args at 0x0000022F265E8CA0>
    │ │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │ └ <function synloop at 0x0000022F265BFF40>
    └ <Consumer: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\loops.py", line 143, in synloop
    _loop_cycle()
    └ <function synloop.<locals>._loop_cycle at 0x0000022F26C6D120>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\loops.py", line 132, in _loop_cycle
    connection.drain_events(timeout=2.0)
    │          └ <function Connection.drain_events at 0x0000022F48705750>
    └ <Connection: redis://172.20.20.128:6379/2 at 0x22f266906a0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\connection.py", line 341, in drain_events
    return self.transport.drain_events(self.connection, **kwargs)
           │    │                      │    │             └ {'timeout': 2.0}
           │    │                      │    └ <property object at 0x0000022F486F9800>
           │    │                      └ <Connection: redis://172.20.20.128:6379/2 at 0x22f266906a0>
           │    └ <property object at 0x0000022F486F98F0>
           └ <Connection: redis://172.20.20.128:6379/2 at 0x22f266906a0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 997, in drain_events
    get(self._deliver, timeout=timeout)
    │   │    │                 └ 2.0
    │   │    └ <function Transport._deliver at 0x0000022F265B91B0>
    │   └ <kombu.transport.redis.Transport object at 0x0000022F266D7CA0>
    └ <bound method MultiChannelPoller.get of <kombu.transport.redis.MultiChannelPoller object at 0x0000022F266DD8D0>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 598, in get
    ret = self.handle_event(fileno, event)
          │    │            │       └ 1
          │    │            └ 8204
          │    └ <function MultiChannelPoller.handle_event at 0x0000022F265BB2E0>
          └ <kombu.transport.redis.MultiChannelPoller object at 0x0000022F266DD8D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 580, in handle_event
    return self.on_readable(fileno), self
           │    │           │        └ <kombu.transport.redis.MultiChannelPoller object at 0x0000022F266DD8D0>
           │    │           └ 8204
           │    └ <function MultiChannelPoller.on_readable at 0x0000022F265BB250>
           └ <kombu.transport.redis.MultiChannelPoller object at 0x0000022F266DD8D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 576, in on_readable
    chan.handlers[type]()
    │    │        └ 'BRPOP'
    │    └ {'BRPOP': <bound method Channel._brpop_read of <kombu.transport.redis.Channel object at 0x0000022F266DD750>>, 'LISTEN': <boun...
    └ <kombu.transport.redis.Channel object at 0x0000022F266DD750>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 986, in _brpop_read
    self.connection._deliver(loads(bytes_to_str(item)), dest)
    │    │          │        │     │            │       └ 'workflow_celery'
    │    │          │        │     │            └ b'{"body": "W1siOTU5MjdmNGEwOGViNDI4YTg3YTIzYTNiMDczOGViNWQiLCAiYWM4YjIzYWVkMjU5NDc5MDhhYzA0MmRjNWI1ZjE1OTQiLCAiIiwgIjEiXSwge...
    │    │          │        │     └ <function bytes_to_str at 0x0000022F47B8EF80>
    │    │          │        └ <function loads at 0x0000022F48713010>
    │    │          └ <function Transport._deliver at 0x0000022F265B91B0>
    │    └ <kombu.transport.redis.Transport object at 0x0000022F266D7CA0>
    └ <kombu.transport.redis.Channel object at 0x0000022F266DD750>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 1017, in _deliver
    callback(message)
    │        └ {'body': 'W1siOTU5MjdmNGEwOGViNDI4YTg3YTIzYTNiMDczOGViNWQiLCAiYWM4YjIzYWVkMjU5NDc5MDhhYzA0MmRjNWI1ZjE1OTQiLCAiIiwgIjEiXSwge30...
    └ <function Channel.basic_consume.<locals>._callback at 0x0000022F26C6CF70>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 639, in _callback
    return callback(message)
           │        └ <Message object at 0x22f2696f6d0 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': '26e2...
           └ <bound method Consumer._receive_callback of <Consumer: [<Queue workflow_celery -> <Exchange workflow_celery(direct) bound to ...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\messaging.py", line 668, in _receive_callback
    return on_m(message) if on_m else self.receive(decoded, message)
           │    │           │         │    │       │        └ <Message object at 0x22f2696f6d0 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': '26e2...
           │    │           │         │    │       └ None
           │    │           │         │    └ <function Consumer.receive at 0x0000022F48A82170>
           │    │           │         └ <Consumer: [<Queue workflow_celery -> <Exchange workflow_celery(direct) bound to chan:1> -> workflow_celery bound to chan:1>,...
           │    │           └ <function Consumer.create_task_handler.<locals>.on_task_received at 0x0000022F26C6CEE0>
           │    └ <Message object at 0x22f2696f6d0 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': '26e2...
           └ <function Consumer.create_task_handler.<locals>.on_task_received at 0x0000022F26C6CEE0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 688, in on_task_received
    strategy(
    └ <function default.<locals>.task_message_handler at 0x0000022F2667A440>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\strategy.py", line 207, in task_message_handler
    handle(req)
    │      └ <Request: bisheng.worker.workflow.tasks.continue_workflow[926b4239-d9ae-424c-af86-30f54fd4f2fe] ('95927f4a08eb428a87a23a3b073...
    └ <bound method WorkController._process_task of <Worker: celery@WIN-S45T7NVPCSH (running)>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\worker.py", line 226, in _process_task
    req.execute_using_pool(self.pool)
    │   │                  │    └ <celery.concurrency.solo.TaskPool object at 0x0000022F265C8AF0>
    │   │                  └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │   └ <function create_request_cls.<locals>.Request.execute_using_pool at 0x0000022F2667A3B0>
    └ <Request: bisheng.worker.workflow.tasks.continue_workflow[926b4239-d9ae-424c-af86-30f54fd4f2fe] ('95927f4a08eb428a87a23a3b073...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\request.py", line 754, in execute_using_pool
    result = apply_async(
             └ <bound method BasePool.apply_async of <celery.concurrency.solo.TaskPool object at 0x0000022F265C8AF0>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\concurrency\base.py", line 153, in apply_async
    return self.on_apply(target, args, kwargs,
           │    │        │       │     └ {}
           │    │        │       └ ('bisheng.worker.workflow.tasks.continue_workflow', '926b4239-d9ae-424c-af86-30f54fd4f2fe', {'lang': 'py', 'task': 'bisheng.w...
           │    │        └ <function fast_trace_task at 0x0000022F492FD480>
           │    └ <function apply_target at 0x0000022F47B93130>
           └ <celery.concurrency.solo.TaskPool object at 0x0000022F265C8AF0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\concurrency\base.py", line 30, in apply_target
    ret = target(*args, **kwargs)
          │       │       └ {}
          │       └ ('bisheng.worker.workflow.tasks.continue_workflow', '926b4239-d9ae-424c-af86-30f54fd4f2fe', {'lang': 'py', 'task': 'bisheng.w...
          └ <function fast_trace_task at 0x0000022F492FD480>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 651, in fast_trace_task
    R, I, T, Rstr = tasks[task].__trace__(
                    │     └ 'bisheng.worker.workflow.tasks.continue_workflow'
                    └ {'celery.chord': <@task: celery.chord of bisheng at 0x22f64a08ca0>, 'celery.accumulate': <@task: celery.accumulate of bisheng...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 │    │       └ {}
                 │    └ ['95927f4a08eb428a87a23a3b0738eb5d', 'ac8b23aed25947908ac042dc5b5f1594', '', '1']
                 └ <@task: bisheng.worker.workflow.tasks.continue_workflow of bisheng at 0x22f64a08ca0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           │    │    │       └ {}
           │    │    └ ('95927f4a08eb428a87a23a3b0738eb5d', 'ac8b23aed25947908ac042dc5b5f1594', '', '1')
           │    └ <staticmethod(<function continue_workflow at 0x0000022F1F310C10>)>
           └ <@task: bisheng.worker.workflow.tasks.continue_workflow of bisheng at 0x22f64a08ca0>

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\worker\workflow\tasks.py", line 107, in continue_workflow
    _continue_workflow(unique_id, workflow_id, chat_id, user_id)
    │                  │          │            │        └ '1'
    │                  │          │            └ ''
    │                  │          └ 'ac8b23aed25947908ac042dc5b5f1594'
    │                  └ '95927f4a08eb428a87a23a3b0738eb5d'
    └ <function _continue_workflow at 0x0000022F1F310B80>

> File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\worker\workflow\tasks.py", line 84, in _continue_workflow
    raise Exception('workflow object not found maybe data is expired')

Exception: workflow object not found maybe data is expired
[2025-08-22 15:38:32.638795] [WARNING process-23824-31892 bisheng.worker.workflow.tasks:20] - trace=95927f4a08eb428a87a23a3b0738eb5d workflow object not found for unique_id: 95927f4a08eb428a87a23a3b0738eb5d
[2025-08-22 15:41:17.947776] [INFO process-23588-24316 datasets.config:58] - trace=1 PyTorch version 2.8.0 available.
[2025-08-22 15:52:02.304204] [INFO process-37312-40884 datasets.config:58] - trace=1 PyTorch version 2.8.0 available.
[2025-08-22 15:54:18.392158] [INFO process-37312-40884 bisheng.workflow.nodes.start.start:32] - trace=8ab8c8f022044dffa432caedf2811ad8 === StartNode self 内部信息 ===
[2025-08-22 15:54:18.396155] [INFO process-37312-40884 bisheng.workflow.nodes.start.start:34] - trace=8ab8c8f022044dffa432caedf2811ad8 id: start_553b9
[2025-08-22 15:54:18.398194] [INFO process-37312-40884 bisheng.workflow.nodes.start.start:34] - trace=8ab8c8f022044dffa432caedf2811ad8 type: start
[2025-08-22 15:54:18.401090] [INFO process-37312-40884 bisheng.workflow.nodes.start.start:34] - trace=8ab8c8f022044dffa432caedf2811ad8 name: 开始
[2025-08-22 15:54:18.404108] [INFO process-37312-40884 bisheng.workflow.nodes.start.start:34] - trace=8ab8c8f022044dffa432caedf2811ad8 description: 工作流运行的起始节点。
[2025-08-22 15:54:18.407746] [INFO process-37312-40884 bisheng.workflow.nodes.start.start:34] - trace=8ab8c8f022044dffa432caedf2811ad8 target_edges: [EdgeBase(id='xy-edge__start_553b9right_handle-input_2775bleft_handle', source='start_553b9', sourceHandle='right_handle', sourceType='', target='input_2775b', targetHandle='left_handle', targetType='')]
[2025-08-22 15:54:18.412757] [INFO process-37312-40884 bisheng.workflow.nodes.start.start:34] - trace=8ab8c8f022044dffa432caedf2811ad8 user_id: 1
[2025-08-22 15:54:18.415756] [INFO process-37312-40884 bisheng.workflow.nodes.start.start:34] - trace=8ab8c8f022044dffa432caedf2811ad8 workflow_id: ac8b23aed25947908ac042dc5b5f1594
[2025-08-22 15:54:18.419798] [INFO process-37312-40884 bisheng.workflow.nodes.start.start:34] - trace=8ab8c8f022044dffa432caedf2811ad8 graph_state: history_memory=ConversationBufferWindowMemory(chat_memory=InMemoryChatMessageHistory(messages=[]), k=10) variables_pool={}
[2025-08-22 15:54:18.423321] [INFO process-37312-40884 bisheng.workflow.nodes.start.start:34] - trace=8ab8c8f022044dffa432caedf2811ad8 node_data: id='start_553b9' type='start' name='开始' description='工作流运行的起始节点。' group_params=[NodeGroupParams(name='开场引导', params=[NodeParams(key='guide_word', label='开场白', value='您好，请问想聊些什么呢？', type='textarea', help='', tab='', placeholder='每次工作流开始执行时向用户发送此消息，支持 Markdown 格式，为空时不发送。', required=False, options=None), NodeParams(key='guide_question', label='引导问题', value=['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], type='input_list', help='为用户提供推荐问题，引导用户输入，超过3个时将随机选取3个。', tab='', placeholder='请输入引导问题', required=False, options=None)], description=''), NodeGroupParams(name='全局变量', params=[NodeParams(key='current_time', label='当前时间', value='', type='var', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='chat_history', label='', value=10, type='chat_history_num', help='', tab='', placeholder='', required=False, options=None), NodeParams(key='preset_question', label='预置问题列表', value=[{'key': 'f4cbae', 'value': ''}], type='input_list', help='适合文档审核、报告生成等场景，利用提前预置的问题批量进行 RAG 问答。', tab='', placeholder='输入批量预置问题', required=False, options=None)], description='')] tab={} tool_key='' v=1
[2025-08-22 15:54:18.435547] [INFO process-37312-40884 bisheng.workflow.nodes.start.start:34] - trace=8ab8c8f022044dffa432caedf2811ad8 node_params: {'guide_word': '您好，请问想聊些什么呢？', 'guide_question': ['今天贵州茅台股价情况？', '最近关于低空经济的政策', ''], 'current_time': '2025-08-22 15:54:18', 'chat_history': 10, 'preset_question': [{'key': 'f4cbae', 'value': ''}]}
[2025-08-22 15:54:18.439547] [INFO process-37312-40884 bisheng.workflow.nodes.start.start:34] - trace=8ab8c8f022044dffa432caedf2811ad8 other_node_variable: {}
[2025-08-22 15:54:18.442547] [INFO process-37312-40884 bisheng.workflow.nodes.start.start:34] - trace=8ab8c8f022044dffa432caedf2811ad8 current_step: 0
[2025-08-22 15:54:18.446551] [INFO process-37312-40884 bisheng.workflow.nodes.start.start:34] - trace=8ab8c8f022044dffa432caedf2811ad8 max_steps: 50
[2025-08-22 15:54:18.450536] [INFO process-37312-40884 bisheng.workflow.nodes.start.start:34] - trace=8ab8c8f022044dffa432caedf2811ad8 callback_manager: <bisheng.worker.workflow.redis_callback.RedisCallback object at 0x0000026466F0F910>
[2025-08-22 15:54:18.453546] [INFO process-37312-40884 bisheng.workflow.nodes.start.start:34] - trace=8ab8c8f022044dffa432caedf2811ad8 tmp_collection_name: tmp_workflow_data
[2025-08-22 15:54:18.457790] [INFO process-37312-40884 bisheng.workflow.nodes.start.start:34] - trace=8ab8c8f022044dffa432caedf2811ad8 stop_flag: False
[2025-08-22 15:54:18.460894] [INFO process-37312-40884 bisheng.workflow.nodes.start.start:34] - trace=8ab8c8f022044dffa432caedf2811ad8 exec_unique_id: 15b6bd3eaa8d4b1791d675e667fb9ba9
[2025-08-22 15:54:18.464801] [INFO process-37312-40884 bisheng.workflow.nodes.start.start:35] - trace=8ab8c8f022044dffa432caedf2811ad8 === StartNode self 信息结束 ===
[2025-08-22 15:54:37.300265] [INFO process-37312-40884 bisheng.workflow.callback.llm_callback:35] - trace=8ab8c8f022044dffa432caedf2811ad8 on_llm_new_token True outkey=output
[2025-08-22 15:56:12.355420] [ERROR process-37312-40884 bisheng.worker.workflow.tasks:98] - trace=a55caf95f8974dec845522aec6396ce6 continue_workflow error
Traceback (most recent call last):

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
           │         │     └ {'__name__': '__main__', '__doc__': 'Entry-point for the :program:`celery` umbrella command.', '__package__': 'celery', '__lo...
           │         └ <code object <module> at 0x0000026407CBB100, file "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pa...
           └ <function _run_code at 0x0000026407CD9CF0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
         │     └ {'__name__': '__main__', '__doc__': 'Entry-point for the :program:`celery` umbrella command.', '__package__': 'celery', '__lo...
         └ <code object <module> at 0x0000026407CBB100, file "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pa...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\__main__.py", line 19, in <module>
    main()
    └ <function main at 0x0000026407CDA710>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\__main__.py", line 15, in main
    sys.exit(_main())
    │   │    └ <function main at 0x000002640A0A0550>
    │   └ <built-in function exit>
    └ <module 'sys' (built-in)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\celery.py", line 231, in main
    return celery(auto_envvar_prefix="CELERY")
           └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {'auto_envvar_prefix': 'CELERY'}
           │    │     └ ()
           │    └ <function Command.main at 0x0000026409663C70>
           └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1363, in main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x00000264079FFB50>
         │    └ <function Group.invoke at 0x000002640966C670>
         └ <DYMGroup celery>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1830, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x000002640A07B190>
           │               │       │       └ <function Command.invoke at 0x0000026409663B50>
           │               │       └ <CeleryDaemonCommand worker>
           │               └ <click.core.Context object at 0x000002640A07B190>
           └ <function Group.invoke.<locals>._process_result at 0x000002640A0A0670>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery'], 'hostname': 'celery@WIN-S...
           │   │      │    │           └ <click.core.Context object at 0x000002640A07B190>
           │   │      │    └ <function worker at 0x000002640A0A13F0>
           │   │      └ <CeleryDaemonCommand worker>
           │   └ <function Context.invoke at 0x0000026409662EF0>
           └ <click.core.Context object at 0x000002640A07B190>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\core.py", line 794, in invoke
    return callback(*args, **kwargs)
           │         │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery'], 'hostname': 'celery@WIN-S...
           │         └ ()
           └ <function worker at 0x000002640A0A13F0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\click\decorators.py", line 34, in new_func
    return f(get_current_context(), *args, **kwargs)
           │ │                       │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery'], 'hostname': 'celery@WIN-S...
           │ │                       └ ()
           │ └ <function get_current_context at 0x00000264095DF1C0>
           └ <function worker at 0x000002640A0A1360>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\base.py", line 135, in caller
    return f(ctx, *args, **kwargs)
           │ │     │       └ {'loglevel': 20, 'pool': <class 'celery.concurrency.solo.TaskPool'>, 'queues': ['workflow_celery'], 'hostname': 'celery@WIN-S...
           │ │     └ ()
           │ └ <click.core.Context object at 0x000002640A07B190>
           └ <function worker at 0x000002640A0A12D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bin\worker.py", line 356, in worker
    worker.start()
    │      └ <function WorkController.start at 0x0000026409FD2440>
    └ <Worker: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\worker.py", line 203, in start
    self.blueprint.start(self)
    │    │         │     └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │    │         └ <function Blueprint.start at 0x0000026409FBECB0>
    │    └ <celery.worker.worker.WorkController.Blueprint object at 0x0000026466DA1990>
    └ <Worker: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 116, in start
    step.start(parent)
    │    │     └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │    └ <function StartStopStep.start at 0x0000026409FBFD90>
    └ <step: Consumer>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 365, in start
    return self.obj.start()
           │    │   └ <function Consumer.start at 0x0000026466E1C790>
           │    └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
           └ <step: Consumer>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 341, in start
    blueprint.start(self)
    │         │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │         └ <function Blueprint.start at 0x0000026409FBECB0>
    └ <celery.worker.consumer.consumer.Consumer.Blueprint object at 0x0000026466DE2440>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\bootsteps.py", line 116, in start
    step.start(parent)
    │    │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │    └ <function Evloop.start at 0x0000026466E1D900>
    └ <step: event loop>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 772, in start
    c.loop(*c.loop_args())
    │ │     │ └ <function Consumer.loop_args at 0x0000026466E1CC10>
    │ │     └ <Consumer: celery@WIN-S45T7NVPCSH (running)>
    │ └ <function synloop at 0x0000026466DF7EB0>
    └ <Consumer: celery@WIN-S45T7NVPCSH (running)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\loops.py", line 143, in synloop
    _loop_cycle()
    └ <function synloop.<locals>._loop_cycle at 0x0000026466F24160>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\loops.py", line 132, in _loop_cycle
    connection.drain_events(timeout=2.0)
    │          └ <function Connection.drain_events at 0x0000026409445750>
    └ <Connection: redis://172.20.20.128:6379/2 at 0x26466eb0760>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\connection.py", line 341, in drain_events
    return self.transport.drain_events(self.connection, **kwargs)
           │    │                      │    │             └ {'timeout': 2.0}
           │    │                      │    └ <property object at 0x0000026409439940>
           │    │                      └ <Connection: redis://172.20.20.128:6379/2 at 0x26466eb0760>
           │    └ <property object at 0x0000026409439A30>
           └ <Connection: redis://172.20.20.128:6379/2 at 0x26466eb0760>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 997, in drain_events
    get(self._deliver, timeout=timeout)
    │   │    │                 └ 2.0
    │   │    └ <function Transport._deliver at 0x0000026466DE51B0>
    │   └ <kombu.transport.redis.Transport object at 0x0000026466EB0F40>
    └ <bound method MultiChannelPoller.get of <kombu.transport.redis.MultiChannelPoller object at 0x0000026466EB1030>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 598, in get
    ret = self.handle_event(fileno, event)
          │    │            │       └ 1
          │    │            └ 8012
          │    └ <function MultiChannelPoller.handle_event at 0x0000026466DE72E0>
          └ <kombu.transport.redis.MultiChannelPoller object at 0x0000026466EB1030>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 580, in handle_event
    return self.on_readable(fileno), self
           │    │           │        └ <kombu.transport.redis.MultiChannelPoller object at 0x0000026466EB1030>
           │    │           └ 8012
           │    └ <function MultiChannelPoller.on_readable at 0x0000026466DE7250>
           └ <kombu.transport.redis.MultiChannelPoller object at 0x0000026466EB1030>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 576, in on_readable
    chan.handlers[type]()
    │    │        └ 'BRPOP'
    │    └ {'BRPOP': <bound method Channel._brpop_read of <kombu.transport.redis.Channel object at 0x0000026466EB0FD0>>, 'LISTEN': <boun...
    └ <kombu.transport.redis.Channel object at 0x0000026466EB0FD0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\redis.py", line 986, in _brpop_read
    self.connection._deliver(loads(bytes_to_str(item)), dest)
    │    │          │        │     │            │       └ 'workflow_celery'
    │    │          │        │     │            └ b'{"body": "W1siYTU1Y2FmOTVmODk3NGRlYzg0NTUyMmFlYzYzOTZjZTYiLCAiYjk5MGE5NjMyODJkNGViMTk3MjQxMDgzZjFiNTY1NzQiLCAiIiwgIjIiXSwge...
    │    │          │        │     └ <function bytes_to_str at 0x00000264088CEF80>
    │    │          │        └ <function loads at 0x0000026409453010>
    │    │          └ <function Transport._deliver at 0x0000026466DE51B0>
    │    └ <kombu.transport.redis.Transport object at 0x0000026466EB0F40>
    └ <kombu.transport.redis.Channel object at 0x0000026466EB0FD0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 1017, in _deliver
    callback(message)
    │        └ {'body': 'W1siYTU1Y2FmOTVmODk3NGRlYzg0NTUyMmFlYzYzOTZjZTYiLCAiYjk5MGE5NjMyODJkNGViMTk3MjQxMDgzZjFiNTY1NzQiLCAiIiwgIjIiXSwge30...
    └ <function Channel.basic_consume.<locals>._callback at 0x0000026466F240D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\transport\virtual\base.py", line 639, in _callback
    return callback(message)
           │        └ <Message object at 0x26466e944c0 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': 'bb28...
           └ <bound method Consumer._receive_callback of <Consumer: [<Queue workflow_celery -> <Exchange workflow_celery(direct) bound to ...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\kombu\messaging.py", line 668, in _receive_callback
    return on_m(message) if on_m else self.receive(decoded, message)
           │    │           │         │    │       │        └ <Message object at 0x26466e944c0 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': 'bb28...
           │    │           │         │    │       └ None
           │    │           │         │    └ <function Consumer.receive at 0x00000264097C2170>
           │    │           │         └ <Consumer: [<Queue workflow_celery -> <Exchange workflow_celery(direct) bound to chan:1> -> workflow_celery bound to chan:1>]>
           │    │           └ <function Consumer.create_task_handler.<locals>.on_task_received at 0x0000026466F24040>
           │    └ <Message object at 0x26466e944c0 with details {'state': 'RECEIVED', 'content_type': 'application/json', 'delivery_tag': 'bb28...
           └ <function Consumer.create_task_handler.<locals>.on_task_received at 0x0000026466F24040>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\consumer\consumer.py", line 688, in on_task_received
    strategy(
    └ <function default.<locals>.task_message_handler at 0x0000026466EC24D0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\strategy.py", line 207, in task_message_handler
    handle(req)
    │      └ <Request: bisheng.worker.workflow.tasks.continue_workflow[252734da-c953-420a-9d14-8ae4b4c8da99] ('a55caf95f8974dec845522aec63...
    └ <bound method WorkController._process_task of <Worker: celery@WIN-S45T7NVPCSH (running)>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\worker.py", line 226, in _process_task
    req.execute_using_pool(self.pool)
    │   │                  │    └ <celery.concurrency.solo.TaskPool object at 0x0000026466DE1DB0>
    │   │                  └ <Worker: celery@WIN-S45T7NVPCSH (running)>
    │   └ <function create_request_cls.<locals>.Request.execute_using_pool at 0x0000026466EC2560>
    └ <Request: bisheng.worker.workflow.tasks.continue_workflow[252734da-c953-420a-9d14-8ae4b4c8da99] ('a55caf95f8974dec845522aec63...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\worker\request.py", line 754, in execute_using_pool
    result = apply_async(
             └ <bound method BasePool.apply_async of <celery.concurrency.solo.TaskPool object at 0x0000026466DE1DB0>>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\concurrency\base.py", line 153, in apply_async
    return self.on_apply(target, args, kwargs,
           │    │        │       │     └ {}
           │    │        │       └ ('bisheng.worker.workflow.tasks.continue_workflow', '252734da-c953-420a-9d14-8ae4b4c8da99', {'lang': 'py', 'task': 'bisheng.w...
           │    │        └ <function fast_trace_task at 0x000002640A03D480>
           │    └ <function apply_target at 0x00000264088D3130>
           └ <celery.concurrency.solo.TaskPool object at 0x0000026466DE1DB0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\concurrency\base.py", line 30, in apply_target
    ret = target(*args, **kwargs)
          │       │       └ {}
          │       └ ('bisheng.worker.workflow.tasks.continue_workflow', '252734da-c953-420a-9d14-8ae4b4c8da99', {'lang': 'py', 'task': 'bisheng.w...
          └ <function fast_trace_task at 0x000002640A03D480>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 651, in fast_trace_task
    R, I, T, Rstr = tasks[task].__trace__(
                    │     └ 'bisheng.worker.workflow.tasks.continue_workflow'
                    └ {'celery.chord': <@task: celery.chord of bisheng at 0x26425748cd0>, 'celery.accumulate': <@task: celery.accumulate of bisheng...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 453, in trace_task
    R = retval = fun(*args, **kwargs)
                 │    │       └ {}
                 │    └ ['a55caf95f8974dec845522aec6396ce6', 'b990a963282d4eb197241083f1b56574', '', '2']
                 └ <@task: bisheng.worker.workflow.tasks.continue_workflow of bisheng at 0x26425748cd0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\celery\app\trace.py", line 736, in __protected_call__
    return self.run(*args, **kwargs)
           │    │    │       └ {}
           │    │    └ ('a55caf95f8974dec845522aec6396ce6', 'b990a963282d4eb197241083f1b56574', '', '2')
           │    └ <staticmethod(<function continue_workflow at 0x000002645FB14C10>)>
           └ <@task: bisheng.worker.workflow.tasks.continue_workflow of bisheng at 0x26425748cd0>

  File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\worker\workflow\tasks.py", line 107, in continue_workflow
    _continue_workflow(unique_id, workflow_id, chat_id, user_id)
    │                  │          │            │        └ '2'
    │                  │          │            └ ''
    │                  │          └ 'b990a963282d4eb197241083f1b56574'
    │                  └ 'a55caf95f8974dec845522aec6396ce6'
    └ <function _continue_workflow at 0x000002645FB14B80>

> File "D:\projects\postwise-v2.0\postwise\src\backend\bisheng\worker\workflow\tasks.py", line 84, in _continue_workflow
    raise Exception('workflow object not found maybe data is expired')

Exception: workflow object not found maybe data is expired
[2025-08-22 15:56:12.450412] [WARNING process-37312-40884 bisheng.worker.workflow.tasks:20] - trace=a55caf95f8974dec845522aec6396ce6 workflow object not found for unique_id: a55caf95f8974dec845522aec6396ce6
[2025-08-22 16:10:03.009213] [INFO process-32328-42292 datasets.config:58] - trace=1 PyTorch version 2.8.0 available.
[2025-08-22 16:28:45.979117] [INFO process-32800-41340 datasets.config:58] - trace=1 PyTorch version 2.8.0 available.
