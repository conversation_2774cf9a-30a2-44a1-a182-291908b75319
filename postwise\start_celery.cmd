@echo off
echo Starting BISHENG Celery Worker...
echo Working directory: %cd%
echo Target directory: src\backend

if not exist "src\backend" (
    echo Error: src\backend directory not found!
    echo Please run this script from the postwise directory.
    pause
    exit /b 1
)

cd src\backend

echo Current directory: %cd%
echo Running: C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe -m celery -A bisheng.worker.main worker -l info --pool=solo -Q workflow_celery,knowledge_celery,celery
echo.
echo Queue Configuration:
echo   - workflow_celery: 工作流执行任务
echo   - knowledge_celery: 知识库处理任务
echo   - celery: 默认队列任务
echo.
echo Press Ctrl+C to stop the worker
echo ----------------------------------------

C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe -m celery -A bisheng.worker.main worker -l info --pool=solo -Q workflow_celery,knowledge_celery,celery

pause
